{% extends "base.html" %}

{% block title %}Extraction Étapes JSON - Enrichissement Tests Jira Xray{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h3 class="card-title mb-0">
                    <i class="fas fa-code"></i> Extraction des Étapes de Test JSON
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Étape 2 :</strong> Extraction des colonnes Action, Data, Expected Result depuis le champ JSON "Custom field (Manual Test Steps)"
                </div>
                
                <!-- Informations sur le fichier -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-body">
                                <h6 class="card-title">📁 Fichier à traiter</h6>
                                <p class="card-text">
                                    <strong>Nom :</strong> {{ filename }}<br>
                                    <strong>ID :</strong> {{ file_id }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-body">
                                <h6 class="card-title">🎯 Colonnes à extraire</h6>
                                <p class="card-text">
                                    <span class="badge bg-primary me-1">Action</span>
                                    <span class="badge bg-success me-1">Data</span>
                                    <span class="badge bg-warning me-1">Expected Result</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Aperçu de l'extraction -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-eye"></i> Aperçu de l'Extraction JSON
                        </h5>
                    </div>
                    <div class="card-body">
                        <button id="btn-preview" class="btn btn-outline-info mb-3" onclick="loadPreview()">
                            <i class="fas fa-search"></i> Charger l'Aperçu
                        </button>
                        
                        <div id="preview-container" style="display: none;">
                            <div id="preview-content"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Format JSON attendu -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-code"></i> Format JSON Attendu
                        </h5>
                    </div>
                    <div class="card-body">
                        <h6>📋 Format Liste avec Fields :</h6>
                        <pre class="bg-light p-3 rounded"><code>[{
  "id": 3763673,
  "fields": {
    "Action": "change HLR/EPC HSS profile...",
    "Data": "",
    "Expected Result": "Same data in SDM107 and SDM207"
  }
}]</code></pre>
                        
                        <h6 class="mt-3">📋 Format Objet Direct :</h6>
                        <pre class="bg-light p-3 rounded"><code>{
  "Action": "Test authentication",
  "Data": "User credentials", 
  "Expected Result": "Authentication successful"
}</code></pre>
                        
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Note :</strong> Si le JSON est malformé, l'outil tentera une extraction par expressions régulières.
                        </div>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="text-center">
                    <button id="btn-extract" class="btn btn-success btn-lg me-2" onclick="extractJSONSteps()">
                        <i class="fas fa-magic"></i> Extraire les Étapes JSON
                    </button>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
                
                <!-- Résultats -->
                <div id="results-container" style="display: none;" class="mt-4">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-check-circle"></i> Extraction Terminée
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="results-content"></div>
                            
                            <div class="text-center mt-3">
                                <a id="download-with-steps" href="#" class="btn btn-success btn-lg me-2">
                                    <i class="fas fa-download"></i> Télécharger avec Étapes
                                </a>
                                <a id="continue-enrichment" href="#" class="btn btn-primary btn-lg">
                                    <i class="fas fa-arrow-right"></i> Continuer l'Enrichissement
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Charger l'aperçu de l'extraction JSON
async function loadPreview() {
    const button = document.getElementById('btn-preview');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Chargement...';
    button.disabled = true;
    
    try {
        const response = await fetch(`/preview_json_steps/{{ file_id }}`, {
            method: 'GET'
        });
        
        const result = await response.json();
        
        if (result.success) {
            displayPreview(result.samples);
            document.getElementById('preview-container').style.display = 'block';
        } else {
            showAlert('warning', result.message);
        }
    } catch (error) {
        showAlert('danger', 'Erreur de communication avec le serveur');
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Afficher l'aperçu
function displayPreview(samples) {
    const container = document.getElementById('preview-content');
    
    if (samples.length === 0) {
        container.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                Aucune donnée JSON trouvée dans le fichier.
            </div>
        `;
        return;
    }
    
    let html = '<h6>📋 Aperçu des Extractions :</h6>';
    
    samples.forEach((sample, index) => {
        const hasData = sample.extracted.Action || sample.extracted.Data || sample.extracted['Expected Result'];
        const statusClass = hasData ? 'success' : 'warning';
        const statusIcon = hasData ? 'check-circle' : 'exclamation-triangle';
        
        html += `
            <div class="card mb-2 border-${statusClass}">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-${statusIcon} text-${statusClass}"></i>
                        Ligne ${sample.row} - ${sample.summary}
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">JSON Source :</small>
                            <pre class="small bg-light p-2 rounded">${sample.json_preview}</pre>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">Extraction :</small>
                            <ul class="small">
                                <li><strong>Action:</strong> ${sample.extracted.Action || '<em>vide</em>'}</li>
                                <li><strong>Data:</strong> ${sample.extracted.Data || '<em>vide</em>'}</li>
                                <li><strong>Expected Result:</strong> ${sample.extracted['Expected Result'] || '<em>vide</em>'}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Extraire les étapes JSON
async function extractJSONSteps() {
    const button = document.getElementById('btn-extract');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Extraction en cours...';
    button.disabled = true;
    
    try {
        const response = await fetch(`/extract_json_steps/{{ file_id }}`, {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.success) {
            displayResults(result);
            document.getElementById('results-container').style.display = 'block';
            
            // Configurer les liens
            document.getElementById('download-with-steps').href = `/download/${result.output_file}`;
            document.getElementById('continue-enrichment').href = `/enrich/{{ file_id }}?source=with_steps`;
            
            showAlert('success', result.message);
        } else {
            showAlert('danger', result.message);
        }
    } catch (error) {
        showAlert('danger', 'Erreur de communication avec le serveur');
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Afficher les résultats
function displayResults(result) {
    const container = document.getElementById('results-content');
    
    container.innerHTML = `
        <div class="row text-center">
            <div class="col-md-4">
                <h4 class="text-primary">${result.stats.processed}</h4>
                <small>Tests Traités</small>
            </div>
            <div class="col-md-4">
                <h4 class="text-success">${result.stats.extracted}</h4>
                <small>Étapes Extraites</small>
            </div>
            <div class="col-md-4">
                <h4 class="text-${result.stats.errors > 0 ? 'warning' : 'muted'}">${result.stats.errors}</h4>
                <small>Erreurs</small>
            </div>
        </div>
        
        <div class="mt-3">
            <p class="text-muted">
                <i class="fas fa-info-circle"></i>
                Le fichier avec les colonnes Action, Data, Expected Result a été généré.
                Vous pouvez maintenant continuer avec l'enrichissement des labels.
            </p>
        </div>
    `;
}

// Fonction pour afficher des alertes
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'exclamation-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
