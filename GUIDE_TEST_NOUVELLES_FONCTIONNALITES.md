# 🧪 GUIDE DE TEST - NOUVELLES FONCTIONNALITÉS

## ⚠️ IMPORTANT : Workflow Modifié

**Les nouvelles fonctionnalités ne sont visibles que si vous suivez le nouveau workflow !**

## 🎯 Nouveau Workflow à Tester

### 1️⃣ **Enrichissement avec Format Jira Optimisé**

1. **Lancer l'application** :
   ```bash
   python run_app.py
   ```

2. **Télécharger le fichier** :
   - Aller sur http://127.0.0.1:5000
   - Télécharger `example_tests.csv`

3. **CRUCIAL - Choisir le bon format** :
   - Sur la page de validation, vous verrez **2 options** :
     - ❌ **Format Standard** (ancien - labels combinés)
     - ✅ **Format Jira Optimisé** (nouveau - colonnes séparées)
   - **Cliquer sur "Enrichir (Jira)"** ← IMPORTANT !

4. **Vérifier l'enrichissement** :
   - Le fichier enrichi aura **8 colonnes "Labels"**
   - Chaque label sera dans une colonne séparée
   - Plus de labels ajoutés automatiquement

### 2️⃣ **Analyse des Test Sets (Nouveau)**

1. **Après l'enrichissement** :
   - Cliquer sur **"Analyser Test Sets"** (bouton jaune)
   - OU aller dans Historique → cliquer sur l'icône d'analyse

2. **Page d'analyse** :
   - **Étape 1** : Cliquer "Démarrer l'Analyse"
   - **Étape 2** : Cliquer "Corriger Automatiquement" 
   - **Étape 3** : Cliquer "Générer Dashboard"

3. **Résultats attendus** :
   - Tests mal classés détectés
   - Labels manquants identifiés
   - Corrections appliquées automatiquement
   - Score de qualité affiché

## 🔍 Différences Visibles

### **AVANT (Format Standard)** :
```csv
Summary,Labels,Test Set
CFU Test,"Functional, System, Manual, Regression, 2G, MAP",HLR Call Forwarding Services
```

### **APRÈS (Format Jira + Analyse)** :
```csv
Summary,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Test Set
CFU Test,Functional,System,Manual,Regression,2G,MAP,CallForwarding,3GPP_TS_23.082,HLR Call Forwarding Services
```

## 📊 Nouvelles Fonctionnalités à Tester

### ✅ **Colonnes Labels Séparées**
- **8 colonnes** avec le même nom "Labels"
- **Un seul label par colonne**
- **Format parfait pour Jira Xray**

### ✅ **Labels Techniques Ajoutés**
- **2G/4G** selon le domaine
- **MAP/Diameter** selon l'interface
- **Références 3GPP** (TS 23.082, etc.)

### ✅ **Test Sets Corrigés**
- **Réaffectation automatique** des tests mal classés
- **Cohérence** avec les standards 3GPP

### ✅ **Analyse de Qualité**
- **Score de conformité** global
- **Métriques détaillées** par critère
- **Tableau de bord HTML** interactif

## 🚨 Erreurs Communes

### ❌ **Utiliser l'ancien format**
- Si vous cliquez "Enrichir (Standard)", vous n'aurez pas les colonnes séparées
- Les nouvelles fonctionnalités d'analyse ne seront pas visibles

### ❌ **Ne pas utiliser l'analyse**
- L'enrichissement seul ne montre que les améliorations de base
- Il faut utiliser "Analyser Test Sets" pour voir toutes les corrections

### ❌ **Regarder le mauvais fichier**
- `enriched_xxx.csv` = enrichissement de base
- `corrected_xxx.csv` = avec toutes les corrections appliquées

## 🎯 Test Rapide de Validation

### **Commande de test** :
```bash
python test_format_fix.py
```

### **Résultats attendus** :
```
✅ Colonnes Labels créées : 8
✅ Tests analysés : 27
✅ Tests mal classés : 4
✅ Labels ajoutés : 111
✅ Références 3GPP ajoutées : 13
```

## 📁 Fichiers à Vérifier

### **Après enrichissement Jira** :
- `enriched_xxx.csv` → 8 colonnes Labels

### **Après analyse et correction** :
- `corrected_xxx.csv` → Labels supplémentaires + Test Sets corrigés
- `dashboard_xxx.html` → Tableau de bord qualité

## 💡 Si Ça Ne Marche Toujours Pas

1. **Vérifier la version** :
   ```bash
   python -c "from test_enrichment_tool_v2 import TestEnrichmentToolV2; print('V2 OK')"
   ```

2. **Tester manuellement** :
   ```bash
   python test_format_fix.py
   ```

3. **Vérifier les fichiers** :
   - `example_tests_corrected.csv` doit avoir 8 colonnes Labels
   - Chaque test doit avoir plus de labels qu'avant

4. **Relancer l'application** :
   ```bash
   python run_app.py
   ```

## 🎊 Validation Réussie

**Vous saurez que ça marche quand** :
- ✅ 8 colonnes "Labels" dans le CSV
- ✅ Chaque label dans une colonne séparée  
- ✅ Labels techniques ajoutés (2G, MAP, Diameter, etc.)
- ✅ Références 3GPP ajoutées (3GPP_TS_23.082, etc.)
- ✅ Test Sets corrigés selon standards
- ✅ Score de qualité > 90%

**Le workflow complet enrichissement + analyse fonctionne parfaitement !** 🎉
