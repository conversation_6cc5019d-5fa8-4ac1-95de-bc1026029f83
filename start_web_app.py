#!/usr/bin/env python3
"""
Script de lancement de l'application web d'enrichissement
"""

import os
import sys
import webbrowser
import time
import threading

def check_dependencies():
    """Vérifie que toutes les dépendances sont installées"""
    try:
        import flask
        import pandas
        import openpyxl
        return True, "Toutes les dépendances sont installées"
    except ImportError as e:
        return False, f"Dépendance manquante: {e}"

def check_files():
    """Vérifie que tous les fichiers nécessaires sont présents"""
    required_files = [
        'app.py',
        'test_enrichment_tool.py', 
        'create_excel_output.py',
        'templates/base.html',
        'templates/index.html',
        'templates/validate.html',
        'templates/enrich.html',
        'templates/rules.html',
        'templates/help.html'
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        return False, f"Fichiers manquants: {missing_files}"
    else:
        return True, "Tous les fichiers sont présents"

def create_directories():
    """Crée les dossiers nécessaires"""
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('processed', exist_ok=True)
    os.makedirs('templates', exist_ok=True)

def open_browser_delayed():
    """Ouvre le navigateur après un délai"""
    time.sleep(2)  # Attendre que le serveur soit prêt
    webbrowser.open('http://127.0.0.1:5000')

def main():
    print("🚀 LANCEMENT DE L'APPLICATION WEB D'ENRICHISSEMENT")
    print("=" * 55)
    
    # Vérification des dépendances
    print("\n🔍 Vérification des prérequis...")
    deps_ok, deps_msg = check_dependencies()
    if not deps_ok:
        print(f"❌ {deps_msg}")
        print("\n💡 Pour installer les dépendances :")
        print("   pip install flask pandas openpyxl")
        return
    print(f"✅ {deps_msg}")
    
    # Vérification des fichiers
    files_ok, files_msg = check_files()
    if not files_ok:
        print(f"❌ {files_msg}")
        return
    print(f"✅ {files_msg}")
    
    # Création des dossiers
    create_directories()
    print("✅ Dossiers créés/vérifiés")
    
    print("\n🌐 INFORMATIONS DE L'APPLICATION")
    print("-" * 35)
    print("📱 URL d'accès    : http://127.0.0.1:5000")
    print("🔧 Mode debug     : Activé")
    print("🛑 Arrêt          : Ctrl+C")
    print("📁 Uploads        : ./uploads/")
    print("📁 Résultats      : ./processed/")
    
    print("\n🎯 FONCTIONNALITÉS DISPONIBLES")
    print("-" * 35)
    print("✅ Téléchargement par glisser-déposer")
    print("✅ Validation automatique du format CSV")
    print("✅ Enrichissement avec suivi en temps réel")
    print("✅ Téléchargement CSV et Excel")
    print("✅ Documentation des règles intégrée")
    print("✅ Guide d'aide complet")
    
    print("\n📋 FICHIER D'EXEMPLE DISPONIBLE")
    print("-" * 35)
    if os.path.exists('example_tests.csv'):
        print("✅ example_tests.csv - 27 tests d'exemple")
        print("   Utilisez ce fichier pour tester l'application")
    else:
        print("❌ example_tests.csv non trouvé")
        print("   Créez un fichier CSV avec au minimum une colonne 'Summary'")
    
    # Demander confirmation
    print("\n" + "=" * 55)
    response = input("❓ Lancer l'application web maintenant ? (o/n): ")
    
    if response.lower() not in ['o', 'oui', 'y', 'yes']:
        print("👋 Lancement annulé")
        return
    
    print("\n🚀 Lancement de l'application...")
    print("⏳ Démarrage du serveur Flask...")
    
    # Ouvrir le navigateur automatiquement après un délai
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # Importer et lancer l'application
        from app import app
        print("✅ Application Flask chargée")
        print("🌐 Serveur démarré sur http://127.0.0.1:5000")
        print("🔄 Ouverture automatique du navigateur...")
        print("\n" + "=" * 55)
        print("🎉 APPLICATION PRÊTE ! Utilisez l'interface web.")
        print("🛑 Pour arrêter : Ctrl+C")
        print("=" * 55)
        
        # Lancer l'application
        app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
        
    except KeyboardInterrupt:
        print("\n\n👋 Application arrêtée par l'utilisateur")
        print("💾 Vos fichiers traités sont conservés dans ./processed/")
        
    except Exception as e:
        print(f"\n❌ Erreur lors du lancement: {e}")
        print("\n🔧 Solutions possibles :")
        print("   1. Vérifiez que le port 5000 n'est pas utilisé")
        print("   2. Relancez avec des privilèges administrateur")
        print("   3. Vérifiez votre pare-feu")

if __name__ == "__main__":
    main()
