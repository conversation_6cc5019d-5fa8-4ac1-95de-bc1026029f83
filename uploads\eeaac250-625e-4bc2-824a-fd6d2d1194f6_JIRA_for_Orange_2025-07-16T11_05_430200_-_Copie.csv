Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project description,Project url,Priority,Resolution,Assignee,Reporter,Creator,Created,Updated,Last Viewed,Resolved,Affects Version/s,Fix Version/s,Component/s,Due Date,Votes,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Description,Environment,Watchers,Log Work,Original Estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Attachment,Custom field (Automation status),Custom field (Branch),Custom field (Business Gain),Custom field (CDR/MC),Custom field (Category List),Custom field (Change completion date),Custom field (Change start date),Custom field (Change type),Custom field (Cost),Custom field (Cucumber Scenario),Custom field (Cucumber Test Type),Customer Request Type,Custom field (Dataset values),Custom field (Dataset values),Custom field (Date MEP),Custom field (Date UAT),Demande SLA 16H,Demande SLA 16H simplified,Custom field (Domain List),<PERSON><PERSON><PERSON> de traitement,Du<PERSON>e de traitement simplified,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> simplified,<PERSON><PERSON><PERSON> de prise en charge,<PERSON><PERSON>lai de prise en charge simplified,Custom field (Effects),Custom field (Entity List),Custom field (Environment),Custom field (Epic Link),Custom field (External Contributor/s),Custom field (External issue ID),Fermeture apres x jours,Fermeture apres x jours simplified,Custom field (First Backlog Transition),Custom field (First key),Custom field (First-Name),Custom field (Flagged),Custom field (Generic Test Definition),Custom field (Groups),Custom field (Groups),Custom field (Impact),Custom field (Impacted Entity),Custom field (Jira Project Type),Custom field (Last-Name),Custom field (Linked major incidents),Custom field (List Entity),Custom field (MVP Macro Budget (K€)),Custom field (Mail),Custom field (Manual Test Steps),Custom field (Operational categorization),Custom field (Organizations),Custom field (Original story points),Custom field (Overcoast),Custom field (Parent Key),Custom field (Parent Link),Custom field (Penalties),Custom field (Platform),Custom field (Pre-Conditions association with a Test),Prise en compte,Prise en compte simplified,Custom field (Product categorization),Custom field (QC),Custom field (Qualification Date),Custom field (Rank),Custom field (Ref. Project CARTO),Custom field (Reference Code),Custom field (Request participants),Resolution Time SLA,Resolution Time SLA simplified,Response Time SLA,Response Time SLA simplified,Custom field (Result),Custom field (Review date),Custom field (Revision),Satisfaction score (out of 5),Custom field (Scoring),Sprint,Custom field (Steps Count),Custom field (Structure Index Monitor),Custom field (Support),Custom field (Target end),Custom field (Target start),Custom field (Team),Custom field (Team List),"Temps d&#39;attribution","Temps d&#39;attribution simplified",Temps première réponse,Temps première réponse simplified,Custom field (Test Execution Status),Custom field (Test Plans associated with a Test),Custom field (Test Repository Path),Custom field (Test Sets association with a Test),Custom field (Test Sets association with a Test),Custom field (Test Type),Custom field (TestRunStatus),Time to close after resolution,Time to close after resolution simplified,Time to first response,Time to first response simplified,Time to resolution,Time to resolution simplified,Custom field (TimeRecup (deprecated)),Custom field (User Activity),Custom field (Workaround (deprecated)),Comment
CFB,SANTSDM-1892,8939084,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,11/Jul/25 6:44 PM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Forward service

CFB

The purpose of the test is to verify that it is possible to provision a subscriber with CFB.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 2.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFB provisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1892,,,,,,,,software,,,,,,"[{""id"":3844652,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFBservice in the HLR.\n\tRegister and Activate CFBservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFBprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102786},{""id"":3844653,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102786}]",,,,,,,,,,,,,,,"0|j1qd0o:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939084,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
CFNRy,SANTSDM-1891,8939083,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Forward service

CFNRy

The purpose of the test is to verify that it is possible to provision a subscriber with CFNRy.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 3.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFNRy provisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1891,,,,,,,,software,,,,,,"[{""id"":3844650,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFNRyservice in the HLR.\n\tRegister and Activate CFNRyservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFRyprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102785},{""id"":3844651,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102785}]",,,,,,,,,,,,,,,"0|j1qd0g:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939083,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
CFNRc,SANTSDM-1890,8939082,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Forward service

CFNRc

The purpose of the test is to verify that it is possible to provision a subscriber with CFNRc.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 4.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFNRc provisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1890,,,,,,,,software,,,,,,"[{""id"":3844648,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFNRcservice in the HLR.\n\tRegister and Activate CFNRcservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFNRcprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102784},{""id"":3844649,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102784}]",,,,,,,,,,,,,,,"0|j1qd08:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939082,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
CFD,SANTSDM-1889,8939081,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,11/Jul/25 6:45 PM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Forward service

CFD

The purpose of the test is to verify that it is possible to provision a subscriber with CFD.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 2.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFDprovisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1889,,,,,,,,software,,,,,,"[{""id"":3844646,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFDservice in the HLR.\n\tRegister and Activate CFDservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFDprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102783},{""id"":3844647,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102783}]",,,,,,,,,,,,,,,"0|j1qd00:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939081,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
SS Call Barred-BAOC,SANTSDM-1888,8939080,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Barring service

SS Call Barring-BAOC

The purpose of the test is to verify that it is possible to provision a subscriber with BAOC.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.088 section 1.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have BAOC provisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1888,,,,,,,,software,,,,,,"[{""id"":3844644,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add BAOCservice in the HLR.\n\tRegister and Activate BAOCservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing BAOCprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102782},{""id"":3844645,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102782}]",,,,,,,,,,,,,,,"0|j1qczs:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939080,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Barring Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
SS Call Barring-BOIC,SANTSDM-1887,8939079,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Barring service

SS Call Barring-BOIC

The purpose of the test is to verify that it is possible to provision a subscriber with BOIC.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.088 section 1.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have BOIC provisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1887,,,,,,,,software,,,,,,"[{""id"":3844642,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add BOICservice in the HLR.\n\tRegister and Activate BOICservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing BOICprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102781},{""id"":3844643,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102781}]",,,,,,,,,,,,,,,"0|j1qczk:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939079,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Barring Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
SS Call Barring- BOIC-exHC,SANTSDM-1886,8939078,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,15/Jul/25 4:41 PM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call barring service

SS Call Barring- BOIC-exHC

The purpose of the test is to verify that it is possible to provision a subscriber with BOIC -exHC.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.088 section 1.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have BOIC -exHC provisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1886,,,,,,,,software,,,,,,"[{""id"":3844640,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add BOIC-EXCH service in the HLR.\n\tRegister and Activate BOIC-EXCHservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing BOIC-EXCH provision data.\n\n\""""},""attachments"":[],""testVersionId"":1102780},{""id"":3844641,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102780}]",,,,,,,,,,,,,,,"0|j1qczc:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939078,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Barring Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
SS Call Barring-BAIC,SANTSDM-1885,8939077,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Barring Service

SSCall Berried-BAIC

The purpose of the test is to verify that it is possible to provision a subscriber with BAIC.

Reference: 3GPP TS 29.002 sections 11.3  22.4, and 3GPP TS 23.088 section 7.1.2

PRECONDITIONS : 
	
	Configurations: A
	
	
	MS does not have BAIC provisioned.
	
	
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1885,,,,,,,,software,,,,,,"[{""id"":3844638,""index"":1,""fields"":{""Action"":""\""\n\t\n\tProvision the MS with BAIC.\n\t\n\t\n\tActivate BAIC.\n\t\n\t\n\tAttempt a mobile terminate call.\n\t\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\tVerify that the provision is completed successfully.\n\t\n\t\n\tVerify that the call is rejected ( Test is done via OTIP Map SRI Message)\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102779},{""id"":3844639,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\t\n\t\t\t\t\n\t\t\t\tGMSCHLRSend Routing Information Request\n\t\t\t\t\n\t\t\t\t\n\t\t\t\tGMSCHLRMAP ERROR\n\t\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102779}]",,,,,,,,,,,,,,,"0|j1qcz4:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939077,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Barring Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
SS Call Barring- BIC-ROAM,SANTSDM-1884,8939076,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Barring Service

SS Call Berried- BIC-ROAM

The purpose of the test is to verify that it is possible to provision a subscriber with BIC-ROAM.

Reference: 3GPP TS 29.002 sections 11.3  22.4, and 3GPP TS 23.088 section 7.1.2



PRECONDITIONS : 
	
	Configurations: A
	
	
	MS does not have BIC-ROAM provisioned.
	
	
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1884,,,,,,,,software,,,,,,"[{""id"":3844636,""index"":1,""fields"":{""Action"":""\""\n\t\n\tProvision the MS with BIC-ROAM.\n\t\n\t\n\tActivate BIC-ROAM.\n\t\n\t\n\tAttempt a mobile terminate call.\n\t\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\tVerify that the provision is completed successfully.\n\t\n\t\n\tVerify that the call is rejected when MS roaming outside the Home PLMN Country ( via OTIP Map Message SRI)\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102778},{""id"":3844637,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\t\n\t\t\t\t\n\t\t\t\tGMSC  HLRSend Routing Information Request\n\t\t\t\t\n\t\t\t\t\n\t\t\t\tGMSC  HLRMAP ERROR\n\t\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102778}]",,,,,,,,,,,,,,,"0|j1qcyw:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939076,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Barring Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
Barring Controlled by subscriber,SANTSDM-1883,8939075,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Barring service

Barring Controlled by subscriber

The purpose of this test is to perform a successful activation of the call barring protected supplementary service.

Reference: 3GPP TS 29.002 sections 11.8, 22.4  3GPP TS 23.011 section 3.4

PRECONDITIONS : 
	Configurations: CS Networks
	The subscription option is provisioned with ""Control of a Supplementary Service by the subscriber using a password"" and a password is provided at provision time.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1883,,,,,,,,software,,,,,,"[{""id"":3844634,""index"":1,""fields"":{""Action"":""\""Activate ss BAOC in UE with correct password\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the activation is successful.\n\n\""""},""attachments"":[],""testVersionId"":1102777},{""id"":3844635,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tActivate SS\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tGet Password\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tGet Password Ack\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tActivate SS Ack\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102777}]",,,,,,,,,,,,,,,"0|j1qcyo:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939075,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Barring Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
Barring Controlled by service provider,SANTSDM-1882,8939074,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Barring service

Barring Controlled by service provider

The purpose of this test is to verify that it is not possible to active ss BAOC service when the control of the Supplementary Service is reserved to the service provider.

PRECONDITIONS : 
	Configurations: CS Networks
	The subscription option is provisioned with ""Control of a Supplementary Service by the service provider""
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1882,,,,,,,,software,,,,,,"[{""id"":3844632,""index"":1,""fields"":{""Action"":""\""Activate call barring in UE with correct password\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the activation is unsuccessful.\n\n\""""},""attachments"":[],""testVersionId"":1102776},{""id"":3844633,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tActivate SS\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tMap Error\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102776}]",,,,,,,,,,,,,,,"0|j1qcyg:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939074,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Barring Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
HLR Location Update Management CS,SANTSDM-1881,8939073,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:34 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,UpdateLocation,"SUMMARY : Mobility Management

First-time LU of MS in VLR

The purpose of this test is to perform a successful registration of the MS first time LU in the VLR.

Reference: 3GPP TS 29.002 section 8.1.2, 19.1.1

PRECONDITIONS : 1. Subscriber A has been allocated in HLR and has no roaming restriction;

2. Subscriber has not registered for VLR location information in HLR;

3. Network Configuration:",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1881,,,,,,,,software,,,,,,"[{""id"":3844630,""index"":1,""fields"":{""Action"":""\""1. Subscriber A powers on and register in VLR1 and VLR1 originates LU to HLR;\n\n2. Observe if LU signalling flow is normal in HLR and VLR1;\n\n3. Check if new location information of subscriber is correct in agent.\n\"""",""Data"":"""",""Expected Result"":""\""1. HLR receives UPDATE LOCATION REQUEST from VLR1 and sends INSERT SUBSCRIBER DATA to VLR1. After receiving INSERT SUBSCRIBER DATA RESPONSE from VLR1, HLR sends UPDATE LOCATION RESPONSE to VLR1;\n\n2. MS accomplishes LU and log in network successfully;\n\n3. It is seen at HLR agent that VLR number in location information of subscriber A is number of VLR1.\n\""""},""attachments"":[],""testVersionId"":1102775},{""id"":3844631,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tUpdate Location Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert SubscriberData Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert SubscriberData Response\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tUpdate Location Response\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102775}]",,,,,,,,,,,,,,,"0|j1qcy8:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939073,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Location Management,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
HLR Location Update Management PS,SANTSDM-1880,8939071,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:34 PM,11/Jul/25 6:45 PM,,,,HLR,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : Mobility Management

First-time LU of MS in SLR

The purpose of this test is to perform a successful registration of the MS first time LU in the SLR.

Reference: 3GPP TS 29.002 section 8.1.2, 19.1.1

PRECONDITIONS : 1. Subscriber A has been allocated in HLR and has no roaming restriction;

2. Subscriber has not registered for SLR location information in HLR;",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1880,,,,,,,,software,,,,,,"[{""id"":3844628,""index"":1,""fields"":{""Action"":""\""1. Subscriber A powers on and register in SLR1 and SLR1 originates LU to HLR;\n\n2. Observe if LU signalling flow is normal in HLR and SLR1;\n\n3. Check if new location information of subscriber is correct in agent.\n\"""",""Data"":"""",""Expected Result"":""\""1. HLR receives UPDATE GPRS LOCATION REQUEST from SLR1 and sends INSERT SUBSCRIBER DATA GPRS to SLR1. After receiving INSERT SUBSCRIBER DATA GPRS RESPONSE from SLR1, HLR sends UPDATE GPRS LOCATION RESPONSE to SLR1;\n\n2. MS accomplishes LU and log in network successfully;\n\n3. It is seen at HLR agent that VLR number in location information of subscriber A is number of VLR1.\n\""""},""attachments"":[],""testVersionId"":1102774},{""id"":3844629,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tSGSN\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tUpdate GPRS Location Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tSGSN\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert SubscriberData GPRS Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tSGSN\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert SubscriberData GPRS Response\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tSGSN\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tUpdateGPRS Location Response\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102774}]",,,,,,,,,,,,,,,"0|j1qcxs:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939071,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Location Management,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
HLR Insert Subscriber Data CS – Sign Telephone service,SANTSDM-1879,8939070,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Subscriber Data Management

Successful insert subscriber data  Sign Telephone service

The purpose of this test is to verify that telephone service can be provided to an MS.

PRECONDITIONS : 
	Network configuration: CS network
	MS is attached in CS network.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1879,,,,,,,,software,,,,,,"[{""id"":3844626,""index"":1,""fields"":{""Action"":""\""Modify the subscriber subscription information to add telephone service in the HLR.\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the subscription modification is successful.\n\n\""""},""attachments"":[],""testVersionId"":1102773},{""id"":3844627,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102773}]",,,,,,,,,,,,,,,"0|j1qcxk:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939070,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Location Management,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
HLR Insert Subscriber Data GPRS – addition of a PDP context,SANTSDM-1878,8939069,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Subscriber Data Management

Successful insert subscriber data  Addition of a PDP context

The purpose of this test is to verify that telephone service can be provided to an MS.

PRECONDITIONS : 
	Network configuration: PS network
	MS is attached in PS network.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1878,,,,,,,,software,,,,,,"[{""id"":3844624,""index"":1,""fields"":{""Action"":""\""Modify the subscriber subscription information to add PDP contextservice in the HLR.\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the subscription modification is successful.\n\n\""""},""attachments"":[],""testVersionId"":1102772},{""id"":3844625,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriberData GPRS request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriberData GPRS response\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102772}]",,,,,,,,,,,,,,,"0|j1qcxc:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939069,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Location Management,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
"HLR Delete subscriber data – single PDP context deleted, context active",SANTSDM-1877,8939068,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Subscriber Data Management

Successful Delete subscriber data  Remove Telephone service

The purpose of this test is to verify that telephone service can be deleted.

PRECONDITIONS : 
	Network configuration: CS network
	MS is attached in CS network.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1877,,,,,,,,software,,,,,,"[{""id"":3844622,""index"":1,""fields"":{""Action"":""\""Modify the subscriber subscription information to add telephone service in the HLR.\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the subscription modification is successful.\n\n\""""},""attachments"":[],""testVersionId"":1102771},{""id"":3844623,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102771}]",,,,,,,,,,,,,,,"0|j1qcx4:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939068,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Location Management,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
HLR Send Authentication Information,SANTSDM-1876,8939067,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:47 PM,16/Jul/25 10:17 AM,,,,HLR,,0,Manual,NonFunctional,Regression,Security,System,,,,"SUMMARY : Mobility Management

First-time SAI of MS in VLR

The purpose of this test is to perform a successful registration of the MS first time SAI in the VLR.



PRECONDITIONS : 1. Subscriber A has been allocated in HLR and has no roaming restriction;

2. Subscriber has not registered for VLR location information in HLR;

3. Network Configuration:",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1876,,,,,,,,software,,,,,,"[{""id"":3844619,""index"":1,""fields"":{""Action"":""\""1. Subscriber A powers on and register in VLR1 and VLR1 originates Send Auth Info Request to HLR with 3 requested vectors;\n\n2. Observe if SAI signalling flow is normal in HLR and VLR1;\n\n3. Check if the authentifaction parameter of subscriber is correct in agent.\n\"""",""Data"":"""",""Expected Result"":""\""HLR receives Send Auth INfo Rquest from VLR1 and sends Authentification vectors to VLR1.\n\n\n\""""},""attachments"":[],""testVersionId"":1102770},{""id"":3844620,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tSEND Authent INFO Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tSEND Authent INFO Response\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102770},{""id"":3844621,""index"":3,""fields"":{""Action"":""\""None\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102770}]",,,,,,,,,,,,,,,"0|j1qcww:",,,,,,,,,,,,,,3.0,,,,,,,,,,,"{""issueId"":8939067,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Location Management,SANTSDM-1912,,Manual,TODO,,,,,,,,,,
HLR Send Routing Information,SANTSDM-1875,8939066,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:34 PM,16/Jul/25 11:07 AM,,,,HLR,,0,Functional,Manual,OAM,Regression,System,,,,"SUMMARY : Mobility Management

First-time Send routing info of MS in VLR

The purpose of this test is to perform a terminating call.

Reference: 3GPP TS 29.002 section 8.1.2, 19.1.1

PRECONDITIONS : 1. The MAP_send_routing_information _REQ message is send by the GMSC to request the routing information of the call to the HLR;

2. The MAP_Provide_Roaming_number_IND message is send by the HLR to obtain the MSRN from the Terminating MSC;

3. The MAP_Provide_Roaming_number_RSP message is send by the Terminating VLR to the HLR to acknoledge the MAP_PROVIDE_ROAMING_NUMBER_IND message:",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1875,,,,,,,,software,,,,,,"[{""id"":3844617,""index"":1,""fields"":{""Action"":""\""1. The MAP_send_routing_information _REQ message is send by the GMSC to request the routing information of the call to the HLR ;\n\n2. The MAP_Provide_Roaming_number_IND message is send by the HLR to obtain the MSRN from the Terminating MSC; ;\n\n3. The MAP_Provide_Roaming_number_RSP message is send by the Terminating VLR to the HLR to acknoledge the MAP_PROVIDE_ROAMING_NUMBER_IND message: .\n\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102769},{""id"":3844618,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tSRI Req\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tRoaming number Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tRoaming number Response\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tSRI resp\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102769}]",,,,,,,,,,,,,,,"0|j1qcwo:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939066,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Location Management,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
HLR Send Routing Information for Short Message,SANTSDM-1874,8939065,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:28 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,Functional,IMS,Manual,Regression,System,,,"SUMMARY : Mobility Management

First-time Send Routing Info for Short message of MS in VLR

The purpose of this test is to perform a terminating call.

Reference: 3GPP TS 29.002 section 8.1.2, 19.1.1

PRECONDITIONS : 1. The MAP_send_routing_information _REQ message for Short message is send by the GMSC to request the routing information of the call to the HLR;

2. The MAP_send_routing_information _RSP message is send by the HLR.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1874,,,,,,,,software,,,,,,"[{""id"":3844615,""index"":1,""fields"":{""Action"":""\""1. The MAP_send_routing_information _REQfor SM is send by the GMSC to request the routing information of the call to the HLR;\n\n2. The MAP_send_routing_information _RSP for SM message is send by the HLR.\n\n\n\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102768},{""id"":3844616,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tSend Rountig Info for SM Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tSend Routing Info for SM Resp\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102768}]",,,,,,,,,,,,,,,"0|j1qcwg:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939065,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Location Management,SANTSDM-1906,,Manual,TODO,,,,,,,,,,
HLR Call Waiting,SANTSDM-1873,8939064,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:34 PM,16/Jul/25 11:07 AM,,,,HLR,,0,Functional,Manual,OAM,Regression,System,,,,"SUMMARY : Supplementary service

Call Waiting

The purpose of this test is call waiting function.

Reference: 3GPP TS 23.083 section 1

PRECONDITIONS : 1. Terminal supports CW;

2. Subscriber A, B,C have been allocated in HLR and log on network successfully;

3. Network Configuration",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1873,,,,,,,,software,,,,,,"[{""id"":3844613,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add Call Waitservice in the HLR.\n\tRegister and Activate Call Waitservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing Call Waitprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102767},{""id"":3844614,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102767}]",,,,,,,,,,,,,,,"0|j1qcw8:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939064,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Supplementary service,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
HLR Call Holding,SANTSDM-1872,8939063,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:34 PM,16/Jul/25 11:07 AM,,,,HLR,,0,Functional,Manual,OAM,Regression,System,,,,"SUMMARY : Supplementary service

Call Holding

The purpose of this test is call holding function.

Reference: 3GPP TS 23.083 section 2

PRECONDITIONS : 1. Terminal supports CH;

2. Subscriber A and B have been allocated in HLR and log on network successfully;

3. Network Configuration",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1872,,,,,,,,software,,,,,,"[{""id"":3844611,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add Call Holdservice in the HLR.\n\tRegister and Activate Call Holdservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing Call Holdprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102766},{""id"":3844612,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102766}]",,,,,,,,,,,,,,,"0|j1qcw0:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939063,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Supplementary service,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
HLR CLIP,SANTSDM-1871,8939062,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:02 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,Functional,Manual,MAP,Regression,SupplementaryService,System,,"SUMMARY : The Calling Line Identification Presentation (CLIP) service is a supplementary service provided for callees to view the calling numbers.

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1871,,,,,,,,software,,,,,,"[{""id"":3844609,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CLIPservice in the HLR.\n\tRegister and Activate CLIPservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CLIPprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102765},{""id"":3844610,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102765}]",,,,,,,,,,,,,,,"0|j1qcvs:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939062,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Supplementary service,SANTSDM-1905,,Manual,TODO,,,,,,,,,,
HLR CLIR,SANTSDM-1870,8939061,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:02 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,Functional,Manual,MAP,Regression,SupplementaryService,System,,"SUMMARY : The Calling Line Identification Restriction (CLIR) service is a supplementary service provided for callers. This service enables the callers to prevent their numbers from being presented to the callees.

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1870,,,,,,,,software,,,,,,"[{""id"":3844607,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CLIRservice in the HLR.\n\tRegister and Activate CLIRservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CLIRprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102764},{""id"":3844608,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102764}]",,,,,,,,,,,,,,,"0|j1qcvk:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939061,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Supplementary service,SANTSDM-1905,,Manual,TODO,,,,,,,,,,
HLR COLP,SANTSDM-1869,8939060,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:02 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,Functional,Manual,MAP,Regression,SupplementaryService,System,,"SUMMARY : The Connected Line Identification Presentation (COLP) service is a supplementary service provided for callers. It enables callers to view connected numbers.

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1869,,,,,,,,software,,,,,,"[{""id"":3844605,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add COLPservice in the HLR.\n\tRegister and Activate COLPservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing COLPprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102763},{""id"":3844606,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102763}]",,,,,,,,,,,,,,,"0|j1qcvc:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939060,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Supplementary service,SANTSDM-1905,,Manual,TODO,,,,,,,,,,
HLR COLR,SANTSDM-1868,8939059,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:02 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,Functional,Manual,MAP,Regression,SupplementaryService,System,,"SUMMARY : The Connected Line Identification Restriction (COLR) service enables the connected party to prevent its number from being presented to the caller.

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1868,,,,,,,,software,,,,,,"[{""id"":3844603,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add COLRservice in the HLR.\n\tRegister and Activate COLRservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing COLRprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102762},{""id"":3844604,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102762}]",,,,,,,,,,,,,,,"0|j1qcv4:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939059,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Supplementary service,SANTSDM-1905,,Manual,TODO,,,,,,,,,,
HLR OVER-CLIR,SANTSDM-1867,8939058,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:02 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,Functional,Manual,MAP,Regression,SupplementaryService,System,,"SUMMARY : Override Calling Line Identification Restriction (OVER-CLIR) service for subscribers

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1867,,,,,,,,software,,,,,,"[{""id"":3844601,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add OVER-CLIRservice in the HLR.\n\tRegister and Activate OVER-CLIRservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing OVER-CLIRprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102761},{""id"":3844602,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102761}]",,,,,,,,,,,,,,,"0|j1qcuw:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939058,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Supplementary service,SANTSDM-1905,,Manual,TODO,,,,,,,,,,
HLR OVER-COLR,SANTSDM-1866,8939057,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:02 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,Functional,Manual,MAP,Regression,SupplementaryService,System,,"SUMMARY : Override Connected Line Identification Restriction (OVER-COLR) service for subscribers

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1866,,,,,,,,software,,,,,,"[{""id"":3844599,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add OVER-COLRservice in the HLR.\n\tRegister and Activate OVER-COLRservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing OVER-COLRprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102760},{""id"":3844600,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102760}]",,,,,,,,,,,,,,,"0|j1qcuo:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939057,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Supplementary service,SANTSDM-1905,,Manual,TODO,,,,,,,,,,
HLR MPTY,SANTSDM-1865,8939056,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:02 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,Functional,Manual,MAP,Regression,SupplementaryService,System,,"SUMMARY : The multiparty (MPTY) service enables a subscriber to originate a multi-connection call. That is, a service subscriber can talk with three or more participants at the same time.

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1865,,,,,,,,software,,,,,,"[{""id"":3844597,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add MPTY service in the HLR.\n\tRegister and Activate MPTYservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing MPTY provision data.\n\n\""""},""attachments"":[],""testVersionId"":1102759},{""id"":3844598,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102759}]",,,,,,,,,,,,,,,"0|j1qcug:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939056,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Supplementary service,SANTSDM-1905,,Manual,TODO,,,,,,,,,,
HLR AoC,SANTSDM-1864,8939055,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:02 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,Functional,Manual,MAP,Regression,SupplementaryService,System,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1864,,,,,,,,software,,,,,,[],,,,,,,,,,,,,,,"0|j1qcu8:",,,,,,,,,,,,,,0.0,,,,,,,,,,,"{""issueId"":8939055,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Supplementary service,SANTSDM-1905,,Manual,TODO,,,,,,,,,,
HLR PLMNSS,SANTSDM-1863,8939054,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:02 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,Functional,Manual,MAP,Regression,SupplementaryService,System,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1863,,,,,,,,software,,,,,,[],,,,,,,,,,,,,,,"0|j1qcu0:",,,,,,,,,,,,,,0.0,,,,,,,,,,,"{""issueId"":8939054,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Supplementary service,SANTSDM-1905,,Manual,TODO,,,,,,,,,,
HLR Roaming restriction CS,SANTSDM-1862,8939053,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:34 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,UpdateLocation,"SUMMARY : MS-A has subscribed to roaming restriction feature
At location update procedure theRegional Subscription Data that present in the ISD indicate the subscription area zone code on which MS-A can roam

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1862,,,,,,,,software,,,,,,"[{""id"":3844596,""index"":1,""fields"":{""Action"":""\""MS-A try aCS location update to a blacklisted VLR number\n\"""",""Data"":"""",""Expected Result"":""\""HLR refuse the update location of MS-A\n\""""},""attachments"":[],""testVersionId"":1102756}]",,,,,,,,,,,,,,,"0|j1qcts:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939053,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR Specific Roaming,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
HLR Roaming restriction PS,SANTSDM-1861,8939052,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:34 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,UpdateLocation,"SUMMARY : MS-A has subscribed to roaming restriction feature
At location update procedure theRegional Subscription Data that present in the ISD indicate the subscription area zone code on which MS-A can roam

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1861,,,,,,,,software,,,,,,"[{""id"":3844595,""index"":1,""fields"":{""Action"":""\""MS-A try a PS location update to a blacklisted SGSNnumber\n\"""",""Data"":"""",""Expected Result"":""\""HLR refuse the update location of MS-A\n\""""},""attachments"":[],""testVersionId"":1102755}]",,,,,,,,,,,,,,,"0|j1qctk:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939052,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR Specific Roaming,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
HLR CUG subscription,SANTSDM-1860,8939051,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:33 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,Functional,Manual,MAP,Regression,System,,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1860,,,,,,,,software,,,,,,"[{""id"":3844593,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CUGservice in the HLR, with only CUG intra-group call allowed\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CUG information Listdata.\n\n\""""},""attachments"":[],""testVersionId"":1102754},{""id"":3844594,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102754}]",,,,,,,,,,,,,,,"0|j1qctc:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939051,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR Closed User Group - CUG,SANTSDM-1904,,Manual,TODO,,,,,,,,,,
HLR CUG terminating call intra-group allowed,SANTSDM-1859,8939050,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:33 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,Functional,Manual,MAP,Regression,System,,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1859,,,,,,,,software,,,,,,"[{""id"":3844590,""index"":1,""fields"":{""Action"":""\""- MS-A has subscribed to CUGservice in the HLR, with only CUG intra-group call allowed\n- MS-A receive a call from MS-B which ispart of the CUG\n\"""",""Data"":"""",""Expected Result"":""\""- The HLR allow the call after the analysis of the SRI message that contain CUG interlock parameter with Intra-CUG restrictions to None\n\""""},""attachments"":[],""testVersionId"":1102753},{""id"":3844591,""index"":2,""fields"":{""Action"":""\""- MS-A has subscribed to CUGservice in the HLR, with only CUG intra-group call allowed\n- MS-A receive a call from MS-B which is not part of the CUG\n\"""",""Data"":"""",""Expected Result"":""\""- The HLR reject the call after the analysis of the SRI message that contain CUG interlock parameter with telephony service inter-CUG accessibiltity to value None Designated\n\""""},""attachments"":[],""testVersionId"":1102753},{""id"":3844592,""index"":3,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Send Routing Info\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Send Routing Info Ack - Reject\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102753}]",,,,,,,,,,,,,,,"0|j1qct4:",,,,,,,,,,,,,,3.0,,,,,,,,,,,"{""issueId"":8939050,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR Closed User Group - CUG,SANTSDM-1904,,Manual,TODO,,,,,,,,,,
HLR CUG terminating call inter-group allowed,SANTSDM-1858,8939049,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:33 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,Functional,Manual,MAP,Regression,System,,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1858,,,,,,,,software,,,,,,"[{""id"":3844587,""index"":1,""fields"":{""Action"":""\""- MS-A has subscribed to CUGservice in the HLR, with only CUG inter-group call allowed\n- MS-A receive a call from MS-B which ispart of the same CUG\n\"""",""Data"":"""",""Expected Result"":""\""- The HLR allow the call after the analysis of the SRI message that contain CUG interlock parameter with Intra-CUG restrictions to None\n\n- The HLR allowthe call after the analysis of the SRI message that contain CUG interlock parameter with telephony service inter-CUG accessibiltity to valueOutgoing and Incoming Access\n\""""},""attachments"":[],""testVersionId"":1102752},{""id"":3844588,""index"":2,""fields"":{""Action"":""\""- MS-A has subscribed to CUGservice in the HLR, with only CUG intra-group call allowed\n- MS-A receive a call from MS-B which is not part of the CUG\n\"""",""Data"":"""",""Expected Result"":""\""- The HLR reject the call after the analysis of the SRI message that contain CUG interlock parameter with telephony service inter-CUG accessibiltity to value None Designated\n\n- The HLR allow the call after the analysis of the SRI message that contain CUG interlock parameter with Intra-CUG restrictions to None\n\""""},""attachments"":[],""testVersionId"":1102752},{""id"":3844589,""index"":3,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Send Routing Info\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Send Routing Info Ack - Reject\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102752}]",,,,,,,,,,,,,,,"0|j1qcsw:",,,,,,,,,,,,,,3.0,,,,,,,,,,,"{""issueId"":8939049,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR Closed User Group - CUG,SANTSDM-1904,,Manual,TODO,,,,,,,,,,
HLR ECT,SANTSDM-1857,8939048,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:34 PM,16/Jul/25 11:07 AM,,,,HLR,,0,Functional,Manual,OAM,Regression,System,,,,"SUMMARY : Explicit Call Transfer

ECT

The purpose of the test is to verify that it is possible to provision a subscriber with ECT.

Reference: 3GPP TS 29.002, TS22.091, TS23.091, TS24.091

PRECONDITIONS : 
	Configurations: CS Networks",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1857,,,,,,,,software,,,,,,"[{""id"":3844585,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add ECTservice in the HLR.\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing ECTprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102751},{""id"":3844586,""index"":2,""fields"":{""Action"":""\""Messages fows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102751}]",,,,,,,,,,,,,,,"0|j1qcso:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939048,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR Explicit Cal Transfer,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
HLR BORO,SANTSDM-1856,8939047,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Barring of All Outgoing Calls when Roaming

The purpose of the test is to verify that it is possible to provision a subscriber with BORO.

Reference: 3GPP TS 29.002, TS22.088, TS 23.088, TS 24.088

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have BOROprovisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1856,,,,,,,,software,,,,,,"[{""id"":3844583,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add BOROservice in the HLR.\n\tRegister and Activate BOROservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing BOROprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102750},{""id"":3844584,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102750}]",,,,,,,,,,,,,,,"0|j1qcsg:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939047,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR Barring of All Outgoing Calls when Roaming - BORO,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
HLR GPRS Mobility Management,SANTSDM-1855,8939046,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,16/Jul/25 11:07 AM,,,,HLR,,0,Manual,NonFunctional,Regression,Security,System,,,,"SUMMARY : - Attach procedure
- Location update procedure

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1855,,,,,,,,software,,,,,,"[{""id"":3844581,""index"":1,""fields"":{""Action"":""\""- Provision MS user with GPRS profile in the HLR\n- Send Authentication message procedure\n\"""",""Data"":"""",""Expected Result"":""\""- Authentication vector are provided by the HLR\n\""""},""attachments"":[],""testVersionId"":1102749},{""id"":3844582,""index"":2,""fields"":{""Action"":""\""- Send Location update message procedure\n\"""",""Data"":"""",""Expected Result"":""\""- Insert subscriber data message are replied\n- Location update procedure is acknoledged\n\""""},""attachments"":[],""testVersionId"":1102749}]",,,,,,,,,,,,,,,"0|j1qcs8:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939046,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR GPRS,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
HLR GPRS Session management,SANTSDM-1854,8939045,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:34 PM,16/Jul/25 11:07 AM,,,,HLR,,0,Functional,Manual,OAM,Regression,System,,,,"SUMMARY : Network initiated PDP context activation procedure

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1854,,,,,,,,software,,,,,,"[{""id"":3844580,""index"":1,""fields"":{""Action"":""\""- Provision MS with GPRS profile\n- Initiate Send Routing Info for GPRS message procedure\n\"""",""Data"":"""",""Expected Result"":""\""- Send Routing Info for GPRS message response is replied with success\n\""""},""attachments"":[],""testVersionId"":1102748}]",,,,,,,,,,,,,,,"0|j1qcs0:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939045,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR GPRS,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
HLR GPRS Subscriber data insertion,SANTSDM-1853,8939044,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:33 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,Functional,Manual,MAP,Regression,System,,,"SUMMARY : - Modification of subscriber profile

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1853,,,,,,,,software,,,,,,"[{""id"":3844579,""index"":1,""fields"":{""Action"":""\""- Provision an existing MS with new datain his GPRS profile\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the HLR send the Insert Subscriber Data message procedure\n\""""},""attachments"":[],""testVersionId"":1102747}]",,,,,,,,,,,,,,,"0|j1qcrs:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939044,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR GPRS,SANTSDM-1904,,Manual,TODO,,,,,,,,,,
HLR GPRS Subscriber data deletion,SANTSDM-1852,8939043,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:33 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,Functional,Manual,MAP,Regression,System,,,"SUMMARY : - Supress of existing data in MSprofile

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1852,,,,,,,,software,,,,,,"[{""id"":3844578,""index"":1,""fields"":{""Action"":""\""- Supress an existing data in the GPRS MS profile\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the HLR initiate the Delete Subscriber Data message procedure\n\""""},""attachments"":[],""testVersionId"":1102746}]",,,,,,,,,,,,,,,"0|j1qcrk:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939043,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR GPRS,SANTSDM-1904,,Manual,TODO,,,,,,,,,,
HLR GPRS Initiated detach procedure,SANTSDM-1851,8939042,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,16/Jul/25 11:07 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : - The HLR initiate a detach procedure to delete MM and PDP contexts on the SGSN

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1851,,,,,,,,software,,,,,,"[{""id"":3844577,""index"":1,""fields"":{""Action"":""\""- Provision the detach procedure from the HLR GUI\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the HLR initiate the Cancel Location message procedure\n\""""},""attachments"":[],""testVersionId"":1102745}]",,,,,,,,,,,,,,,"0|j1qcrc:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939042,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR GPRS,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
HLR CAMEL O-CSI,SANTSDM-1850,8939041,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:29 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,CAMEL,Functional,Manual,MAP,Regression,System,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1850,,,,,,,,software,,,,,,"[{""id"":3844576,""index"":1,""fields"":{""Action"":""\""- Provision O-CSI data for an existing MS\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the HLR initiate the Insert Subscriber Data message procedure\n- Verify thatthe ISD message contain the O-CSI data\n\""""},""attachments"":[],""testVersionId"":1102744}]",,,,,,,,,,,,,,,"0|j1qcr4:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939041,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR CAMEL Services,SANTSDM-1903,,Manual,TODO,,,,,,,,,,
HLR CAMEL T-CSI,SANTSDM-1849,8939040,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:29 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,CAMEL,Functional,Manual,MAP,Regression,System,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1849,,,,,,,,software,,,,,,"[{""id"":3844574,""index"":1,""fields"":{""Action"":""\""- Provision T-CSI data for an existing MS\n- Initiate a SRI message procedure\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the SRI message response contain the T-CSI data\n\""""},""attachments"":[],""testVersionId"":1102743},{""id"":3844575,""index"":2,""fields"":{""Action"":""\""- Initiate a SRI message procedure that contain supress T-CSI\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the SRI message response contain the MSRN\n\""""},""attachments"":[],""testVersionId"":1102743}]",,,,,,,,,,,,,,,"0|j1qcqw:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939040,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR CAMEL Services,SANTSDM-1903,,Manual,TODO,,,,,,,,,,
HLR CAMEL SS-CSI,SANTSDM-1848,8939039,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:29 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,CAMEL,Functional,Manual,MAP,Regression,System,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1848,,,,,,,,software,,,,,,"[{""id"":3844573,""index"":1,""fields"":{""Action"":""\""- Provision SS-CSI data for an existing MS\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the HLR initiate the Insert Subscriber Data message procedure\n- Verify thatthe ISD message contain the SS-CSI data\n\""""},""attachments"":[],""testVersionId"":1102742}]",,,,,,,,,,,,,,,"0|j1qcqo:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939039,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR CAMEL Services,SANTSDM-1903,,Manual,TODO,,,,,,,,,,
HLR CAMEL TIF-CSI,SANTSDM-1847,8939038,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:29 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,CAMEL,Functional,Manual,MAP,Regression,System,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1847,,,,,,,,software,,,,,,"[{""id"":3844572,""index"":1,""fields"":{""Action"":""\""- Provision TIF-CSI data for an existing MS\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the HLR initiate the Insert Subscriber Data message procedure\n- Verify thatthe ISD message contain the TIF-CSI data\n\""""},""attachments"":[],""testVersionId"":1102741}]",,,,,,,,,,,,,,,"0|j1qcqg:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939038,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR CAMEL Services,SANTSDM-1903,,Manual,TODO,,,,,,,,,,
HLR CAMEL U-CSI,SANTSDM-1846,8939037,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:29 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,CAMEL,Functional,Manual,MAP,Regression,System,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1846,,,,,,,,software,,,,,,"[{""id"":3844571,""index"":1,""fields"":{""Action"":""\""- Provision U-CSI data for an existing MS\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the U-CSI provionning is appliedin the SDM database\n\""""},""attachments"":[],""testVersionId"":1102740}]",,,,,,,,,,,,,,,"0|j1qcq8:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939037,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR CAMEL Services,SANTSDM-1903,,Manual,TODO,,,,,,,,,,
HLR CAMEL UG-CSI,SANTSDM-1845,8939036,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:29 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,CAMEL,Functional,Manual,MAP,Regression,System,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1845,,,,,,,,software,,,,,,"[{""id"":3844570,""index"":1,""fields"":{""Action"":""\""- Provision UG-CSI data for an existing MS\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the UG-CSI provionning is appliedin the SDM database\n\""""},""attachments"":[],""testVersionId"":1102739}]",,,,,,,,,,,,,,,"0|j1qcq0:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939036,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR CAMEL Services,SANTSDM-1903,,Manual,TODO,,,,,,,,,,
HLR CAMEL GPRS-CSI,SANTSDM-1844,8939035,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:29 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,CAMEL,Functional,Manual,MAP,Regression,System,,"SUMMARY : None
PRECONDITIONS : - Support of CAP3",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1844,,,,,,,,software,,,,,,"[{""id"":3844569,""index"":1,""fields"":{""Action"":""\""- Provision GPRS-CSI data for an existing MS\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the HLR initiate the Insert Subscriber Data message procedure\n- Verify thatthe ISD message contain the GPRS-CSI data\n\""""},""attachments"":[],""testVersionId"":1102738}]",,,,,,,,,,,,,,,"0|j1qcps:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939035,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR CAMEL Services,SANTSDM-1903,,Manual,TODO,,,,,,,,,,
HLR CAMEL D-CSI,SANTSDM-1843,8939034,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:29 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,CAMEL,Functional,Manual,MAP,Regression,System,,"SUMMARY : None
PRECONDITIONS : - Support of CAP3",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1843,,,,,,,,software,,,,,,"[{""id"":3844568,""index"":1,""fields"":{""Action"":""\""- Provision D-CSI data for an existing MS\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the HLR initiate the Insert Subscriber Data message procedure\n- Verify thatthe ISD message contain the D-CSI data\n\""""},""attachments"":[],""testVersionId"":1102737}]",,,,,,,,,,,,,,,"0|j1qcpk:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939034,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR CAMEL Services,SANTSDM-1903,,Manual,TODO,,,,,,,,,,
HLR CAMEL M-CSI,SANTSDM-1842,8939033,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:34 PM,16/Jul/25 11:07 AM,,,,HLR,,0,Functional,Manual,OAM,Regression,System,,,,"SUMMARY : None
PRECONDITIONS : - Support of CAP3 for CS mobility management
- Support of CAP4 for PS mobility management",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1842,,,,,,,,software,,,,,,"[{""id"":3844567,""index"":1,""fields"":{""Action"":""\""- Provision M-CSI data for an existing MS\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the HLR initiate the Insert Subscriber Data message procedure\n- Verify thatthe ISD message contain the M-CSI data\n\""""},""attachments"":[],""testVersionId"":1102736}]",,,,,,,,,,,,,,,"0|j1qcpc:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939033,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR CAMEL Services,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
HLR CAMEL VT-CSI,SANTSDM-1841,8939032,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:29 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,CAMEL,Functional,Manual,MAP,Regression,System,,"SUMMARY : None
PRECONDITIONS : - Support of CAP3 or CAP4",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1841,,,,,,,,software,,,,,,"[{""id"":3844566,""index"":1,""fields"":{""Action"":""\""- Provision VT-CSI data for an existing MS\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the HLR initiate the Insert Subscriber Data message procedure\n- Verify thatthe ISD message contain the VT-CSI data\n\""""},""attachments"":[],""testVersionId"":1102735}]",,,,,,,,,,,,,,,"0|j1qcp4:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939032,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR CAMEL Services,SANTSDM-1903,,Manual,TODO,,,,,,,,,,
HLR CAMEL MO-SMS-CSI,SANTSDM-1840,8939031,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:29 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,CAMEL,Functional,Manual,MAP,Regression,System,,"SUMMARY : None
PRECONDITIONS : - Support of CAP3 or CAP4",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1840,,,,,,,,software,,,,,,"[{""id"":3844565,""index"":1,""fields"":{""Action"":""\""- Provision MO-SMS-CSI data for an existing MS\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the HLR initiate the Insert Subscriber Data message procedure\n- Verify thatthe ISD message contain the MO-SMS-CSI data\n\""""},""attachments"":[],""testVersionId"":1102734}]",,,,,,,,,,,,,,,"0|j1qcow:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939031,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR CAMEL Services,SANTSDM-1903,,Manual,TODO,,,,,,,,,,
HLR CAMEL MT-CSI,SANTSDM-1839,8939030,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 4:29 PM,16/Jul/25 11:07 AM,,,,HLR,,0,2G,CAMEL,Functional,Manual,MAP,Regression,System,,"SUMMARY : None
PRECONDITIONS : - Support of CAP4",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1839,,,,,,,,software,,,,,,"[{""id"":3844564,""index"":1,""fields"":{""Action"":""\""- Provision MT-CSI data for an existing MS\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the HLR initiate the Insert Subscriber Data message procedure\n- Verify thatthe ISD message contain the MT-CSI data\n\""""},""attachments"":[],""testVersionId"":1102733}]",,,,,,,,,,,,,,,"0|j1qcoo:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939030,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR CAMEL Services,SANTSDM-1903,,Manual,TODO,,,,,,,,,,
HLR Restore,SANTSDM-1838,8939029,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 2:11 PM,16/Jul/25 11:07 AM,,,,HLR,,0,BackupRestore,Manual,NonFunctional,Regression,System,,,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1838,,,,,,,,software,,,,,,"[{""id"":3844563,""index"":1,""fields"":{""Action"":""\""- Send a MAP Restore message procedure to the HLR\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the HLR reply with the ISD message procedure\n- Verifythat the HLR acknowledge the Restore message\n\""""},""attachments"":[],""testVersionId"":1102732}]",,,,,,,,,,,,,,,"0|j1qcog:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939029,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/HLR Fault recovery - RESET - RESTORE,SANTSDM-1898,,Manual,TODO,,,,,,,,,,
HSS_Update_location_ULR,SANTSDM-1837,8939028,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:39 PM,16/Jul/25 11:07 AM,,,,EPC-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,UpdateLocation,"SUMMARY : HSS successfully sending subscriber data to the MME during the update location for adefined subscriber.

Verify that the HSS can successfully send the subscriber data to the MME during theUpdate Location for a subscriber who is provided with the EPS service.

PRECONDITIONS : The HSS and the neighboring NEs operate properly.

The subscriber is defined in the HSS.

The subscriber is provided with the EPS service.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1837,,,,,,,,software,,,,,,"[{""id"":3844561,""index"":1,""fields"":{""Action"":""\""The subscriber originates the Update Location on the MME, and then the MME sends a ULR message to the HSS for the subscription data.\n\nLog in to the Provisioning Gateway client, and query the dynamic information about the subscriber.\n\"""",""Data"":"""",""Expected Result"":""\""The Update Location of the subscriber is successful and the HSS successfully returns a ULA message containing the EPS service data of the subscriber.\n\nThe command output contains the MME address information about the subscriber and the output indicates that the subscriber status is not purged.\n\""""},""attachments"":[],""testVersionId"":1102731},{""id"":3844562,""index"":2,""fields"":{""Action"":""\""Messages flows\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\t1\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t2\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Answer\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102731}]",,,,,,,,,,,,,,,"0|j1qco8:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939028,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/4G HSS Service S6a,SANTSDM-1901,,Manual,TODO,,,,,,,,,,
HSS_Cancel-location_CLR,SANTSDM-1836,8939027,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:39 PM,16/Jul/25 11:07 AM,,,,EPC-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,UpdateLocation,"SUMMARY : Triggering the cancel location during the inter-office update location

Verify the triggering of the CLR procedure during the inter-office update location

PRECONDITIONS : The HSS and the neighboring NEs operate properly.

The subscriber is defined and provided with the EPS service.

The HSS is connected to two MMEs.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1836,,,,,,,,software,,,,,,"[{""id"":3844559,""index"":1,""fields"":{""Action"":""\""The subscriber originates the Update Location on MME1, and then MME1 sends a ULR message to the HSS.\n\nAfter roaming to MME2, the subscriber originates the Update Location on MME2, and then MME2 sends a ULR message to the HSS.\n\nLog in to the Provisioning Gateway client, and query the dynamic information about the subscriber.\n\"""",""Data"":"""",""Expected Result"":""\""The Update Location on MME1 is successful. The dynamic information save in the HSS is the information about MME1.\n\nThe Update Location on MME2 is successful. The HSS sends a ULA message to MME2 and a CLR message to MME1.\n\nThe output contains the address information about MME2.\n\""""},""attachments"":[],""testVersionId"":1102730},{""id"":3844560,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\t1\n\t\t\t\n\t\t\t\n\t\t\tMME1HSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t2\n\t\t\t\n\t\t\t\n\t\t\tMME1HSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Answer\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t3\n\t\t\t\n\t\t\t\n\t\t\tMME2HSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t4\n\t\t\t\n\t\t\t\n\t\t\tMME1HSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tCancel-Location-Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t5\n\t\t\t\n\t\t\t\n\t\t\tMME1HSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tCancel-Location-Answer\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t6\n\t\t\t\n\t\t\t\n\t\t\tMME2HSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Answer\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102730}]",,,,,,,,,,,,,,,"0|j1qco0:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939027,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/4G HSS Service S6a,SANTSDM-1901,,Manual,TODO,,,,,,,,,,
HSS_Purge_UE_PUR,SANTSDM-1835,8939026,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:19 PM,11/Jul/25 6:50 PM,,,,EPC-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,UpdateLocation,"SUMMARY : Purge UE procedure triggered when the subscriber information is deleted on the MME

Verify that the purge UE procedure can be triggered when the subscriber information is deleted on the MME.

PRECONDITIONS : The HSS and the neighboring NEs operate properly.

The subscriber is defined and provided with the EPS service.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1835,,,,,,,,software,,,,,,"[{""id"":3844557,""index"":1,""fields"":{""Action"":""\""The subscriber originates the Update Location on MME, and then MME sends a ULR message to the HSS.\n\nDelete the subscriber on MME.\n\nLog in to the Provisioning Gateway client - deploy WCDMA-Location information, and query the dynamic information about the subscriberIN window EPC location information :the flag is present\n\"""",""Data"":"""",""Expected Result"":""\""The Update Location is successful and the HSS sends a ULA message containing the subscriber data to the MME.\n\nAfter the subscriber information is deleted, the MME sends a PUR message to the HSS and the HSS returns a PUA message.\n\nIn the output, the PURGE flag is displayed as PURGED.\n\""""},""attachments"":[],""testVersionId"":1102729},{""id"":3844558,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\t1\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t2\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Answer\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t3\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tPurge-UE-Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t4\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tPurge-UE-Answer\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102729}]",,,,,,,,,,,,,,,"0|j1qcns:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939026,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/4G HSS Service S6a,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
HSS_Insert_Subscriber_Data_IDR,SANTSDM-1834,8939025,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:19 PM,11/Jul/25 6:50 PM,,,,EPC-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,UpdateLocation,"SUMMARY : Triggering the IDR Procedure during the APN data modification

Verify that the IDR procedure can be triggered when subscriber data is modified.

PRECONDITIONS : The HSS and the neighboring NEs operate properly.

The subscriber is defined and provided with the EPS service.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1834,,,,,,,,software,,,,,,"[{""id"":3844555,""index"":1,""fields"":{""Action"":""\""The subscriber originates Update Location on the MME, and then the MME sends a ULR message to the HSS.\n\nLog in to the Provisioning Gateway client, and modify the APNQOS module number (QoS parameter of the subscriber).\n\"""",""Data"":"""",""Expected Result"":""\""The Update Location of the subscriber is successful and the HSS successfully returns a ULA message containing the subscriber data to the MME.\n\nThe data modification of the subscriber is successful and the HSS sends an IDR message containing the new APN data to the MME.\n\""""},""attachments"":[],""testVersionId"":1102728},{""id"":3844556,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\t1\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t2\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Answer\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t3\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tInsert Subscriber Data-Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t4\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tInsert Subscriber Data-Answer\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102728}]",,,,,,,,,,,,,,,"0|j1qcnk:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939025,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/4G HSS Service S6a,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
HSS_Delete_Subscriber_Data_DSR,SANTSDM-1833,8939024,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:19 PM,11/Jul/25 6:50 PM,,,,EPC-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,UpdateLocation,"SUMMARY : Triggering the DSR Procedure during the deletion of charging characteristics

Verify that the DSR procedure can be triggered when the subscriber data is deleted.

PRECONDITIONS : The HSS and the neighboring NEs operate properly.

The subscriber is defined and provided with the EPS service.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1833,,,,,,,,software,,,,,,"[{""id"":3844553,""index"":1,""fields"":{""Action"":""\""The subscriber originates Update location on the MME, and then the MME sends a ULR message to the HSS.\n\nLog in to the Provisioning Gateway client, and delete Charging Characteristics of the subscriber.\n\"""",""Data"":"""",""Expected Result"":""\""The Update location of the subscriber is successful and the HSS successfully returns a ULA message containing the subscriber data to the MME.\n\nThe data deletion of the subscriber is successful and the HSS sends a DSR message containing DSR-Flags. The value of DSR-Flags is 0x00000004, indicating that the Subscribed Charging Characteristics have been deleted from the subscription data.\n\""""},""attachments"":[],""testVersionId"":1102727},{""id"":3844554,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\t1\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t2\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Answer\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t3\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tDelete-Subscriber-Data-Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t4\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tDelete-Subscriber-Data-Answer\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102727}]",,,,,,,,,,,,,,,"0|j1qcnc:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939024,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/4G HSS Service S6a,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
HSS_Authentication_AIR,SANTSDM-1832,8939023,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:47 PM,16/Jul/25 11:07 AM,,,,EPC-HSS,,0,Manual,NonFunctional,Regression,Security,System,,,,"SUMMARY : eUTRAN authentication information retrieval

Verify that the Authentication Information Retrieval Procedure can be triggered by the MME to request authentication information from the HSS.

PRECONDITIONS : The HSS and the neighboring NEs operate properly.

The subscriber is defined and provided with the EPS service and has no roaming restriction and authentication algorithm is Milanage algorithm and parameters are correct;",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1832,,,,,,,,software,,,,,,"[{""id"":3844551,""index"":1,""fields"":{""Action"":""\""The subscriber originates Update location on the MME, the MME sends a AIR message to the HSS for authentication and includes the Requested-EUTRAN-Authentication-Info AVP in the message\n\"""",""Data"":"""",""Expected Result"":""\""HSS receives AIR and returns AIA to MME,carring the E-UTRAN authentication vectors Mutual authentication between network and UE is successful and UE registers in network successfully.\n\""""},""attachments"":[],""testVersionId"":1102726},{""id"":3844552,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\t1\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tAuthentication-Information-Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t2\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tAuthentication-Information-Answer\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t3\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t4\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Answer\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102726}]",,,,,,,,,,,,,,,"0|j1qcn4:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939023,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/4G HSS Service S6a,SANTSDM-1912,,Manual,TODO,,,,,,,,,,
HSS_Notification_NOR,SANTSDM-1831,8939022,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,11/Jul/25 6:50 PM,,,,EPC-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,UpdateLocation,"SUMMARY : Triggering the NOR procedure after the MME allocates a PDN GW IP address for a subscriber

Verify that the DSU procedure can be triggered when the subscriber data is deleted.

PRECONDITIONS : The HSS and the neighboring NEs operate properly.

The subscriber is defined and provided with the EPS service. In the registered APN information, the PDN GW IP addresses are dynamically allocated.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1831,,,,,,,,software,,,,,,"[{""id"":3844549,""index"":1,""fields"":{""Action"":""\""The subscriber originates Update location on the MME, and then the MME sends a ULR message to the HSS.\n\nThe MME dynamically allocates a PDN GW IP address for the subscriber.\n\nLog in to the Provisioning Gateway client, and query the dynamic information about the subscriber.\n\"""",""Data"":"""",""Expected Result"":""\""The Update location of the subscriber is successful and the HSS successfully returns a ULA message containing the subscriber data to the MME.\n\nThe PDN GW IP address is successfully allocated and the MME sends a NOR message to notify the HSS to allocate a PDN GW IP address for the subscriber. The HSS returns a NOA message to the MME and updates the PDN GW IP address in the database.\n\nThe command output contains all the PDN GW IP addresses that are dynamically allocated for the APNs of the subscriber.\n\""""},""attachments"":[],""testVersionId"":1102725},{""id"":3844550,""index"":2,""fields"":{""Action"":""\""Messsages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\t1\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t2\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tUpdate-Location-Answer\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t3\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tNotify-Request\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t4\n\t\t\t\n\t\t\t\n\t\t\tMMEHSS\n\t\t\t\n\t\t\t\n\t\t\tL3\n\t\t\t\n\t\t\t\n\t\t\tNotify-Answer\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102725}]",,,,,,,,,,,,,,,"0|j1qcmw:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939022,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/4G HSS Service S6a,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
IMS_UAR,SANTSDM-1830,8939021,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,Manual,NonFunctional,Regression,Security,System,,,,"SUMMARY : Location Management Procedure

Re-registration

The purpose of this test is to verify the UE can re-register successful

PRECONDITIONS : IMS Network Equipment is working in normal operation

there is only one HSS in the IMS net, and HSS address is configured at the I-CSCF

User A is registered.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1830,,,,,,,,software,,,,,,"[{""id"":3844548,""index"":1,""fields"":{""Action"":""\""\n\tUse Signal Trace to track the Cx interface\n\tUser A start to send re-registration message at the UE\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tI-CSCF received P-CSCFs message, and send UAR(Cx-Query) to HSS, and query S-CSCFs name；\n\tUser-Authorization-Type in UAR message is REGISTRATION;\n\tthe HSS shall return the stored S-CSCF name and the Experimental-Result-Code set to DIAMETER_SUBSEQUENT_REGISTRATION；\n\tI-CSCF send re-registration message to the S-CSCF\n\n\""""},""attachments"":[],""testVersionId"":1102724}]",,,,,,,,,,,,,,,"0|j1qcmo:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939021,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service Cx,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
IMS_LIR,SANTSDM-1829,8939020,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : Conversation setup process

Called user is registered

The purpose of this test is to verify that if the called subscriber is registered in HSS, HSS can send the subscribers current S-CSCF name to I-CSCF by LIA message.

PRECONDITIONS : 
	IMS Network Equipment is working Normal;
	There is only one HSS in the IMS net, and HSS address is configured at the I-CSCF;
	User A is registered;
	User B is registered, and S-CSCFs name is S-CSCF1.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1829,,,,,,,,software,,,,,,"[{""id"":3844547,""index"":1,""fields"":{""Action"":""\""\n\tUse Signal Trace to track the Cx interface;\n\tUser A call user B;\n\tCall request transmit to I-CSCF;\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tI-CSCF received INVITE message from S-CSCF, then send LIR (Cx-Query) to the HSS for query called user Bs S-CSCF;\n\tHSS answer LIA(Cx-Query Response) message, Result-Code is DIAMETER_SUCCESS, and the Server-Name AVP included S-CSCF1s name, Server-Capabilities AVP is not exist in the message;\n\tI-CSCF transmit INVITE message to the S-CSCF1.\n\n\""""},""attachments"":[],""testVersionId"":1102723}]",,,,,,,,,,,,,,,"0|j1qcmg:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939020,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service Cx,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
IMS_MAR,SANTSDM-1828,8939019,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:47 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,Manual,NonFunctional,Regression,Security,System,,,,"SUMMARY : IMS Multimedia Authentication Request

Test IMS Multimedia Authentication Request

PRECONDITIONS : 
	IMS Network Equipment is working in normal operation;
	there is only one HSS in the IMS net, and HSS address is configured at the I-CSCF;
	User A has S-CSCF name in the HSS;
	User A is not registered.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1828,,,,,,,,software,,,,,,"[{""id"":3844546,""index"":1,""fields"":{""Action"":""\""\n\tUse Signal Trace to track the Cx interface;\n\tUE start to send first registration message.\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tI-CSCF received P-CSCFs registered message, and send UAR(Cx-Query) to HSS, and query S-CSCFs capabilities;\n\tUser-Authorization-Type in UAR message is REGISTRATION;\n\tHSS answer UAA(Cx-Query Response) message, the message has Server-Capabilities AVP, which enables the I-CSCF to select an S-CSCF.;\n\tUAAs Result-Code is DIAMETER_FIRST_REGISTRATION;\n\tI-CSCF send registered message to the selected S-CSCF;\n\tS-CSCF received Registered Request from I-CSCF, then send MAR(Cx-Query) to HSS for download Authentication Vector;\n\tHSS answer MAA (Cx-Query Response) answer, and Result-Code is DIAMETER_SUCCESS, Authentication Vector is in the SIP-Auth-Data-Item AVP, and the number of authentication vectors in the SIP-Number-Auth-Items specified. At the same time, S-CSCF name in the MAR message is stored in the HSS;\n\tAccording to the Authentication Vector from the HSS, S-CSCF answered 401, and approve user\n\n\""""},""attachments"":[],""testVersionId"":1102722}]",,,,,,,,,,,,,,,"0|j1qcm8:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939019,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service Cx,SANTSDM-1912,,Manual,TODO,,,,,,,,,,
IMS_PPR,SANTSDM-1827,8939018,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : Location Management Procedure

HSS send user subscriber data to S-CSCF

The purpose of this test is to verify that if the registered subscribers data is changed in HSS, it can be send to S-CSCF by PPR message.

PRECONDITIONS : 
	IMS Network Equipment is working in normal operation;
	There is only one HSS in the IMS net, and HSS address is configured at the S-CSCF;
	User A is registered;",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1827,,,,,,,,software,,,,,,"[{""id"":3844545,""index"":1,""fields"":{""Action"":""\""\n\tUse Signal Trace to track the Cx interface of S-CSCF;\n\tModify user subscriber data in the HSS, e.g. modify user subscriber SP, Trigger HSS send PPR update requests to S-CSCF.\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tS-CSCF update user data successful, answer PPA, and Result-Code is DIAMETER_SUCCESS.\n\n\""""},""attachments"":[],""testVersionId"":1102721}]",,,,,,,,,,,,,,,"0|j1qcm0:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939018,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service Cx,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
IMS_SAR,SANTSDM-1826,8939017,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,Manual,NonFunctional,Regression,Security,System,,,,"SUMMARY : Location Management Procedure

Registration

The purpose of this test is to verify the UE can register successful

PRECONDITIONS : 
	IMS Network Equipment is working Normal;
	there is only one HSS in the IMS net, and HSS address is configured at the I-CSCF",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1826,,,,,,,,software,,,,,,"[{""id"":3844544,""index"":1,""fields"":{""Action"":""\""\n\tUse Signal Trace to track the Cx interface;\n\tUser A start to send registration message at the UE\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tI-CSCF received P-CSCFs registered message, and send UAR(Cx-Query) to HSS, and query S-CSCFs capabilities;\n\tUser-Authorization-Type in UAR message is REGISTRATION;\n\tHSS answer UAA(Cx-Query Response) message, the message has Server-Capabilities AVP, which enables the I-CSCF to select an S-CSCF.;\n\tUAAs Result-Code is DIAMETER_FIRST_REGISTRATION;\n\tI-CSCF send registered message to the selected S-CSCF\n\tS-CSCF send SAR(Cx-Query) to HSS, In the SAR Server Assignment Type AVP is RE_REGISTRATION;\n\tHSS answer SAA (Cx-Query Response),and Result-Code is DIAMETER_SUCCESS；\n\tAfter S-CSCF received successful SAA, Return to the user 200 OK response, User Registration success.\n\n\""""},""attachments"":[],""testVersionId"":1102720}]",,,,,,,,,,,,,,,"0|j1qcls:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939017,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service Cx,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
IMS HSS Early IMS authentication,SANTSDM-1825,8939016,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:47 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,Manual,NonFunctional,Regression,Security,System,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1825,,,,,,,,software,,,,,,[],,,,,,,,,,,,,,,"0|j1qclk:",,,,,,,,,,,,,,0.0,,,,,,,,,,,"{""issueId"":8939016,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service Sh,SANTSDM-1912,,Manual,TODO,,,,,,,,,,
IMS HSS HTTP Digest authentication,SANTSDM-1824,8939015,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:47 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,Manual,NonFunctional,Regression,Security,System,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1824,,,,,,,,software,,,,,,[],,,,,,,,,,,,,,,"0|j1qclc:",,,,,,,,,,,,,,0.0,,,,,,,,,,,"{""issueId"":8939015,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service Sh,SANTSDM-1912,,Manual,TODO,,,,,,,,,,
IMS HSS SIP Digest authentication,SANTSDM-1823,8939014,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:47 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,Manual,NonFunctional,Regression,Security,System,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1823,,,,,,,,software,,,,,,[],,,,,,,,,,,,,,,"0|j1qcl4:",,,,,,,,,,,,,,0.0,,,,,,,,,,,"{""issueId"":8939014,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service Sh,SANTSDM-1912,,Manual,TODO,,,,,,,,,,
IMS HSS SIM-USIM authentication,SANTSDM-1822,8939013,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:47 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,Manual,NonFunctional,Regression,Security,System,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1822,,,,,,,,software,,,,,,[],,,,,,,,,,,,,,,"0|j1qckw:",,,,,,,,,,,,,,0.0,,,,,,,,,,,"{""issueId"":8939013,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service Sh,SANTSDM-1912,,Manual,TODO,,,,,,,,,,
IMS HSS T-ADS UE no registered,SANTSDM-1821,8939012,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1821,,,,,,,,software,,,,,,"[{""id"":3844543,""index"":1,""fields"":{""Action"":""\""- The UE is not registered in MME-SGSN\n- The HSS receive from SCC-AS an UDR message requestingT-ADS information\n\"""",""Data"":"""",""Expected Result"":""\""- The HSS does not send neither S6a IDR to MME or MAP PSI to SGSN\n- The HSS return an UDA message withIMSVoiceOverPSSessionSupportset to IMS-VOICE-OVER-PS-SUPPORT-UNKNOWN\n\""""},""attachments"":[],""testVersionId"":1102715}]",,,,,,,,,,,,,,,"0|j1qcko:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939012,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
IMS HSS T-ADS MME no support,SANTSDM-1820,8939011,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1820,,,,,,,,software,,,,,,"[{""id"":3844541,""index"":1,""fields"":{""Action"":""\""- The UE is registered in MME only\n- The HSS receive from SCC-AS an UDR message requesting T-ADS information\n- T-ADS information for the UE is not present in the database\n\"""",""Data"":"""",""Expected Result"":""\""- The HSS send a S6a IDR message to the MME\n\""""},""attachments"":[],""testVersionId"":1102714},{""id"":3844542,""index"":2,""fields"":{""Action"":""\""- The MME responds with S6a IDA no supportIMS-VOICE-OVER-PS-NOT-SUPPORTED\n\"""",""Data"":"""",""Expected Result"":""\""- The HSS responds to SCC-AS an UDA message withIMS-VOICE-OVER-PS-NOT-SUPPORTED\n\""""},""attachments"":[],""testVersionId"":1102714}]",,,,,,,,,,,,,,,"0|j1qckg:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939011,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
IMS HSS T-ADS UE registered with support information,SANTSDM-1819,8939010,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,11/Jul/25 6:51 PM,,,,IMS-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1819,,,,,,,,software,,,,,,"[{""id"":3844539,""index"":1,""fields"":{""Action"":""\""- The UE registration S6a ULR contains theIMS-VOICE-OVER-PS-SUPPORT in T-ADS information\n- The HSS receive from SCC-AS an UDR message requesting T-ADS information\n\"""",""Data"":"""",""Expected Result"":""\""- The HSS does not send neither S6a IDR to MME or MAP PSI to SGSN- The HSS return an UDA message withIMSVoiceOverPSSessionSupportset to IMS-VOICE-OVER-PS-SUPPORT\n\""""},""attachments"":[],""testVersionId"":1102713},{""id"":3844540,""index"":2,""fields"":{""Action"":""\""- The UE registration MAP LocUpdatecontains theIMS-VOICE-OVER-PS-SUPPORT in T-ADS information\n- The HSS receive from SCC-AS an UDR message requesting T-ADS information\n\"""",""Data"":"""",""Expected Result"":""\""- The HSS does not send MAP PSI to SGSN\n- The HSS return an UDA message withIMSVoiceOverPSSessionSupportset to IMS-VOICE-OVER-PS-SUPPORT\n\""""},""attachments"":[],""testVersionId"":1102713}]",,,,,,,,,,,,,,,"0|j1qck8:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939010,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
IMS HSS T-ADS UE registered without support information,SANTSDM-1818,8939009,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,11/Jul/25 6:51 PM,,,,IMS-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1818,,,,,,,,software,,,,,,"[{""id"":3844537,""index"":1,""fields"":{""Action"":""\""- The UE registration S6a ULR does not contains theT-ADS information\n- The HSS receive from SCC-AS an UDR message requesting T-ADS information\n\"""",""Data"":"""",""Expected Result"":""\""- The HSSsendS6a IDR to MMEto retreive T-ADS information\n- The HSS return an UDA message with the T-ADS infortion towards the SCC-AS\n\""""},""attachments"":[],""testVersionId"":1102712},{""id"":3844538,""index"":2,""fields"":{""Action"":""\""- The UE registration MAP LocUpdatedoes not contains theT-ADS information\n- The HSS receive from SCC-AS an UDR message requesting T-ADS information\n\"""",""Data"":"""",""Expected Result"":""\""- The HSSsend a MAP PSIto retreive T-ADS information\n- The HSS return an UDA message with the T-ADS infortion towards the SCC-AS\n\""""},""attachments"":[],""testVersionId"":1102712}]",,,,,,,,,,,,,,,"0|j1qck0:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939009,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
IMS HSS Subscriber barring,SANTSDM-1817,8939008,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,Manual,NonFunctional,Regression,Security,System,,,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1817,,,,,,,,software,,,,,,"[{""id"":3844536,""index"":1,""fields"":{""Action"":""\""-The UE is provisionned with registration barring\n- The UE attempts a registration procedure and send a UAR message\n\"""",""Data"":"""",""Expected Result"":""\""- The HSS respond with a UAA message that containsthe error code DIAMETER_AUTHORIZATION_REJECTED\n\""""},""attachments"":[],""testVersionId"":1102711}]",,,,,,,,,,,,,,,"0|j1qcjs:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939008,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Subscriber barring,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
IMS HSS Registration restriction,SANTSDM-1816,8939007,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,Manual,NonFunctional,Regression,Security,System,,,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1816,,,,,,,,software,,,,,,"[{""id"":3844535,""index"":1,""fields"":{""Action"":""\""-The UE is provisionned with registration restriction\n- The UE attempts a registration procedure and send a UAR message\n\"""",""Data"":"""",""Expected Result"":""\""- The HSS respond with a UAA message that containsthe error code DIAMETER_AUTHORIZATION_REJECTED\n\""""},""attachments"":[],""testVersionId"":1102710}]",,,,,,,,,,,,,,,"0|j1qcjk:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939007,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Registration restriction,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
IMS HSS CS Domain Location Information Retrieval,SANTSDM-1815,8939006,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1815,,,,,,,,software,,,,,,"[{""id"":3844533,""index"":1,""fields"":{""Action"":""\""-The AS sends a UDR message carrying attribute-value pairs (AVPs), such as User-Identity and Data-Reference to the IMS-HSS, requesting the location information of the CS-PS domain subscriber.\n\"""",""Data"":"""",""Expected Result"":""\""-the IMS-HSS sends a MAP_ANY_TIME_INTERROGATION message to the HLR, requesting the location information of the CS-PS subscriber. In this message, the RequestedInfo information element (IE) indicates that the location information is requested, and the Subscriber Identity IE identifies the subscriber\n\""""},""attachments"":[],""testVersionId"":1102709},{""id"":3844534,""index"":2,""fields"":{""Action"":""\""-The HLR sends a MAP_ANY_TIME_INTERROGATION_ACK message to the IMS-HSS. In this message, the LocationInformation IE contains the location information\n\"""",""Data"":"""",""Expected Result"":""\""-The IMS-HSS sends a User Data Answer (UDA) message to the AS. In this message, the User-Data AVP contains the location information\n\""""},""attachments"":[],""testVersionId"":1102709}]",,,,,,,,,,,,,,,"0|j1qcjc:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939006,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS CS-PS Domain Location Information Retrieval,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
IMS HSS PS Domain Location Information Retrieval,SANTSDM-1814,8939005,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1814,,,,,,,,software,,,,,,"[{""id"":3844531,""index"":1,""fields"":{""Action"":""\""-The AS sends a UDR message carrying attribute-value pairs (AVPs), such as User-Identity and Data-Reference to the IMS-HSS, requesting the location information of the CS-PS domain subscriber.\n\"""",""Data"":"""",""Expected Result"":""\""- The IMS-HSS sends a UDR message carrying theseUser-Identity and Data-Reference to the IMS-HSS\n\""""},""attachments"":[],""testVersionId"":1102708},{""id"":3844532,""index"":2,""fields"":{""Action"":""\""-The IMS-HSSsendsa UDA message to the IMS-HSS. In this message, the User-Data AVP contains the location information\n\"""",""Data"":"""",""Expected Result"":""\""-The IMS-HSS sends a User Data Answer (UDA) message to the AS. In this message, the User-Data AVP contains the location information\n\""""},""attachments"":[],""testVersionId"":1102708}]",,,,,,,,,,,,,,,"0|j1qcj4:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939005,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS CS-PS Domain Location Information Retrieval,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
IMS HSS Alias IMPU Group PPR-PPA,SANTSDM-1813,8939004,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1813,,,,,,,,software,,,,,,"[{""id"":3844528,""index"":1,""fields"":{""Action"":""\""- The alias IMPU group data that the AS subscribes to is deleted\n\"""",""Data"":"""",""Expected Result"":""\""- The IMS-HSS leaves theUser-DataAVP empty in the PPR message sent to the S-CSCF\n- The AS acknowledge with a PPA message\n\""""},""attachments"":[],""testVersionId"":1102707},{""id"":3844529,""index"":2,""fields"":{""Action"":""\""-the alias IMPU group has been modified and the S-CSCF does not support the enhanced Alias IMPU Group feature\n\"""",""Data"":"""",""Expected Result"":""\""- The IMS-HSS sends a PPR message to notify the S-CSCFof the modification. The message carries the AVPs such asSupported-FeaturesAVP with the alias IMPU group ID in the PPR message\n- The AS acknowledge with a PNA message\n\""""},""attachments"":[],""testVersionId"":1102707},{""id"":3844530,""index"":3,""fields"":{""Action"":""\""- The alias IMPU group has been modified and the S-CSCF supports the enhanced Alias IMPU Group feature\n\"""",""Data"":"""",""Expected Result"":""\""- The IMS-HSS sends en PPR message to notify the S-CSCF of the modification. The message- carries the AVP such as\nSupported-FeaturesAVP with the alias IMPU group ID\nUser-DataAVP with theAliasIdentityGroupIDAVP of the IMPU.\n\""""},""attachments"":[],""testVersionId"":1102707}]",,,,,,,,,,,,,,,"0|j1qciw:",,,,,,,,,,,,,,3.0,,,,,,,,,,,"{""issueId"":8939004,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Alias IMPU Group,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
IMS HSS Shared iFC not supported by S-CSCF,SANTSDM-1812,8939003,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : None
PRECONDITIONS : Shared iFC ID are configured into IMS-HSS and S-CSCF",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1812,,,,,,,,software,,,,,,"[{""id"":3844527,""index"":1,""fields"":{""Action"":""\""- The S-CSCFsends an SAR message to the IMS-HSS.\n\"""",""Data"":"""",""Expected Result"":""\""- The IMS-HSS respond with a SAA that include the IFCs that are present in the Shared iFC\n\""""},""attachments"":[],""testVersionId"":1102706}]",,,,,,,,,,,,,,,"0|j1qcio:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939003,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Shared iFC,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
IMS HSS Shared iFC supported by S-CSCF,SANTSDM-1811,8939002,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,IMS-HSS,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : None
PRECONDITIONS : Shared iFC ID are configured into IMS-HSS and S-CSCF",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1811,,,,,,,,software,,,,,,"[{""id"":3844526,""index"":1,""fields"":{""Action"":""\""- The S-CSCFsends an SAR message to the IMS-HSS.\n\"""",""Data"":"""",""Expected Result"":""\""- The IMS-HSS respond with a SAA that include the unique ID of the Shared iFC\n\""""},""attachments"":[],""testVersionId"":1102705}]",,,,,,,,,,,,,,,"0|j1qcig:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939002,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Shared iFC,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
User data subscription service,SANTSDM-1810,8939001,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:37 PM,16/Jul/25 11:07 AM,,,,COMMON,,0,Manual,NonFunctional,Regression,Resiliency,System,,,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1810,,,,,,,,software,,,,,,"[{""id"":3844525,""index"":1,""fields"":{""Action"":""\""- Verify that when an entity that handle user data subscription is faulty, the service operation (querying, adding, deleting, updating) is transfered to working entity\n\"""",""Data"":"""",""Expected Result"":""\""- The data subscription service is correctly transfered to a proper working entity\n\""""},""attachments"":[],""testVersionId"":1102704}]",,,,,,,,,,,,,,,"0|j1qci8:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939001,""testStatuses"":[]}",,/SDM COMMON TESTCASES/RESILIENCY Testcases/Architecture reliability,SANTSDM-1910,,Manual,TODO,,,,,,,,,,
Redundancy deployment,SANTSDM-1809,8939000,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:37 PM,16/Jul/25 11:07 AM,,,,COMMON,,0,Manual,NonFunctional,Regression,Resiliency,System,,,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1809,,,,,,,,software,,,,,,"[{""id"":3844523,""index"":1,""fields"":{""Action"":""\""- For each type of nodes, verify that at least two nodes of the same type are deployed\n\"""",""Data"":"""",""Expected Result"":""\""- Nodes redundancy of each type are correctly deployed\n\""""},""attachments"":[],""testVersionId"":1102703},{""id"":3844524,""index"":2,""fields"":{""Action"":""\""- For each type of pods, verify that at least two pods of the same type are deployed\n\"""",""Data"":"""",""Expected Result"":""\""- pods redanduncy of each type are correctly deployed\n\""""},""attachments"":[],""testVersionId"":1102703}]",,,,,,,,,,,,,,,"0|j1qci0:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939000,""testStatuses"":[]}",,/SDM COMMON TESTCASES/RESILIENCY Testcases/Deployment reliability,SANTSDM-1910,,Manual,TODO,,,,,,,,,,
Anti-affinity deployment,SANTSDM-1808,8938999,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:37 PM,16/Jul/25 11:07 AM,,,,COMMON,,0,Manual,NonFunctional,Regression,Resiliency,System,,,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1808,,,,,,,,software,,,,,,"[{""id"":3844521,""index"":1,""fields"":{""Action"":""\""- Verify anti-affinity node deployment\n\"""",""Data"":"""",""Expected Result"":""\""- Nodes of the same type are deployed on diffeent hosts\n\""""},""attachments"":[],""testVersionId"":1102702},{""id"":3844522,""index"":2,""fields"":{""Action"":""\""- Verify anti-affinity poddeployment\n\"""",""Data"":"""",""Expected Result"":""\""- Pos that provide the same service functions are deployed on different nodes\n\""""},""attachments"":[],""testVersionId"":1102702}]",,,,,,,,,,,,,,,"0|j1qchs:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8938999,""testStatuses"":[]}",,/SDM COMMON TESTCASES/RESILIENCY Testcases/Deployment reliability,SANTSDM-1910,,Manual,TODO,,,,,,,,,,
Backup,SANTSDM-1807,8938998,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 2:11 PM,16/Jul/25 11:07 AM,,,,COMMON,,0,BackupRestore,Manual,NonFunctional,Regression,System,,,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1807,,,,,,,,software,,,,,,"[{""id"":3844519,""index"":1,""fields"":{""Action"":""\""- Verify that a full SDM data backup can be executed manually\n\"""",""Data"":"""",""Expected Result"":""\""- The manual backup operate with success\n\""""},""attachments"":[],""testVersionId"":1102701},{""id"":3844520,""index"":2,""fields"":{""Action"":""\""- Verify that a full SDM data backup can be executed automatically\n\"""",""Data"":"""",""Expected Result"":""\""- The automatic backup operate with success\n\""""},""attachments"":[],""testVersionId"":1102701}]",,,,,,,,,,,,,,,"0|j1qchk:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8938998,""testStatuses"":[]}",,/SDM COMMON TESTCASES/RESILIENCY Testcases/Data Reliability/Data backup and restore,SANTSDM-1898,,Manual,TODO,,,,,,,,,,
Restore,SANTSDM-1806,8938997,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 2:11 PM,16/Jul/25 11:07 AM,,,,COMMON,,0,BackupRestore,Manual,NonFunctional,Regression,System,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1806,,,,,,,,software,,,,,,[],,,,,,,,,,,,,,,"0|j1qchc:",,,,,,,,,,,,,,0.0,,,,,,,,,,,"{""issueId"":8938997,""testStatuses"":[]}",,/SDM COMMON TESTCASES/RESILIENCY Testcases/Data Reliability/Data backup and restore,SANTSDM-1898,,Manual,TODO,,,,,,,,,,
WHFD-410600 SCTP Diameter Multi-Homing,SANTSDM-1805,8938996,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,HUAWEI-SPECIFIC,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : Definition

The Stream Control Transmission Protocol (SCTP) Multi-Homing function allows multiple IP addresses to be configured for the two endpoints of one SCTP association. It provides a reliable end-to-end multi-path transmission mechanism for users of upper layer protocols.

On the IP networkthe SCTP multi-homing function is provided to improve the reliability of the IP network.



Technical Overview

On the IP network, multiple source and destination addresses can be configured for the IP links between the local and peer entities. The source addresses can be configured on the same pod or on different pods. In this way, multiple logical links and physical paths can be established. The source addresses and destination addresses are assigned different connection priorities and located in different network segments, and different network equipment can be connected to an SCTP association.

The SCTP Multi-Homing function allows one SCTP association to contain multiple IP links by configuring multiple reachable IP addresses on both the local end and peer end.

At present, the SCTP Multi-Homing function allows an SCTP association to contain two or four IP links. Two IP links, however, are recommended to ensure the stability of the interface.

After an SCTP association is established, you must define a primary IP link for each SCTP endpoint. The primary IP link is used to transmit SCTP packets in normal conditions. Using the SCTP Multi-Homing function, the SCTP endpoint at the receiving end checks whether the IP link transmitting the incoming SCTP packets belongs to an SCTP association before processing the SCTP packets.Figure 1shows the two IP links contained in an SCTP association when the dual planes of the SCTP multi-homing are enabled.

Figure 1IP links contained in an SCTP association






Generally, the primary IP link is established by using the first IP address in the IP address list of the local and peer ends. Data is transmitted over the primary IP link if the primary IP link functions properly. After the server receives the primitives through the SCTP association, it sets the source addresses and destination IP addresses of the primary IP link.

The IP link for transmitting data is selected based on the priority of available IP links. The primary IP link has the highest priority. The other IP links are selected in an ascending order based on the serial numbers of the local IP addresses.Table 1lists the priority configuration of the primary and secondary IP links.




	
	Table 1Priority configuration of the primary and secondary IP links

	
	
	
		
			
			Path
			
			
			Primary or Secondary
			
			
			Priority
			
		
	
	
		
			
			[IP1, IP3]
			
			
			Primary
			
			
			High
			
		
		
			
			[IP2, IP4]
			
			
			Secondary
			
			
			Low
			
		
	


The principles for selecting the IP links for transmitting data are as follows:


	
	When the current IP link is unavailable, the IP link with the highest priority is selected from the available IP links. If all IP links are unavailable, data transmission is interrupted.
	
	
	When an IP link with higher priority than the current IP link is available, the IP link with higher priority is selected.
	


PRECONDITIONS : Conditions


	The peer entity supports the SCTP multi-homing function.
	You understand theWHFD-410600 SCTP Multi-Homing Feature Descriptionfeature.
	You have data configuration rights.
	You have obtained the user name and password required for logging in to the PGW Web LMT.
	Two local entity IP addresses whoseAddress typeisIPV4have been added by runningADD IPADDR. (You can runLST IPADDRto query whether such IP addresses are configured.)


License Requirements

A license is required for this feature. The license control item is82202958 LKS9DIASCTPM01 Diameter SCTP multi-homing.

The license must be installed before this feature can be used.




	Table 1Required data
	
		
			
			Command
			
			
			Parameter
			
			
			Example Value
			
			
			Description
			
		
	
	
		
			
			ADD DMLE
			
			
			Entity name
			
			
			HSS01
			
			
			Planned by the carrier after considering all site entities.
			
		
		
			
			Local device type
			
			
			IMS-HSS(IMS-HSS)
			
			
			Planned by the carrier after considering all site entities.
			
		
		
			
			Realm name
			
			
			huawei.com
			
			
			Consistent with that configured on the peer.
			
		
		
			
			Host name
			
			
			HSS01.huawei.com
			
			
			Consistent with that configured on the peer.
			
		
		
			
			ADD DMPE
			
			
			Entity name
			
			
			AS01
			
			
			Planned by the carrier after considering all site entities.
			
		
		
			
			Diameter peer device type
			
			
			AS(AS)
			
			
			Consistent with that configured on the peer.
			
		
		
			
			Host name
			
			
			AS01.huawei.com
			
			
			Consistent with that configured on the peer.
			
		
		
			
			Domain name
			
			
			huawei.com
			
			
			Consistent with that configured on the peer.
			
		
		
			
			Diameter peer entity priority
			
			
			15
			
			
			Consistent with that configured on the peer.
			
		
		
			
			SET DMPECAP
			
			
			Entity name
			
			
			AS01
			
			
			This parameter is planned by the carrier after considering all site entities. It must be set to the same value as that on the peer NE.
			
		
		
			
			Operation type
			
			
			ADD
			
			
			Fixed value.
			
		
		
			
			Diameter peer device type
			
			
			AS
			
			
			This parameter is planned by the carrier by considering all site entities. Set this parameter to the value ofDiameter peer device typeinADD DMPE.
			
		
		
			
			Data type for Sh-pull
			
			
			SELECT ALL
			
			
			Specifies the type of the data that the AS requests to read from the HSS.
			
		
		
			
			Data type for Sh-update
			
			
			SELECT ALL
			
			
			Specifies the type of the data that the AS requests to update.
			
		
		
			
			Data type for Sh-sub-notify
			
			
			SELECT ALL
			
			
			Specifies the type of the data that the AS requests to subscribe to HSS.
			
		
		
			
			Sh Featurelist
			
			
			Notif-Eff-1
			
			
			Specifies the feature list that the AS supports.
			
		
		
			
			Host name
			
			
			AS01.huawei.com
			
			
			This parameter is planned by the carrier by considering all site entities. Set this parameter to the value ofHost nameinADD DMPE.
			
		
		
			
			Domain name
			
			
			huawei.com
			
			
			This parameter is planned by the carrier by considering all site entities. Set this parameter to the value ofDomain nameinADD DMPE.
			
		
		
			
			ADD DMLKS
			
			
			Link set name
			
			
			HSS01_AS01
			
			
			Planned by the carrier after considering all site entities.
			
		
		
			
			Diameter local entity name
			
			
			HSS01
			
			
			This parameter must be defined by runningADD DMLEbefore being used.
			
		
		
			
			Diameter peer entity name
			
			
			AS01
			
			
			This parameter must be defined by runningADD DMPEbefore being used.
			
		
		
			
			Link selection mode
			
			
			POLLING
			
			
			Planned by the carrier after considering all site entities.
			
		
		
			
			ADD DMLNK
			
			
			Link name
			
			
			AS1_1
			
			
			Planned by the carrier after considering all site entities.

			Specifies the name of a link between a local Diameter entity and its peer Diameter entity.
			
		
		
			
			Diameter links name
			
			
			AS1
			
			
			Planned by the carrier after considering all site entities.

			Specifies the name of a linkset.
			
		
		
			
			HSF module number
			
			
			131
			
			
			This parameter must be defined by runningADD MODULEbefore being used.
			
		
		
			
			Transmission protocol type
			
			
			SCTP
			
			
			Specifies the transmission protocol used by a link between a local Diameter entity and its peer Diameter entity.
			
		
		
			
			Work mode
			
			
			
				CLIENT(Client)
				SERVER(Server)
				SERVERMULTPORT(Server Multi-port)
			
			
			
			Specifies the work mode of the local entity.
			
		
		
			
			IP Address name1
			
			
			*************
			
			
			This parameter must be defined by runningADD IPADDRbefore being used.
			
		
		
			
			IP Address name2
			
			
			*************
			
			
			This parameter must be defined by runningADD IPADDRbefore being used.
			
		
		
			
			Local port
			
			
			8000
			
			
			Planned by the carrier after considering all site entities.
			
		
		
			
			Peer IP version
			
			
			IPV4(IPV4)
			
			
			Planned by the carrier after considering all site entities.
			
		
		
			
			Peer IPV4 address
			
			
			*************
			
			
			Planned by the carrier after considering all site entities.

			The setting of this parameter must be the same as the IPv4 address of the peer Diameter entity.
			
		
		
			
			Standby peer IP version
			
			
			IPV4(IPV4)
			
			
			Planned by the carrier after considering all site entities.
			
		
		
			
			Standby peer IPV4 address
			
			
			*************
			
			
			Planned by the carrier after considering all site entities.

			The setting of this parameter must be the same as the IPv4 address of the peer Diameter entity.
			
		
		
			
			Peer port
			
			
			5082
			
			
			The setting of this parameter must be the same as the port number of the peer Diameter entity.
			
		
		
			
			Diameter link priority
			
			
			15
			
			
			Planned by the carrier after considering all site entities.
			
		
		
			
			Cross path flag
			
			
			NO(No)
			
			
			The cross path function is disabled by default. To use the cross path function, make sure that the peer Diameter entity supports the cross path and that the data configuration is consistent between the local and peer Diameter entities.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1805,,,,,,,,software,,,,,,"[{""id"":3844514,""index"":1,""fields"":{""Action"":""\""Open theUDM MML command page.\n\nRunADD DMLEto add a local Diameter entity.\n\n\n\"""",""Data"":"""",""Expected Result"":""\""Example :\n\n/*---------Open the UDM MML command page.----------*/ /*Add a local Diameter entity.*/ ADD DMLE: EN\u003d\""HSS01\"", LOCTYPE\u003dIMS-HSS, DN\u003d\""huawei.com\"", HN\u003d\""HSS01.huawei.com\"";\n\""""},""attachments"":[],""testVersionId"":1102699},{""id"":3844515,""index"":2,""fields"":{""Action"":""\""RunADD DMPEto add a peer Diameter entity.\n\"""",""Data"":"""",""Expected Result"":""\""/*Add a peer Diameter entity.*/ ADD DMPE: EN\u003d\""AS01\"", PEERTYPE\u003dAS, HN\u003d\""AS01.huawei.com\"", DN\u003d\""huawei.com\"", PEPRI\u003d15;\n\""""},""attachments"":[],""testVersionId"":1102699},{""id"":3844516,""index"":3,""fields"":{""Action"":""\""Run theSET DMPECAPto authorize the AS to manage repository data and alias repository data in the IMS-HSS.\n\"""",""Data"":"""",""Expected Result"":""\""/*Authorize the AS to manage repository data and alias repository data in the IMS-HSS.*/ SET DMPECAP: PEERNAME\u003d\""AS01\"", OPTYPE\u003dADD, PEERTYPE\u003dAS, PR\u003dREPOSITORYDATA-1IMSPUBLICIDENTITY-1IMSUSERSTATE-1IFC-1LOCATIONINFORMATION-1USERSTATE-1CHARGINGINFORMATION-1MSISDN-1PSIACTIVATION-1ALIASREPOSITORY-1, UR\u003dREPOSITORYDATA-1PSIACTIVATION-1ALIASREPOSITORY-1, NR\u003dREPOSITORYDATA-1IMSUSERSTATE-1SCSCFNAME-1IFC-1PSIACTIVATION-1ALIASREPOSITORY-1IMSPUBLICIDENTITY-1, ASFEATURELIST\u003dNotif-Eff-1, HN\u003d\""AS01.huawei.com\"", DN\u003d\""huawei.com\"";\n\""""},""attachments"":[],""testVersionId"":1102699},{""id"":3844517,""index"":4,""fields"":{""Action"":""\""RunADD DMLKSto add a Diameter linkset.\n\"""",""Data"":"""",""Expected Result"":""\""/*Add a Diameter linkset.*/ ADD DMLKS: LINKSNAM\u003d\""HSS01_AS01\"", LOCALNAM\u003d\""HSS01\"", PEERNAM\u003d\""AS01\"", LNKMOD\u003dPOLLING;\n\""""},""attachments"":[],""testVersionId"":1102699},{""id"":3844518,""index"":5,""fields"":{""Action"":""\""RunADD DMLNKto add a Diameter link and configure the SCTP Multi-Homing function.\n\n\n\"""",""Data"":"""",""Expected Result"":""\""/*Add a Diameter link and configure the SCTP Multi-Homing function.*/ ADD DMLNK: LNKNAME\u003d\""AS_1\"", LINKSNAM\u003d\""AS1\"", HSFMID\u003d131, PTYPE\u003dSCTP, WMODE\u003dCLIENT, IPNAM1\u003d\""*************\"", IPNAM2\u003d\""*************\"", LPT\u003d8000, PIPVER\u003dIPV4, PIPV4\u003d\""*************\"", SPIPVER\u003dIPV4, SPIPV4\u003d\""*************\"", PPT\u003d5082, LINKPRI\u003d15, CROSSPATHFLAG\u003dNO;\n\""""},""attachments"":[],""testVersionId"":1102699}]",,,,,,,,,,,,,,,"0|j1qch4:",,,,,,,,,,,,,,5.0,,,,,,,,,,,"{""issueId"":8938996,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
WHFD-410103 HLR IMSI Replacing,SANTSDM-1804,8938995,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,HUAWEI-SPECIFIC,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : IMSI Replacing

Function : This item controls the number of subscribers provided with the IMSI Replacement service by the HSS/HLR.

implementation : When one subscriber is provided with the IMSI Replacement service, the number of IMSI Replacement subscribers is incremented by 1. When one subscriber is deregistered from the IMSI Replacement service, the number of IMSI Replacement subscribers is decremented by 1.

application : GSM/UMTS/EPS",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1804,,,,,,,,software,,,,,,[],,,,,,,,,,,,,,,"0|j1qcgw:",,,,,,,,,,,,,,0.0,,,,,,,,,,,"{""issueId"":8938995,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
WHFD-108000 Flexible Subscriber Data Export and Count Feature Description,SANTSDM-1803,8938994,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 2:11 PM,16/Jul/25 11:07 AM,,,,HUAWEI-SPECIFIC,,0,BackupRestore,Manual,NonFunctional,Regression,System,,,,"SUMMARY : Relevant NFs

HLR/HSS and UDM

Definition

The Flexible Subscriber Data Export and Count feature is implemented on theDMAmodule. With the data export function, you can export subscriber data from the HLR in-memory database according to the predefined criteria, and the exported data can be converted into the specified format and then stored to a specified location. With the data count function, you can count the number of subscribers in the HLR in-memory database according to the predefined criteria.

You can create, modify, delete, or view subscriber data export or count templates on the WebUI. The criteria for exporting subscriber data or counting the number of subscribers can be customized in the templates as required.

Subscriber data can be exported in Huawei-proprietary or CSV format. If the Huawei-proprietary format is used, subscriber data is exported using the Huaweis subscriber model. If the CSV format is used, subscriber data is exported by line. The exported data files are stored to the local directory, FTP server, or SFTP server.

Benefits

This feature enables carriers to:


	Create, modify, delete, or view subscriber data flexible export or count templates.
	Compare the subscriber data on the USCDB with the subscriber data on the provisioning system to identify potentially fraudulent subscribers.
	Export subscriber data for operation analysis. For example, export subscriber data for analyzing subscriber behavior before service promotion and subscriber growth prediction.
	Analyze subscriber data and search for statistical analysis results based on various criteria.
	Export subscriber data to a third-party device for backup.
	Export subscriber data during system swap-out.


PRECONDITIONS : License Requirements

Licenses are required for this feature. The license control items are ""82203676 LKQ7FOUT02 Flexible subscriber data export and count"" and ""82301222 LMI5EXPUSR01 Number of Subscribers Data Export to Text File,Per Subs"".

The licenses must be installed before this feature can be used.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1803,,,,,,,,software,,,,,,"[{""id"":3844510,""index"":1,""fields"":{""Action"":""\""\n\n\n\tAn operator creates a count or export template on the DMS WebUI.\n\tThe DMS WebUI sends the template configuration data to the active PGW.\n\tThe active PGW verifies the template configuration data.\n\tThe active PGW sends the DRU a template configuration request for inserting global data to the DRU.\n\tThe DRU sends the template to the active PGW.\n\tThe active PGW sends the template to the DMS WebUI.\n\tThe DMS WebUI displays the template to the operator.\n\n\"""",""Data"":"""",""Expected Result"":""\""Template Export or Count iscreated\n\nAn operator can open theMML command page of the PGW Web LMT, and runDSP EXPTASKto check the progress of a subscriber data export task.\n\""""},""attachments"":[],""testVersionId"":1102697},{""id"":3844511,""index"":2,""fields"":{""Action"":""\""\n\n\n\tAn operator runsADD EXPTASKon the PGW Web LMT.\n\tThe active PGW module checks whether another task is running. If another task is running, the active PGW module ends the service procedure. If no other task is running, the active PGW module proceeds to the next step.\n\tThe active PGW sends a start success or failure response to the operator.\n\tThe active PGW sends the DMA a message for starting the export task.\n\tThe DMA sends a response message to the active PGW, indicating that the data has been exported.\n\tThe active PGW returns the export results to the operator.\n\n\n\n\"""",""Data"":"""",""Expected Result"":""\""Data exported\n\nAn operator can open theMML command page of the PGW Web LMT, and runDSP EXPTASKto check the progress of a subscriber data export task.\n\""""},""attachments"":[],""testVersionId"":1102697},{""id"":3844512,""index"":3,""fields"":{""Action"":""\""None\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102697},{""id"":3844513,""index"":4,""fields"":{""Action"":""\""\n\n\n\tThe operator runsADD EXPTASKon the PGW Web LMT to create a scheduled or periodic data export task.\n\n\tThe scheduled or periodic data export task, after created, will be stored on the global DRU. When the start time reaches, the active PGW starts the task.\n\t\n\tThe active PGW module checks whether another task is running. If another task is running, the active PGW module ends the service procedure. If no other task is running, the active PGW module proceeds to the next step.\n\tThe active PGW sends the DMA a message for starting the export task.\n\tThe DMA sends a response message to the active PGW, indicating that the data has been exported.\n\tThe active PGW returns the export results to the operator.\n\n\"""",""Data"":"""",""Expected Result"":""\""Data exported\n\nAn operator can open theMML command page of the PGW Web LMT, and runDSP EXPTASKto check the progress of a subscriber data export task.\n\""""},""attachments"":[],""testVersionId"":1102697}]",,,,,,,,,,,,,,,"0|j1qcgo:",,,,,,,,,,,,,,4.0,,,,,,,,,,,"{""issueId"":8938994,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES,SANTSDM-1898,,Manual,TODO,,,,,,,,,,
WHFD-410301 Supporting CSFB,SANTSDM-1802,8938993,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:08 PM,16/Jul/25 11:07 AM,,,,HUAWEI-SPECIFIC,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : The Circuit Switched Fallback (CS Fallback) in Evolved Packet System (EPS) feature enables carriers to reuse the circuit switched (CS) infrastructure to provide voice services for the subscribers who are attached to both the EPS and CS domain.

Application Scenarios
This feature applies to GSM/UMTS/EPS convergent networks.

The LTE/system architecture evolution (SAE) network is an all-IP flat network and relies on the IMS to provide voice services. At the initial phase of LTE/SAE, carriers may express a preference to reuse the CS infrastructure to provide voice services, partly because IMS voice services still have some technical issues to address, for example, deployment issues and charging accuracy. Carriers' choice to retain the legacy CS domain is also driven by the demand for getting the most out of CS investments and by LTE/SAE deployment policies. The CS Fallback feature is the ideal choice for carriers who are not willing to rely on the IMS to provide voice services in the LTE network. With the CS Fallback feature, subscribers in the LTE network can be reverted to the CS domain when they require voice services.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1802,,,,,,,,software,,,,,,[],,,,,,,,,,,,,,,"0|j1qcgg:",,,,,,,,,,,,,,0.0,,,,,,,,,,,"{""issueId"":8938993,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
WHFD-410302 Supporting SRVCC,SANTSDM-1801,8938992,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:45 PM,16/Jul/25 11:07 AM,,,,HUAWEI-SPECIFIC,,0,CIS,Manual,NonFunctional,Regression,Security,System,,,"SUMMARY : Refer to Feature guide , HSS9860 Features (WHFD-410302 Supporting SRVCC) for more details

Relevant NFs

IMS-HSS and SAE-HSS ( EPC-HSS)

Definition

The Supporting Single Radio Voice Call Continuity (SRVCC) feature allows subscribers to continue their ongoing calls when they roam from an LTE network to a GSM or UMTS network.

Figure 1illustrates the SRVCC feature implementation.

Figure 1SRVCC feature implementation


The enhanced SRVCC (eSRVCC) is introduced to shorten the voice interruption caused by SRVCC handovers. In the eSRVCC network architecture, the access transfer control function (ATCF)/access transfer gateway (ATGW) is added between the proxy-call session control function (P-CSCF) and the interrogating-CSCF (I-CSCF)/serving-CSCF (S-CSCF) to shorten the voice interruption.

Figure 2illustrates the eSRVCC implementation.

Figure 2eSRVCC implementation



Implementation Principle

SRVCC Handover

Figure 6illustrates the principle for the SRVCC handover.







Figure 6Principle for the SRVCC handover




	Table 6Principle for the SRVCC handover
	
		
			
			Step
			
			
			Procedure
			
			
			Description
			
		
	
	
		
			1
			
			UE sends network measurement reports
			
			
			In anareawhere the LTE co-exists with the GSM or UMTS, a UE sends network measurement reports to the eNodeB. Based on the reports, the eNodeB determines whether to initiate an SRVCC handover.
			
		
		
			2
			
			eNodeB sends a handover request
			
			
			If an SRVCC handover is required, the eNodeB sends a handover request to the MME.
			
		
		
			3
			
			The MME separates voice bearers from non-voice bearers based onQoSclassidentifier(QCI) 1 associated with voice bearers. The MME selects an SRVCC IWF based on the Target ID in the message and sends a PS to CS handover request to the selected SRVCC IWF over the Sv interface. The message contains the UEs STN-SR number allocated by the ATCF.
			
		
		
			4
			
			SRVCC IWF initiates a handover procedure
			
			
			The SRVCC IWF identifies the target MSC server by the Target ID in the message. Then the SRVCC IWF initiates a handover procedure towards the target MSC server.
			
		
		
			5
			
			SRVCC IWF establishes bearers
			
			
			After the target GSM and UMTS bearer network is established:

			
				The SRVCC IWF establishes bearers to the ATCF/ATGW based on the STN-SR.
				The ATCF identifies the session to be handed over based on the C-MSISDN, updates bearer information on the ATGW, switches the local media plane to the GSM/UMTS bearer, and instructs the SCC AS to update the information about the UEaccess network.
			
			
		
	






PRECONDITIONS : 
Table 2describes the requirements for supporting SRVCC.



	Table 2Requirements for supporting SRVCC
	
		
			
			NE
			
			
			Version
			
			
			License Requirements
			
			
			Function Description
			
		
	
	
		
			
			IMS-HSS/SAE-HSS
			
			
			HSS9860 V900R008C00 or later
			
			
			This is an optional feature provided by the HSS9860. The license control itemSupport SRVCCis required for this feature.
			
			
			The IMS-HSS and SAE-HSS store subscribers subscription information including subscriber numbers and servicesubscription data.
			
		
		
			
			MME
			
			
			USN9810 V900R001C05 or later
			
			
			This is an optional feature provided by the USN9810. The license control itemVoLTE Entry Featureis required for this feature.
			
			
			The MME receives handover requests from eNodeBs and sends voice handover requests to the SRVCCinterworkingfunction (IWF).
			
		
		
			
			SBC
			
			
			SE2600 V200R009C00 or later
			
			
			This is an optional feature provided by the SE2600. The license control itemATCF/ATGW (per concurrent session)is required for this feature.
			
			
			The SBC serves as an ingress node to the visited IMS network. It provides the P-CSCF function and forwards messages between UEs and their home networks.

			The SBC supports ATCF/ATGW and performs the following operations during eSRVCC handovers:

			
				During registration, the SBC allocatessession transfer numbers for SRVCC (STN-SRs)and storesaccess transfer update-session transfer identifiers (ATU-STIs)andcorrelation MSISDNs (C-MSISDNs)that are sent from the service centralization and continuity application server (SCC AS).
				During calls, the SBC determines whether to anchor calls on the media plane based on carrier policies andUEcapabilities.
				When a subscriber moves between networks, the SBC associates the handover request initiated by the SRVCC IWF with the anchored session, updates session bearer information, and sends the handover request to the SCC AS.
			
			
		
		
			
			SE2900 V300R001C10 or later
			
			
			This is an optional feature provided by the SE2900. The license control itemsP-CSCF(per concurrent session)andATCF/ATGW(per concurrent session)are required for this feature.
			
		
		
			
			SCC AS
			
			
			ATS9900 V100R003C02 or later
			
			
			This is an optional feature provided by the ATS9900. The license control itemResource,ATS9900,LSF4SCCAS01,SCC AS,Base on per subis required for this feature.
			
			
			The SCC AS completes theSessionTransfer procedure and updates MS media information.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1801,,,,,,,,software,,,,,,"[{""id"":3844508,""index"":1,""fields"":{""Action"":""\""Configuration\n\nConfiguring the SRVCC Data on the Convergent HLR/IMS-HSS/SAE-HSS\n\nConfigure data on the SAE-HSS ( EPC-HSS)\n\n\n\tOpen theHSS9860 Service Managementpage.\n\tRunADD NCCTPLto configure the SAE-HSS to deliver STN-SR to peer entities.\n\tRunMOD STNSRto configure the STN-SR to which aUEhas subscribed.\n\n\nConfigure data on the IMS-HSS.\n\n   4.Open theIMS-HSS Service Managementpage.\n\n\n\nNote\n\nThe following data enables the IMS-HSS to configure the ATCF domain name, the STN-SR allocated by the SCC AS, the ATU-STI, and the handover indication of the second session as a PSI subscriber. When the I-CSCF receives a handover request, it queries PSI data on the IMS-HSS for the SCC AS address and forwards the handover request to the SCC AS.\n\n    5. RunADD HPSIto configure the ATCF domain name, the STN-SR allocated by the SCC AS, the ATU-STI, and the handover indication of the second session as a PSI subscriber.\n\nIf the ATCF is not deployed in the local network, the ATCF domain name must be configured as a PSI subscriber.\n\n    6. RunSET HASto configure the AS address to which the ATCF domain name, the STN-SR, the ATU-STI, and the handover indication of the second session maps.\n\n\n\n\nExample\n\nTask Description\n\nProvide the eSRVCC service to retainvoice call continuityfor subscribers who roam from an E-UTRAN to a UTRAN/GERAN.\n\nScripts\n\n/*-------------Open theHSS9860 Service Managementpage.-------------*/\n\n/*Configure the HSS9860 to deliver STN-SR to peer entities.*/\n\nADD NCCTPL: HLRSN\u003d1, TPLID\u003d1, SUPPSTNSR\u003dSR;\n\n/*Configure the subscribed STN-SR.*/\n\nMOD STNSR: IMSI\u003d\""460900707550901\"", PROV\u003dTRUE, STNSR\u003d\""888666\"";\n\n/*----------Open theIMS-HSS Service Managementpage.----------*/\n\n/*Configure the STN-SR, ATCF, ATU-STI, and handover indication of the second session as a PSI subscriber.*/\n\nADD HPSI: PISI\u003d\""999666\"", PUSI\u003d\""tel:+999666\"", PUSITYPE\u003dDPUSI; ADD HPSI: PISI\u003d\""atcfpsi.voltevisit.com\"", PUSI\u003d\""sip:atcfpsi.voltevisit.com\"", PUSITYPE\u003dDPUSI; ADD HPSI: PISI\u003d\""<EMAIL>\"", PUSI\u003d\""sip:<EMAIL>\"", PUSITYPE\u003dDPUSI; ADD HPSI: PISI\u003d\""<EMAIL>\"", PUSI\u003d\""sip:<EMAIL>\"", PUSITYPE\u003dDPUSI;\n\n/*Configure the SCC AS address to which the STN-SR, ATCF, ATU-STI, and handover indication of the second session maps.*/\n\nSET HAS: PUSI\u003d\""tel:+999666\"", AS\u003d\""sip:**************\\;orig\\;stn-sr\""; SET HAS: PUSI\u003d\""sip:atcfpsi.voltevisit.com\"", AS\u003d\""sip:**************\""; SET HAS: PUSI\u003d\""sip:<EMAIL>\"", AS\u003d\""sip:**************\\;orig\\;atu-sti\""; SET HAS: PUSI\u003d\""sip:<EMAIL>\"", AS\u003d\""sip:**************\\;orig\\;atu-sti\"";\n\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102695},{""id"":3844509,""index"":2,""fields"":{""Action"":""\""Configuration\n\nConfiguring the SRVCC Data on the Standalone HLR, IMS-HSS, and SAE-HSS\n\nScenarios\n\nConfigure the standalone HLR, IMS-HSS, and SAE-HSS tosupportthe SRVCC feature. The configuration procedure is the same as the procedure forconfiguring the SRVCC service data on the convergent HLR, IMS-HSS, and SAE-HSS.\n\nBefore configuring the standalone HLR, IMS-HSS, and SAE-HSS to support the SRVCC feature, the data used forinterworkingbetween the IMS-HSS and the SAE-HSS must be configured.\n\nNote\n\n\n\nFor details about how to configure data used for interworking between the IMS-HSS and the SAE-HSS, seeConfiguring Diameter Signaling Data for IP Networks.\n\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102695}]",,,,,,,,,,,,,,,"0|j1qcg8:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8938992,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES,SANTSDM-1911,,Manual,TODO,,,,,,,,,,
WHFD-310521 Supporting VoWiFi,SANTSDM-1800,8938991,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,11/Jul/25 6:45 PM,,,,HUAWEI-SPECIFIC,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : Relevant NFs
HLR/HSS

Definition
The Voice over WiFi (VoWiFi) service enables non-3GPP subscribers to use IMS services, such as voice, SMS, and supplementary services, over Wi-Fi connections. VoWiFi provides better indoor coverage and rich voice service experience. The VoWiFi service can be provided for a VoWiFi subscriber or a VoWiFi Multi-SIM subscriber. Different from a VoWiFi subscriber who has only one USIM card, a VoWiFi Multi-SIM subscriber has multiple terminals that share one IMPU.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1800,,,,,,,,software,,,,,,[],,,,,,,,,,,,,,,"0|j1qcg0:",,,,,,,,,,,,,,0.0,,,,,,,,,,,"{""issueId"":8938991,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
WHFD-218044-Non-3GPP Access,SANTSDM-1799,8938990,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 4:33 PM,16/Jul/25 11:07 AM,,,,HUAWEI-SPECIFIC,,0,2G,Functional,Manual,MAP,Regression,System,,,SUMMARY : Idem WHFD-310521,,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1799,,,,,,,,software,,,,,,[],,,,,,,,,,,,,,,"0|j1qcfs:",,,,,,,,,,,,,,0.0,,,,,,,,,,,"{""issueId"":8938990,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES,SANTSDM-1904,,Manual,TODO,,,,,,,,,,
WHFD-301652 Faulty Disk Isolation,SANTSDM-1798,8938989,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,16/Jul/25 10:24 AM,16/Jul/25 11:07 AM,,,,HUAWEI-SPECIFIC,,0,Manual,NonFunctional,Performance,Regression,System,,,,"SUMMARY : Definition

Faulty disk isolation (also called the storage bypass function) decouples VNFs from storage devices. It enables VNFs to run in random access memory (RAM) disks and provides essential maintenance functions for VNFs for if a storage fault occurs so as to reduce the impact on services.

Application Scenarios

This feature is used when the storage device is faulty. The NFs that support storage bypass continue to run in the memory and provide minimum maintenance.





Technical Details

If the disk array or distributed storage device is faulty, the VNF, with storage bypass enabled, stores the guest OS, Docker, and container thin pool images in the RAM disks rather than on the disk array or distributed storage, so that applications keep running in the RAM disks without the need to access the storage devices.

Figure 1Storage bypass


Table 2describes the essential maintenance functions that the system can provide when storage bypass is enabled.




	Table 2Essential Maintenance Function
	
		
			
			Essential Maintenance Function
			
			
			Description
			
		
	
	
		
			
			Logging in to the OM Portal
			
			
			Only a local user is allowed to log in to the OM Portal.
			
		
		
			
			Running MML commands on the whitelist
			
			
			MML commands on the whitelist
			
		
		
			
			Checking the active alarms
			
			
			On the OM Portal, chooseMonitoringAlarmsActive Alarms, and check active alarms.
			
		
		
			
			Reporting performance measurement to the EMS
			
			
			Supports reporting performance measurement to the EMS.
			
		
		
			
			Downloading running logs
			
			
			On the OM Portal, chooseMonitoringRun LogsDownloadto download run logs.
			
		
		
			
			Reporting alarms to the EMS
			
			
			Supports reporting alarms to the EMS.
			
		
	


PRECONDITIONS : License Requirements

Licenses are required for this feature. The license control items are as follows: 82209626 LKQDISKISO HLR Disk Fault Isolation, 82209627 LKQDISKISOSAE EPS-HSS Disk Fault Isolation, 82209628 LKQDISKISOIMS IMS-HSS Disk Fault Isolation, 82200CYQ LKQDISKISOUDM UDM Disk Fault Isolation, and 82200FGE LKQ7UDMFE19 UEIR Disk Fault Isolation.

The licenses must be installed before this feature can be used.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1798,,,,,,,,software,,,,,,"[{""id"":3844507,""index"":1,""fields"":{""Action"":""\""Configuration :\n\nFor details about the storage bypass function and how to enable it, seeStorage Bypass Function Description and Deploym\n\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102692}]",,,,,,,,,,,,,,,"0|j1qcfk:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8938989,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES,SANTSDM-1909,,Manual,TODO,,,,,,,,,,
WHFD-301653 Manual Isolation of Subhealthy Disks,SANTSDM-1797,8938988,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:07 AM,,,,HUAWEI-SPECIFIC,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : Definition

If the ALM-135644 Subhealthy Node Storage alarm is generated for a node, the nodes health is suboptimal. After this alarm is generated, the system cannot properly read data from or write data into the disk.

This feature enables users to manually switch the NF to work in storage bypass mode so that it can provide essential maintenance functions, reducing the impact of the fault on services and prevent fault deterioration caused by misoperations.



Technical Overview


	You can manually instruct the NF to enter or exit the storage bypass mode only after the storage bypass function is configured and activated on the NF.
	You can manually instruct the NF to enter or exit the storage bypass mode only when the ALM-135644 Subhealthy Node Storage alarm is generated but neither the ALM-135643 Node Enables Bypass Function nor ALM-135645 Faulty Node Storage alarm is generated for the NF.


PRECONDITIONS : Conditions


	You can manually instruct the NF to enter or exit the storage bypass mode only after the storage bypass function is configured and activated on the NF. For details about how to configure and activate the function, seeStorage Bypass Function Description and Deployment Guide.
	You can manually instruct the NF to enter or exit the storage bypass mode only when the ALM-135644 Subhealthy Node Storage alarm is generated but neither the ALM-135643 Node Enables Bypass Function nor ALM-135645 Faulty Node Storage alarm is generated for the NF.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1797,,,,,,,,software,,,,,,"[{""id"":3844506,""index"":1,""fields"":{""Action"":""\""Disabling the Storage Bypass Mode in When the Storages Health is Suboptimal\n\n\n\tCheck that the ALM-135644 Subhealthy Node Storage alarm is cleared.\n\tOpen theCSP MML command page.\n\tRunSET BYPASSSTATUSto instruct all nodes of the NF to exit the storage bypass mode.\n\tSET BYPASSSTATUS: APPID\u003d0, SETTYPE\u003dexit;\n\t\n\tRunDSP BYPASSSTATUSto verify that all nodes of the NF have already exited the storage bypass mode.\n\tDSP BYPASSSTATUS: APPID\u003d0;\n\t\n\tCheck whether the ALM-135643 Node Enables Bypass Function alarm is cleared. If it is, the NF has already exited the storage bypass mode.\n\n\n\n\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102691}]",,,,,,,,,,,,,,,"0|j1qcfc:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8938988,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
WHFD-218001 - VLR-Specific Roaming Restriction,SANTSDM-1796,8938987,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,11/Jul/25 6:51 PM,,,,HUAWEI-SPECIFIC,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,UpdateLocation,"SUMMARY : block the roaming subscriber on VLR (LocUpdate). see the refusal message from the HLR .



PRECONDITIONS : This parameter specifies the error code sent by the HSS/HLR to the VLR/SGSN when a subscriber roams to an area that is not covered under the roaming plan.

Value:


	
	RoamingNotAllowed
	


Open the HSS9860 Service Management page.

SET MAPSERV: ERRCODEFORROAMRESTRICT=RoamingNotAllowed, VSAREATYPE=VSAREA_NUMBER_TYPE;",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1796,,,,,,,,software,,,,,,"[{""id"":3844504,""index"":1,""fields"":{""Action"":""\""Roaming forbidden\n\n\n\tto add a VLR/SGSN roaming restriction template\n\n\nADD VSLIST: HLRSN\u003d1, TPLID\u003d11, TPLTYPE\u003dVLR, VSAREATYPE\u003dVSAREA_NUMBER_TYPE, VLRSGSNNO\u003d\""33689000112\"", ROAMDEFAULTRULE\u003dRESTRICT;\n\n\n\tto provide the VLR-Specific roaming restriction service for the subscriber.\n\n\nMOD VSRR: IMSI\u003d\""208011000000099\"", PROV\u003dTRUE, TPLTYPE\u003dVLR, TPLID\u003d11;\n\"""",""Data"":"""",""Expected Result"":""\""\n\tThe VLR/SGSN sends an Update Location REQ message to the HLR.\n\n\nThe HLR determines that the subscriber cannot roam to the VLR/SGSN according to the IMSI and VLR/SGSN number carried in the Update Location REQ message, and returns a message indicating location update failure to the VLR/SGSN.\n\n\n\""""},""attachments"":[],""testVersionId"":1102690},{""id"":3844505,""index"":2,""fields"":{""Action"":""\""Roaming permitted\n\n\n\tto add a VLR/SGSN roaming restriction template\n\n\nADD VSLIST: HLRSN\u003d1, TPLID\u003d11, TPLTYPE\u003dVLR, VSAREATYPE\u003dVSAREA_NUMBER_TYPE, VLRSGSNNO\u003d\""33689000112\"", ROAMDEFAULTRULE\u003dPERMISSION;\n\n\n\tto provide the VLR-Specific roaming restriction service for the subscriber.\n\n\nMOD VSRR: IMSI\u003d\""208011000000099\"", PROV\u003dTRUE, TPLTYPE\u003dVLR, TPLID\u003d11;\n\"""",""Data"":"""",""Expected Result"":""\""\n\tThe VLR/SGSN sends an Update Location REQ message to the HLR.\n\tThe HLR sends an Insert Subscriber Data REQ message to the VLR/SGSN.\n\tThe VLR/SGSN sends an Insert Subscriber Data RSP message to the HLR.\n\tThe HLR determines that the subscriber can roam to the VLR/SGSN according to the IMSI and VLR/SGSN number carried in the Update Location REQ message, and sends an Update Location RSP message indicating location update success to the VLR/SGSN.\n\n\""""},""attachments"":[],""testVersionId"":1102690}]",,,,,,,,,,,,,,,"0|j1qcf4:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8938987,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R8.1 (V100R008C10) feature non regression,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
WHFD6218002-Camel Restriction function,SANTSDM-1795,8938986,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:34 PM,16/Jul/25 11:07 AM,,,,HUAWEI-SPECIFIC,,0,Functional,Manual,OAM,Regression,System,,,,"SUMMARY : block sending Camel Brands O-CSI to the VLR (LocUpd) without roaming agreement - HLR routine, replace by odb . Subscriber cant outgoing call  .

PRECONDITIONS : SET MAPCONF: CSIRESTRICTION=TRUE;



MOD CSIR: HLRSN=1, OPTYPE=ADD, VLRSGSNNP=""33689011007"";



SET MAPSERV: ODBOC_SWITCH_FOR_OCSI=ODBBAOC;",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1795,,,,,,,,software,,,,,,"[{""id"":3844499,""index"":1,""fields"":{""Action"":""\""add Camel brand O-CSI to the test subscriber:\n\nADD OCSITPL: HLRSN\u003d1, TPLID\u003d31, CAMEL\u003dPHASE3, DP\u003dCOLLECTINFO, GSMSCFADD\u003d\""33689000888\"", SK\u003d11, DEFCALL\u003dcontinueCall, WITHROAMAGRE_SUPCAMEL\u003dReturnO-CSI, WITHOUTROAMAGRE_SUPCAMEL\u003dAllowLocationUpdateWithMOCall, HPLMNROAM_SUPCAMEL\u003dReturnO-CSI, WITHROAMAGRE_SUPLPCAMEL\u003dReturnLPO-CSI, WITHOUTROAMAGRE_SUPLPCAMEL\u003dReturnLPO-CSI, HPLMNROAM_SUPLPCAMEL\u003dAllowLocationUpdateWithMOCall, WITHROAMAGRE_NOTSUPCAMEL\u003dAllowLocationUpdateWithMOCall, WITHOUTROAMAGRE_NOTSUPCAMEL\u003dAllowLocationUpdateWithMOCall, HPLMNROAM_NOTSUPCAMEL\u003dAllowLocationUpdateWithMOCall, WITHROAMAGRE_SUPHPCAMEL\u003dNoProcess, WITHINTERAGRE_SUPCAMEL\u003dReturnO-CSI, WITHOUTINTERAGRE_SUPCAMEL\u003dReturnBAIC, HPLMNINTER_SUPCAMEL\u003dReturnO-CSI, WITHINTERAGRE_SUPLPCAMEL\u003dReturnLPO-CSI, WITHOUTINTERAGRE_SUPLPCAMEL\u003dReturnBAIC, HPLMNINTER_SUPLPCAMEL\u003dReturnLPO-CSI, WITHINTERAGRE_NOTSUPCAMEL\u003dAllowMTCall, WITHOUTINTERAGRE_NOTSUPCAMEL\u003dReturnBAIC, HPLMNINTER_NOTSUPCAMEL\u003dAllowMTCall;\n\"""",""Data"":"""",""Expected Result"":""\""LMT command \u003d\u003d ok\n\""""},""attachments"":[],""testVersionId"":1102689},{""id"":3844500,""index"":2,""fields"":{""Action"":""\""Add template O-CSI in the \""subscriber template\"" of subscriber:\n\nMOD SUBTPL: HLRSN\u003d1, TPLID\u003d2, TPLTYPE\u003dNORMAL, CARDTYPE\u003dUSIM, NAM\u003dBOTH, PROVOCSI\u003dTRUE, OCSI\u003d31;\n\"""",""Data"":"""",""Expected Result"":""\""LMT command \u003d\u003d ok\n\""""},""attachments"":[],""testVersionId"":1102689},{""id"":3844501,""index"":3,""fields"":{""Action"":""\""Send test MOC with the subsciber with template (user 3G)\n\"""",""Data"":"""",""Expected Result"":""\""Verify in message LocUpdate absence of Camel Brands (replaced by ODB)\n\""""},""attachments"":[],""testVersionId"":1102689},{""id"":3844502,""index"":4,""fields"":{""Action"":""\""do not forget to place orders after test completed :\n\nMOD SUBTPL: HLRSN\u003d1, TPLID\u003d2, TPLTYPE\u003dNORMAL, CARDTYPE\u003dUSIM, NAM\u003dBOTH, PROVOCSI\u003dFALSE;\n\"""",""Data"":"""",""Expected Result"":""\""return to the initial configuration\n\""""},""attachments"":[],""testVersionId"":1102689},{""id"":3844503,""index"":5,""fields"":{""Action"":""\""None\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102689}]",,,,,,,,,,,,,,,"0|j1qcew:",,,,,,,,,,,,,,5.0,,,,,,,,,,,"{""issueId"":8938986,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R8.1 (V100R008C10) feature non regression,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
WHFD-218003 TCSI Suppression Enhancement,SANTSDM-1794,8938985,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:34 PM,16/Jul/25 11:07 AM,,,,HUAWEI-SPECIFIC,,0,Functional,Manual,OAM,Regression,System,,,,"SUMMARY : block sending Camel Brands T-CS to the GMSC (MTC) for roaming subscriber. see TC base without T-CSI service.

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1794,,,,,,,,software,,,,,,"[{""id"":3844495,""index"":1,""fields"":{""Action"":""\""ADD TCSITPL: HLRSN\u003d1, TPLID\u003d41, TPLNAME\u003d\""TCSITPL\"", CAMEL\u003dPHASE3, DP\u003dTAA, GSMSCFADD\u003d\""33689000112\"", SK\u003d12, DEFCALL\u003dcontinueCall, BASCODE1\u003dTS11, BASCODE2\u003dTS12, BASCODE3\u003dTS21, BASCODE4\u003dTS22, AVAILABLE_PLMN\u003dFPLMN;\n\"""",""Data"":"""",""Expected Result"":""\""LMT command \u003d\u003d ok\n\""""},""attachments"":[],""testVersionId"":1102688},{""id"":3844496,""index"":2,""fields"":{""Action"":""\""add the template T-CSI to the subscriber template for the test subscriber :\n\nMOD SUBTPL: HLRSN\u003d1, TPLID\u003d2, TPLTYPE\u003dNORMAL, CARDTYPE\u003dUSIM, NAM\u003dBOTH, PROVTCSI\u003dTRUE, TCSI\u003d41;\n\"""",""Data"":"""",""Expected Result"":""\""LMT command \u003d\u003d ok\n\""""},""attachments"":[],""testVersionId"":1102688},{""id"":3844497,""index"":3,""fields"":{""Action"":""\""made MTC command for subscriber use this template (user 3G)\n\"""",""Data"":"""",""Expected Result"":""\""verify the non-invocation of the service T-CSI\n\""""},""attachments"":[],""testVersionId"":1102688},{""id"":3844498,""index"":4,""fields"":{""Action"":""\""do not forget to place orders after test is completed:\n\nMOD SUBTPL: HLRSN\u003d1, TPLID\u003d2, TPLTYPE\u003dNORMAL, CARDTYPE\u003dUSIM, NAM\u003dBOTH, PROVTCSI\u003dFALSE;\n\"""",""Data"":"""",""Expected Result"":""\""return to the initial configuration\n\""""},""attachments"":[],""testVersionId"":1102688}]",,,,,,,,,,,,,,,"0|j1qceo:",,,,,,,,,,,,,,4.0,,,,,,,,,,,"{""issueId"":8938985,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R8.1 (V100R008C10) feature non regression,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
WHFD-218007 HLR-Specific Roaming Restriction,SANTSDM-1793,8938984,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:34 PM,16/Jul/25 11:07 AM,,,,HUAWEI-SPECIFIC,,0,Functional,Manual,OAM,Regression,System,,,,"SUMMARY : block roaming for all subscribers of the VLR (locup). see the reject message from HLR



PRECONDITIONS :",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1793,,,,,,,,software,,,,,,"[{""id"":3844493,""index"":1,""fields"":{""Action"":""\""MOD HLRRR: HLRSN\u003d1, VLRSGSNNO\u003d\""33689000112\"", ROAMRULE\u003dRESTRICT;\n\"""",""Data"":"""",""Expected Result"":""\""LMT command \u003d\u003dOK\n\""""},""attachments"":[],""testVersionId"":1102687},{""id"":3844494,""index"":2,""fields"":{""Action"":""\""LST HLRRR : OPTYPE\u003dDEFRULE;\n\nSET DEFHLRRR: HLRSN\u003d1, ROAMDEFAULTRULE\u003dPERMISSION;\n\"""",""Data"":"""",""Expected Result"":""\""LMT command \u003d\u003d OK\n\""""},""attachments"":[],""testVersionId"":1102687}]",,,,,,,,,,,,,,,"0|j1qceg:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8938984,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R8.1 (V100R008C10) feature non regression,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
WHFD-218011 Network Capability Configuration (NCC),SANTSDM-1792,8938983,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:34 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,Functional,Manual,OAM,Regression,System,,,,"SUMMARY : Configure MAP version V1 instead of V3 forLocUpd from the test VLR . see the negotiation of version between MSC and HLR.





PRECONDITIONS : ADD NCCTPL: HLRSN=1, TPLID=51, TPLNAME=""VLR1"", UPL=MAPV1;",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1792,,,,,,,,software,,,,,,"[{""id"":3844491,""index"":1,""fields"":{""Action"":""\""ADD VLRTYP: HLRSN\u003d1, VLRPREFIX\u003d\""33689000112\"", TYPE\u003dLOCAL, NCCTPL\u003d51;\n\"""",""Data"":"""",""Expected Result"":""\""LMTcommand \u003d\u003d ok\n\""""},""attachments"":[],""testVersionId"":1102686},{""id"":3844492,""index"":2,""fields"":{""Action"":""\""\n\nreturn to the initial configuration after test\n\nRMV VLRTYP: HLRSN\u003d1, VLRPREFIX\u003d\""33689000112\"";\n\nRMV NCCTPL: HLRSN\u003d1, TPLID\u003d51 ;\n\n\n\"""",""Data"":"""",""Expected Result"":""\""return to the initial configuration and verify\n\""""},""attachments"":[],""testVersionId"":1102686}]",,,,,,,,,,,,,,,"0|j1qce8:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8938983,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R8.1 (V100R008C10) feature non regression,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
WHFD-219901-HLR-Authentication Parameter Configurability,SANTSDM-1791,8938982,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:47 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,Manual,NonFunctional,Regression,Security,System,,,,"SUMMARY : The Authentication Parameter Configurability feature enables the HLR/SAE-HSS/IMS-HSS to configure the parameters c1 to c5 and r1 to r5 according to carriers requirements and generate different sequence number (SQN) indexes (INDs).


	The default values of the parameters c1 to c5 and r1 to r5 are defined in 3GPP TS 35.206.USIM cards use the MILENAGE algorithm to calculate authentication vectors. The parameters c1 to c5 and r1 to r5 are constants used by the MILENAGE algorithm. To enhance authentication security, it is recommended that carriers customize the parameters c1 to c5 and r1 to r5 during SIM/USIM card formulation, instead of using their default values defined in 3GPP TS 35.206.
	SQN INDs can be generated for the network entity bootstrapping server function (BSF) and the network devices in different domains (CS, PS, EPS, IMS, and non-3GPP domain).




Configuration

Table 1describes the data required for configuring the Authentication Parameter Configurability Function.


	
	
		
			
			Command
			
			
			Parameter Name
			
			
			Example Value
			
			
			Description
			
		
	
	
		
			
			SET MAPSERV
			
			
			Authentication step length
			
			
			101
			
			
			This parameter specifies the scheme for generating sequence numbers (SQNs) and the authentication step.

			Note: Set this parameter according to the carriers requirements.
			
		
		
			
			C1 of algorithm Milenage
			
			
			00000000000000000000000000000000
			
			
			These parameters identify the control parameters in the MILENAGE algorithm. The parameter values are in the hexadecimal format.

			These parameters control the calculation details about the MILENAGE algorithm and determine the authentication results. Confirm with the carrier before configuring these parameters.
			NOTE:

			The default values of the parameters c1 to c5 and r1 to r5 are defined in 3GPP TS 35.206. USIM cards use the MILENAGE algorithm to calculate authentication vectors. The parameters c1 to c5 and r1 to r5 are constants used by the MILENAGE algorithm. To enhance authentication security, it is recommended that carriers customize the parameters c1 to c5 and r1 to r5 during SIM/USIM card formulation, instead of using their default values defined in 3GPP TS 35.206.
			
		
		
			
			C2 of algorithm Milenage
			
			
			00000000000000000000000000000001
			
		
		
			
			C3 of algorithm Milenage
			
			
			00000000000000000000000000000002
			
		
		
			
			C4 of algorithm Milenage
			
			
			00000000000000000000000000000004
			
		
		
			
			C5 of algorithm Milenage
			
			
			00000000000000000000000000000008
			
		
		
			
			R1 of algorithm Milenage
			
			
			40
			
		
		
			
			R2 of algorithm Milenage
			
			
			00
			
		
		
			
			R3 of algorithm Milenage
			
			
			20
			
		
		
			
			R4 of algorithm Milenage
			
			
			40
			
		
		
			
			R5 of algorithm Milenage
			
			
			60
			
		
		
			
			SQN IND for authentications from CS
			
			
			0
			
			
			Set the SQN INDs on the HSS to be the same as those on the peer device.
			NOTE:

			
				Each of the following parameters must have a unique value:SQN IND for authentications from CS,SQN IND for authentications from PS,SQN IND for authentications from EPS,SQN IND for authentications from IMS,SQN IND for authentications from Non-3GPP, andSQN IND for authentications from BSF.
				WhenUse SQN IND control functionis set toTRUEin theSET MAPCONFcommand, the setting of the parameterSQN IND for authentications from CSdoes not take effect.
			
			
		
		
			
			SQN IND for authentications from PS
			
			
			1
			
		
		
			
			SQN IND for authentications from EPS
			
			
			16
			
		
		
			
			SQN IND for authentications from IMS
			
			
			2
			
		
		
			
			SQN IND for authentications from Non-3GPP
			
			
			17
			
		
		
			
			SQN IND for authentications from BSF
			
			
			3
			
		
		
			
			AMF fault tolerance flag for check auts
			
			
			RETRY_CONFIG_AMF
			
			
			This parameter specifies whether the HSS checks the AUTS validity during a re-authentication. Configure this parameter based on carrier requirements.
			
		
		
			
			SET MAPCONF
			
			
			AK conversion switch
			
			
			TRUE
			
			
			This parameter specifies whether the HSS uses an anonymity key (AK) to encrypt a sequence number (SQN).

			
				TRUE: The HSS uses an AK to encrypt an SQN.
				FALSE: The HSS does not use an AK to encrypt an SQN.
			
			
		
		
			
			ADD AMF
			
			
			HLR serial number
			
			
			1
			
			
			This parameter specifies the HLR serial number and is planned by the carrier.

			This parameter must be defined by runningADD HLRSNbefore being used.
			
		
		
			
			AMF serial number
			
			
			3
			
			
			This parameter is planned by the carrier after considering all site entities.
			
		
		
			
			AMF value
			
			
			1111
			
			
			This parameter specifies the value of the authentication management field (AMF) used for calculating the message authentication code (MAC).
			
		
		
			
			Whether to set SQN indexes for different domains
			
			
			TRUE
			
			
			
				TRUE: The HSS uses the SQN indexes configured inADD AMFto calculate authentication vectors.
				FALSE: The HSS uses the SQN indexes configured inSET MAPSERVto calculate authentication vectors.
			
			
		
		
			
			SQN IND for authentications from CS
			
			
			11
			
			
			Set the SQN INDs on the HSS to be the same as those on the peer device.
			
		
		
			
			SQN IND for authentications from PS
			
			
			12
			
		
		
			
			SQN IND for authentications from EPS
			
			
			13
			
		
		
			
			SQN IND for authentications from IMS
			
			
			14
			
		
		
			
			SQN IND for authentications from Non-3GPP
			
			
			15
			
		
		
			
			SQN IND for authentications from BSF
			
			
			16
			
		
		
			
			SQN index used in authentication resynchronization
			
			
			FALSE
			
			
			
				TRUE: The HSS uses the SQN IND contained in the authentication resynchronization request sent from the UE.
				FALSE: The HSS uses the SQN IND configured inADD AMForSET MAPSERV.
			
			NOTE:

			Set this parameter toTRUEfor USIM cards that cannot generate different SQN INDs for different domains. Then runADD KIto provide the AMF for the USIM cards.
			
		
		
			
			Whether to use Milenage algorithm parameters
			
			
			TRUE
			
			
			
				TRUE: The HSS9860 uses the MILENAGE algorithm specified by c1 to c5 and r1 to r5 inADD AMFto calculate authentication vectors.
				FALSE: The HSS9860 uses the MILENAGE algorithm specified by AUTH_MILENAGE_c1 to c5 and AUTH_MILENAGE_r1 to r5 inSET MAPSERVto calculate authentication vectors.
			
			
		
		
			
			C1 of algorithm Milenage
			
			
			00000000000000000000000000000010
			
			
			These parameters identify the control parameters in the MILENAGE algorithm. The parameter values are in the hexadecimal format.

			These parameters control the calculation details about the MILENAGE algorithm and determine the authentication results. Confirm with the carrier before configuring these parameters.
			
		
		
			
			C2 of algorithm Milenage
			
			
			00000000000000000000000000000011
			
		
		
			
			C3 of algorithm Milenage
			
			
			00000000000000000000000000000012
			
		
		
			
			C4 of algorithm Milenage
			
			
			00000000000000000000000000000014
			
		
		
			
			C5 of algorithm Milenage
			
			
			00000000000000000000000000000018
			
		
		
			
			R1 of algorithm Milenage
			
			
			21
			
		
		
			
			R2 of algorithm Milenage
			
			
			22
			
		
		
			
			R3 of algorithm Milenage
			
			
			23
			
		
		
			
			R4 of algorithm Milenage
			
			
			24
			
		
		
			
			R5 of algorithm Milenage
			
			
			25
			
		
		
			
			ADD KI
			
			
			HLR serial number
			
			
			1
			
			
			Set this parameter to the value ofHLR serial numberin theADD AMFcommand.
			
		
		
			
			IMSI
			
			
			***************
			
			
			This parameter specifies the IMSI of the subscriber to be provided with a KI.
			
		
		
			
			Operation type
			
			
			ADD
			
			
			This parameter specifies the operation type.
			
		
		
			
			KI value
			
			
			1234567890ABCDEF1234567890ABCDEF
			
			
			This parameter is planned by the carrier after considering all site entities.
			
		
		
			
			Card type
			
			
			USIM
			
			
			Set this parameter toUSIMfor a subscriber to be provided with the Authentication Parameter Configurability Function.
			
		
		
			
			Authentication algorithm type
			
			
			MILENAGE
			
			
			This parameter specifies the authentication algorithm type.

			The value of this parameter must be the same as that written into the USIM card.
			
		
		
			
			OPC value
			
			
			1234567890abcdef1234567890abcde1
			
			
			The value of this parameter must be the same as that written into the USIM card.
			
		
		
			
			AMF serial number
			
			
			3
			
			
			Set this parameter to the value ofAMF serial numberin theADD AMFcommand.
			
		
	




PRECONDITIONS : Conditions


	You understand theAuthentication Parameter Configurability Function.
	basic data has been configured.
	You have data configuration rights.
	You have obtained the user name and password required for logging in to the PGW Web LMT.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1791,,,,,,,,software,,,,,,"[{""id"":3844487,""index"":1,""fields"":{""Action"":""\""Open theUDM Service Managementpage.\n\nRunSET MAPSERVto configure the authentication step, c1 to c5 and r1 to r5 parameters, and SQN INDs.\n\"""",""Data"":"""",""Expected Result"":""\""example\n\n\n\n*-------------Open theUDM Service Managementpage-------------*/\n\n/*Configure the global configurable authentication parameters.*/\n\nSET MAPSERV: AUTH_MILENAGE_C1\u003d\""00000000000000000000000000000000\"", AUTH_MILENAGE_C2\u003d\""00000000000000000000000000000001\"", AUTH_MILENAGE_C3\u003d\""00000000000000000000000000000002\"", AUTH_MILENAGE_C4\u003d\""00000000000000000000000000000004\"", AUTH_MILENAGE_C5\u003d\""00000000000000000000000000000008\"", AUTH_MILENAGE_R1\u003d\""40\"", AUTH_MILENAGE_R2\u003d\""00\"", AUTH_MILENAGE_R3\u003d\""20\"", AUTH_MILENAGE_R4\u003d\""40\"", AUTH_MILENAGE_R5\u003d\""60\"", CSSQNIND\u003d0, PSSQNIND\u003d1, LTESQNIND\u003d16, IMSSQNIND\u003d2, AAASQNIND\u003d17, BSFSQNIND\u003d3;\n\""""},""attachments"":[],""testVersionId"":1102685},{""id"":3844488,""index"":2,""fields"":{""Action"":""\""RunSET MAPCONFto configure theAK conversion switchparameter.\n\"""",""Data"":"""",""Expected Result"":""\""/*Configure the AK conversion switch.*/\n\nSET MAPCONF: AKSWITCH\u003dTRUE;\n\""""},""attachments"":[],""testVersionId"":1102685},{""id"":3844489,""index"":3,""fields"":{""Action"":""\""RunADD AMFto set the AMF value, SQN INDs, and c1 to c5 and r1 to r5 parameters.\n\"""",""Data"":"""",""Expected Result"":""\""*Configure the configurable authentication parameters.*/\n\nADD AMF: HLRSN\u003d1, AMFSNO\u003d3, AMFVALUE\u003d\""*****\"", SQNINDPROV\u003dTRUE, CSSQNIND\u003d11, PSSQNIND\u003d12, LTESQNIND\u003d13, IMSSQNIND\u003d14, AAASQNIND\u003d15, BSFSQNIND\u003d16, RESYNSQNIND\u003dFALSE, CONSTANTPROV\u003dTRUE, C1\u003d\""00000000000000000000000000000010\"", C2\u003d\""00000000000000000000000000000011\"", C3\u003d\""00000000000000000000000000000012\"", C4\u003d\""00000000000000000000000000000014\"", C5\u003d\""00000000000000000000000000000018\"", R1\u003d\""21\"", R2\u003d\""22\"", R3\u003d\""23\"", R4\u003d\""24\"", R5\u003d\""25\"";\n\""""},""attachments"":[],""testVersionId"":1102685},{""id"":3844490,""index"":4,""fields"":{""Action"":""\""RunADD KIto add the authentication data defined inADD AMF.\n\"""",""Data"":"""",""Expected Result"":""\""/*Provide the authentication data defined inADD AMFfor the subscriber with IMSI ***************.*/\n\nADD KI: HLRSN\u003d1, IMSI\u003d\""***************\"", OPERTYPE\u003dADD, KIVALUE\u003d\""*****\"", CARDTYPE\u003dUSIM, ALG\u003dMILENAGE, OPCVALUE\u003d\""*****\"", AMFSNO\u003d1;\n\""""},""attachments"":[],""testVersionId"":1102685}]",,,,,,,,,,,,,,,"0|j1qce0:",,,,,,,,,,,,,,4.0,,,,,,,,,,,"{""issueId"":8938982,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R8.1 (V100R008C10) feature non regression,SANTSDM-1912,,Manual,TODO,,,,,,,,,,
WHFD-310030 - SIP Digest Authentication,SANTSDM-1790,8938981,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:47 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,Manual,NonFunctional,Regression,Security,System,,,,"SUMMARY : 





Definition

The SIP Digest Authentication service enables the HSS to authenticate the identity of SIP subscribers attempting to access an IMS network. This service was developed because the HSS cannot perform IMS AKA or Early IMS Authentication for SIP subscribers, who do not have a SIM, USIM, or ISIM card. Fixed-line SIP subscribers can access an IMS network after SIP Digest Authentication succeeds using the user name and password.



Benefits

Table 1 describes the benefits of the SIP Digest Authentication service.



	Table 1 Benefits of the SIP Digest Authentication service
	
		
			For...
			Benefits
		
	
	
		
			Carriers
			This service ensures IMS network security and data integrity by enabling the IMS network to authenticate the identity of a SIP subscriber.
		
		
			Subscribers
			This service enables SIP subscribers to access IP multimedia services in a secure and reliable manner.
		
	









PRECONDITIONS : 








	
		
			NE
			Version
			License Requirements
			Function Description
		
	
	
		
			IMS-HSS
			HSS9820 V900R006C01 or later
			
			This is an optional service provided by the HSS9860 and requires a license. The license control item HSS BE basic SW,Number of HDA IMPIs,Per Subs must be enabled.
			
			The IMSHSS stores the SIP Digest Authentication data, generates authentication vectors, and sends the vectors to the serving-call session control function (S-CSCF) when required.
		
		
			I-CSCF
			CSC3300 V100R006C02 or later
			This is a basic service provided by the CSC3300 and does not require a license.
			The interrogating-call session control function (I-CSCF) sends the HSS a user authorization request (UAR) message querying the address of the S-CSCF and forwards the REGISTER message to the S-CSCF.
		
		
			P-CSCF
			CSC3300 V100R006C02 or later
			This is a basic service provided by the CSC3300 and does not require a license.
			The proxy-call session control function (P-CSCF) forwards the REGISTER message and Response message to the I-CSCF and user equipment (UE), respectively.
		
		
			SE2900 V300R001C20 or later
			This is an optional service provided by the SE2900 and requires a license. The license control item P-CSCF(per concurrent session) must be enabled.
		
		
			S-CSCF
			CSC3300 V100R006C02 or later
			This is a basic service provided by the CSC3300 and does not require a license.
			The S-CSCF sends the HSS an MAR message requesting the SIP Digest Authentication data.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1790,,,,,,,,software,,,,,,"[{""id"":3844486,""index"":1,""fields"":{""Action"":""\""Inthe SingleSDB PGW / IMS-HSS Service Management\n\nCreate SIP Digest authentication for a VoBB subscriber (Fixed IMS line):\n\n- ADD HSDAINF:\n\nIMPI \u003d +<EMAIL>\n\nHUSERNAME\u003d\""\n\nPWD\u003d\""\n\nREALM\u003d\""sip.osk.com\""\n\"""",""Data"":"""",""Expected Result"":""\""To check the SIP Digest authentication procedure.\n\n\n\""""},""attachments"":[],""testVersionId"":1102684}]",,,,,,,,,,,,,,,"0|j1qcds:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8938981,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R8.1 (V100R008C10) feature non regression/Convergent HSS,SANTSDM-1912,,Manual,TODO,,,,,,,,,,
02- separation of different domains,SANTSDM-1789,8938980,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : HSS support the deployment of the different services in different boards.

This deployment can guarantee that the failure of one domain does not affect the other domain. For example, MSC sends a lot of messages to HSS trigger flow control, but the IMS message received by HSS still can be handled.

PRECONDITIONS : The CCUs which handle 234G service is configured in one board and the CCUs which handle IMS service is configured in another board.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1789,,,,,,,,software,,,,,,"[{""id"":3844484,""index"":1,""fields"":{""Action"":""\""Simulate the 234G board fails. Send IMS message to HSS.\n\"""",""Data"":"""",""Expected Result"":""\""The message is handled successfully.\n\""""},""attachments"":[],""testVersionId"":1102683},{""id"":3844485,""index"":2,""fields"":{""Action"":""\""Recover the 234G board , and simulate the IMS board fails. Send 23G message to HSS.\n\"""",""Data"":"""",""Expected Result"":""\""The message is handled successfully.\n\""""},""attachments"":[],""testVersionId"":1102683}]",,,,,,,,,,,,,,,"0|j1qcdk:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8938980,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R8.1 (V100R008C10) feature non regression/Convergent HSS,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
01  Automatic and Multi-Level Backup and Restoration of User Data,SANTSDM-1788,8938979,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 2:11 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,BackupRestore,Manual,NonFunctional,Regression,System,,,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1788,,,,,,,,software,,,,,,"[{""id"":3844471,""index"":1,""fields"":{""Action"":""\""On the HSS9860 Service Management view in PGW Web LMT, run the ADD KI and ADD TPLSUB commands.\n\"""",""Data"":"""",""Expected Result"":""\""The commands are executed successfully.\n\""""},""attachments"":[],""testVersionId"":1102682},{""id"":3844472,""index"":2,""fields"":{""Action"":""\""On the OMU client USCDB ME, run the DSP CLUSTERID command to query the DRU cluster and DRU nodes and the DSU cluster and DSU nodes that store the data of the subscriber.\n\"""",""Data"":"""",""Expected Result"":""\""The output contains the DRU cluster and DRU nodes and the DSU cluster and DSU nodes that store the data of the subscriber.\n\""""},""attachments"":[],""testVersionId"":1102682},{""id"":3844473,""index"":3,""fields"":{""Action"":""\""On the USCDB MML command interface of the OMU client, run the BKP NODE command to back up the data of the node manually.\n\"""",""Data"":"""",""Expected Result"":""\""The backup command is executed successfully and the backup is successful.\n\""""},""attachments"":[],""testVersionId"":1102682},{""id"":3844474,""index"":4,""fields"":{""Action"":""\""1. On the OMU client USCDB ME, Set LDRSOURCE to 1 for loading from Master node\n\nMOD INSP: INSPT\u003dFE, INSPN\u003dLDRSOURCE, INSPV\u003d1, FETYPEN\u003d\""HSS9860\"";\n\"""",""Data"":"""",""Expected Result"":""\""The command is executed successfully.\n\""""},""attachments"":[],""testVersionId"":1102682},{""id"":3844475,""index"":5,""fields"":{""Action"":""\""Reset the DRU and DSU Slave Node loading from Master node\n\"""",""Data"":"""",""Expected Result"":""\""The DRU and DSU recovery are OK.\n\""""},""attachments"":[],""testVersionId"":1102682},{""id"":3844476,""index"":6,""fields"":{""Action"":""\""On the HSS9860 Service Management view in PGW Web LMT, run the LST SUB command to check whether user exists.\n\"""",""Data"":"""",""Expected Result"":""\""The user exist.\n\""""},""attachments"":[],""testVersionId"":1102682},{""id"":3844477,""index"":7,""fields"":{""Action"":""\""1. On the OMU client USCDB ME, Set LDRSOURCE to 2 for loading from dumpfile\n\nMOD INSP: INSPT\u003dFE, INSPN\u003dLDRSOURCE, INSPV\u003d2, FETYPEN\u003d\""HSS9860\"";\n\"""",""Data"":"""",""Expected Result"":""\""The command is executed successfully.\n\""""},""attachments"":[],""testVersionId"":1102682},{""id"":3844478,""index"":8,""fields"":{""Action"":""\""Reset the DRU and DSU to loading from dumpfile.\n\"""",""Data"":"""",""Expected Result"":""\""The DRU and DSU recovery are OK.\n\""""},""attachments"":[],""testVersionId"":1102682},{""id"":3844479,""index"":9,""fields"":{""Action"":""\""1. On the HSS9860 Service Management view in PGW Web LMT, run the LST SUB command to check whether user exists.\n\"""",""Data"":"""",""Expected Result"":""\""The user exist.\n\""""},""attachments"":[],""testVersionId"":1102682},{""id"":3844480,""index"":10,""fields"":{""Action"":""\""On the USCDB MML command interface of the OMU client, run the BKP DB command to back up the data of database manually.\n\"""",""Data"":"""",""Expected Result"":""\""The backup command is executed successfully and the backup is successful.\n\""""},""attachments"":[],""testVersionId"":1102682},{""id"":3844481,""index"":11,""fields"":{""Action"":""\""1. On the OMU client USCDB ME, Set LDRSOURCE to 3 for loading from dump file.\n\nMOD INSP: INSPT\u003dFE, INSPN\u003dLDRSOURCE, INSPV\u003d3, FETYPEN\u003d\""HSS9860\"";\n\"""",""Data"":"""",""Expected Result"":""\""The command is executed successfully.\n\""""},""attachments"":[],""testVersionId"":1102682},{""id"":3844482,""index"":12,""fields"":{""Action"":""\""After the backup is finished, reset the DRU and DSU to loading from DB.\n\"""",""Data"":"""",""Expected Result"":""\""The DRU and DSU recovery are OK.\n\""""},""attachments"":[],""testVersionId"":1102682},{""id"":3844483,""index"":13,""fields"":{""Action"":""\""On the HSS9860 Service Management view in PGW Web LMT, run the LST SUB command to check whether user exists.\n\"""",""Data"":"""",""Expected Result"":""\""The user exist.\n\""""},""attachments"":[],""testVersionId"":1102682}]",,,,,,,,,,,,,,,"0|j1qcdc:",,,,,,,,,,,,,,13.0,,,,,,,,,,,"{""issueId"":8938979,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R8.1 (V100R008C10) feature non regression/Subscriber Data Reliability,SANTSDM-1898,,Manual,TODO,,,,,,,,,,
02  Subscriber Data Backup Up BE Physical Database,SANTSDM-1787,8938978,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 2:11 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,BackupRestore,Manual,NonFunctional,Regression,System,,,,"SUMMARY : Verify that the physical database can be successfully backuped.

PRECONDITIONS : 
	
		
			
			1. The HSS operates properly.

			2. IMS subscriber A is defined in the HSS.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1787,,,,,,,,software,,,,,,"[{""id"":3844467,""index"":1,""fields"":{""Action"":""\""On the USCDB command interface of the OMU client, run the BKP DB command to perform a backup of the physical database.\n\"""",""Data"":"""",""Expected Result"":""\""The BKP DB command is successfully executed.\n\""""},""attachments"":[],""testVersionId"":1102681},{""id"":3844468,""index"":2,""fields"":{""Action"":""\""Query the database backup status.\n\"""",""Data"":"""",""Expected Result"":""\""The database backup status is Backup Running.\n\""""},""attachments"":[],""testVersionId"":1102681},{""id"":3844469,""index"":3,""fields"":{""Action"":""\""Query the mirror files.\n\nDSP BKPLIST:;\n\"""",""Data"":"""",""Expected Result"":""\""The system displays the mirror files. Time stamp information indicates the time when the mirror files are created, for example, 2013-08-31 04:01:06.\n\""""},""attachments"":[],""testVersionId"":1102681},{""id"":3844470,""index"":4,""fields"":{""Action"":""\""Log in the FTP server; check the backup files in the configured backup path.\n\"""",""Data"":"""",""Expected Result"":""\""The backup files has been uploaded to the FTP server and saved in the correct path.\n\""""},""attachments"":[],""testVersionId"":1102681}]",,,,,,,,,,,,,,,"0|j1qcd4:",,,,,,,,,,,,,,4.0,,,,,,,,,,,"{""issueId"":8938978,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R8.1 (V100R008C10) feature non regression/Subscriber Data Reliability,SANTSDM-1898,,Manual,TODO,,,,,,,,,,
03  Restoring the BE Physical Database,SANTSDM-1786,8938977,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 2:11 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,BackupRestore,Manual,NonFunctional,Regression,System,,,,"SUMMARY : Verify that the physical database can be successfully restored.

PRECONDITIONS : 1. The HSS operates properly.

2. IMS subscriber A is defined in the HSS.

3. The full backup mirror file of IMS subscriber A exists.

The BE is set to load data from the physical database.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1786,,,,,,,,software,,,,,,"[{""id"":3844462,""index"":1,""fields"":{""Action"":""\""On the Portal client, delete the data of subscriber A.\n\"""",""Data"":"""",""Expected Result"":""\""The data of subscriber A is successfully deleted.\n\""""},""attachments"":[],""testVersionId"":1102680},{""id"":3844463,""index"":2,""fields"":{""Action"":""\""1. Restore the physical database.\n\nRST DB:;\n\"""",""Data"":"""",""Expected Result"":""\""The RST DB command is successfully executed.\n\""""},""attachments"":[],""testVersionId"":1102680},{""id"":3844464,""index"":3,""fields"":{""Action"":""\""1. Query the database restoration status.\n\nDSP DB:;\n\"""",""Data"":"""",""Expected Result"":""\""The database restoration status is Restore running .When the restoration status is No running task, the physical database is successfully restored\n\""""},""attachments"":[],""testVersionId"":1102680},{""id"":3844465,""index"":4,""fields"":{""Action"":""\""1. On the CGP MML command interface of the OMU client, run the RST ME command to restart the BE.\n\nRST ME:;\n\"""",""Data"":"""",""Expected Result"":""\""The BE is successfully restarted.\n\""""},""attachments"":[],""testVersionId"":1102680},{""id"":3844466,""index"":5,""fields"":{""Action"":""\""On the Portal client, query whether the data of subscriber A exists in the HSS.\n\"""",""Data"":"""",""Expected Result"":""\""The data of subscriber A exists in the HSS.\n\""""},""attachments"":[],""testVersionId"":1102680}]",,,,,,,,,,,,,,,"0|j1qccw:",,,,,,,,,,,,,,5.0,,,,,,,,,,,"{""issueId"":8938977,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R8.1 (V100R008C10) feature non regression/Subscriber Data Reliability,SANTSDM-1898,,Manual,TODO,,,,,,,,,,
WHFD-310250 - VoLTE profile - Adding a Convergent HSS Subscriber Using Template,SANTSDM-1785,8938976,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:47 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,Manual,NonFunctional,Regression,Security,System,,,,"SUMMARY : 




Function

ADD TPLUSCSUB is used to add a subscriber to the HSS/HLR database so that they can use services on GSM, UMTS, EPS, and IMS networks.




Note



	Before running this command to add a subscriber, run ADD KI to load the KI data for the subscriber for authentication.
	When Authentication type of IMPI2 is set to HDA or SDA, either only HA1 value2 or both User name2 and Password2 can be specified to authenticate IMPI2. You are advised to specify HA1 value2.
	If Whether to use a random key is set to TRUE using SET MAPCONF but no random key is generated using SET RANDOMKEY, this command will fail with ""The random key encryption function has been enabled. Run SET RANDOMKEY to generate a random key first."" displayed.









PRECONDITIONS : In the SingleSDB PGW / HSS9860 Service Management

ADD KI: HLRSN=1, IMSI=""2080144691672xx"", OPERTYPE=ADD, KIVALUE=""*****"", CARDTYPE=USIM, ALG=MILENAGE, OPCVALUE=""*****"", AMFSNO=3, KEYTYPE=ClearKey;

In the SingleSDB PGW / HSS9860 Service Management

Add a Charging Address Template
ADD HCHGTPL: TPLID=8, TPLNAME=""PECF VoLTE Europe IMS Hua"", PECF=""cg01.mnc001.mcc208.3gppnetwork.org"";

Add a Service Profile Template
ADD HSPTPL: TPLID=5, TPLNAME=""VoLTE Hua SP"", SIFCLIST=""500"";

Add a Capability Template
ADD HCAPTPL: TPLID=8, TPLNAME=""VoLTE Europe IMS Hua"", MCAP=""2"", SCSCFLIST=""sip:scscf-0.sip.osk.com"";",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1785,,,,,,,,software,,,,,,"[{""id"":3844461,""index"":1,""fields"":{""Action"":""\""In the SingleSDB PGW / HSS9860 Service Management\n\nADD TPLUSCSUB: HLRSN\u003d1, IMSI\u003d\""2080144691672xx\"", ISDN\u003d\""337891672xx\"", TPLID\u003d4, ANCHOR\u003dTCSI, SUBID\u003d\""4\"", IMPI\u003d\""<EMAIL>\"", IMPULIST\u003d\""sip:+<EMAIL>\\\""\\\""tel:+337891672xx\\\""\\\""sip:<EMAIL>\"", BARIMPULIST\u003d\""<EMAIL>\"", CONSFLG\u003dTRUE, CHARGTPLID\u003d8, SPTPLID\u003d5, CAPTPLID\u003d8;\n\"""",""Data"":"""",""Expected Result"":""\""The EPC and IMS registration of this subscriber is OK\n\nCall from or to this subscriber is OK\n\""""},""attachments"":[],""testVersionId"":1102679}]",,,,,,,,,,,,,,,"0|j1qcco:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8938976,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R8.1 (V100R008C10) feature non regression/HSS Features,SANTSDM-1912,,Manual,TODO,,,,,,,,,,
WHFD-310250 - VoBB (Fixed IMS line) profile - Adding an IMS Subscription,SANTSDM-1784,8938975,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:47 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,Manual,NonFunctional,Regression,Security,System,,,,"SUMMARY : 
Configuring Data for VoBB Subscribers



Example



Task Description

Define a VoBB subscriber using templates on the SPG portal. Figure 1 shows a typical VoBB subscriber data model.

Figure 1 Typical VoBB subscriber data model








PRECONDITIONS : 





In the SingleSDB PGW / IMS-HSS Service Management

Configure the SIP Digest authentication data
ADD HSDAINF: IMPI=""+<EMAIL>"", HUSERNAME=""Huawei*1"", PWD=""*****"", REALM=""sip.osk.com"";

Add a Charging Address Template
ADD HCHGTPL: TPLID=8, TPLNAME=""PECF VoLTE Europe IMS Hua"", PECF=""cg01.mnc001.mcc208.3gppnetwork.org"";

Add a Service Profile Template
ADD HSPTPL: TPLID=5, TPLNAME=""Fixed IMS Hua SP test"", SIFCLIST=""350"";

Add a Capability Template
ADD HCAPTPL: TPLID=8, TPLNAME=""VoLTE Europe IMS Hua"", MCAP=""2"", SCSCFLIST=""sip:scscf-0.sip.osk.com"";",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1784,,,,,,,,software,,,,,,"[{""id"":3844454,""index"":1,""fields"":{""Action"":""\""in the SingleSDB PGW / IMS-HSS Service Management\n\nAdd an IMS Subscription by Template:\n\nADD HTPLSUB: SUBID\u003d\""1\"", IMPI\u003d\""+<EMAIL>\"", IMPU\u003d\""sip:+<EMAIL>\"", IMPUTYPE\u003dDIMPU, CAPTPLID\u003d8, CHARGTPLID\u003d8, SPTPLID\u003d2, IMPUTPLID\u003d100, PBXUSERFLAG\u003dFALSE;\n\n\n\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102678},{""id"":3844455,""index"":2,""fields"":{""Action"":""\""Add a TEL IMPU by Template :\n\nADD HTPLIMPU: IMPI\u003d\""+<EMAIL>\"", IMPU\u003d\""tel:+339006370xx\"", IMPUTYPE\u003dDIMPU, SPTPLID\u003d2, IMPUTPLID\u003d100;\n\n\n\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102678},{""id"":3844456,""index"":3,""fields"":{""Action"":""\""Set an IRS:\n\nSET HIRS: IRSID\u003d8, IMPULIST\u003d\\\""sip:+<EMAIL>\\\""\\\""tel:+339006370xx\\\"";\n\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102678},{""id"":3844457,""index"":4,""fields"":{""Action"":""\""None\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102678},{""id"":3844458,""index"":5,""fields"":{""Action"":""\""Set the Default IMPU of an IRS:\n\nSET HDEFIMPU: IRSID\u003d10, IMPU\u003d\""sip:+<EMAIL>\"";\n\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102678},{""id"":3844459,""index"":6,""fields"":{""Action"":""\""Set the Sharing of a Service Profile:\n\nSET HSPSHARE: BASEIMPU\u003d\""sip:+<EMAIL>\"", IMPU\u003d\""tel:+339006370xx\"";\n\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102678},{""id"":3844460,""index"":7,""fields"":{""Action"":""\""Set an Alias Group ID:\n\nSET HALIASPU: ALIASID\u003d8, IMPULIST\u003d\\\""sip:+<EMAIL>\\\""\\\""tel:+339006370xx\\\"";\n\"""",""Data"":"""",""Expected Result"":""\""The IMS registration of this subscriber is OK\n\nCall from or to this subscriber is OK\n\""""},""attachments"":[],""testVersionId"":1102678}]",,,,,,,,,,,,,,,"0|j1qccg:",,,,,,,,,,,,,,7.0,,,,,,,,,,,"{""issueId"":8938975,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R8.1 (V100R008C10) feature non regression/HSS Features,SANTSDM-1912,,Manual,TODO,,,,,,,,,,
WHFD-310280 - Configuring SS Data Synchronization between CS and VoLTE,SANTSDM-1783,8938974,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 3:19 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,4G,Diameter,Functional,Manual,Regression,S6a,System,,"SUMMARY : 


Scenarios


Configure the Supplementary Service Data Consistency Between CS and VoLTE feature to synchronize supplementary service (SS) data modified by voice over Long Term Evolution (VoLTE) subscribers between the 2G/3G and IMS networks.





PRECONDITIONS : 





	You have the administrator rights of HUAWEI Operation  Maintenance System.
	You have the service provisioning rights.
	You have configured basic HSS9860 data, data for interworking between the HSS9860 and the AS, and subscription data of the subscriber on the HSS9860s on which SAE-HSS FE and IMS-HSS FE are deployed together.


the HSS9860 serves as the convergent HLR/HSS when processing messages over the Sh interface?


	In the Huawei Operation  Maintenance System / HSS9860:


LST INSP: INSPT=HSS9860, INSPN=SH_ENTITY_FUNCTION;


	Verify that the parameter value is 0.


If not do the MML command MOD INSP",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1783,,,,,,,,software,,,,,,"[{""id"":3844443,""index"":1,""fields"":{""Action"":""\""The HSS9860 uses the data format sent from Huawei AS?\n\nIn the Huawei Operation  Maintenance System / HSS9860:\n\nLST INSP: INSPT\u003dHSS9860, INSPN\u003dVOLTE_SH_DATA_SYN_FORMAT;\n\n\n\"""",""Data"":"""",""Expected Result"":""\""Verify that the parameter value is 0.\n\nIf not do the MML command MOD INSP\n\""""},""attachments"":[],""testVersionId"":1102677},{""id"":3844444,""index"":2,""fields"":{""Action"":""\""The HSS9860 serves as the IMS-HSS and SAE-HSS when processing messages over the Sh interface?\n\nIn the Huawei Operation  Maintenance System / HSS9860:\n\nLST INSP: INSPT\u003dHSS9860, INSPN\u003dSH_ENTITY_FUNCTION;\n\"""",""Data"":"""",""Expected Result"":""\""Verify that the parameter value is 0.\n\nIf not do the MML command MOD INSP\n\""""},""attachments"":[],""testVersionId"":1102677},{""id"":3844445,""index"":3,""fields"":{""Action"":""\""The HSS9860 synchronizes Camel service data on CS side to the IMS network (bit 0,3,4,5 set to 1)?\n\nIn the Huawei Operation  Maintenance System / HSS9860:\n\nMOD INSP: INSPT\u003dHSS9860, INSPN\u003dVOLTE_DATA_SYN_SWITCH, INSPV\u003d2097151;\n\"""",""Data"":"""",""Expected Result"":""\""Verify that the old parameter value 0 is modified to 2097151.\n\""""},""attachments"":[],""testVersionId"":1102677},{""id"":3844446,""index"":4,""fields"":{""Action"":""\""The HSS9860 sends supplementary service data in MAP_INSERT_SUBSCRIBER_DATA message to the 2G/3G network (bit 0,1,2,3 set to 1)?\n\nIn the Huawei Operation  Maintenance System / HSS9860:\n\nMOD INSP: INSPT\u003dHSS9860, INSPN\u003dVOLTE_NOT_ISD_SWITCH, INSPV\u003d15;\n\"""",""Data"":"""",""Expected Result"":""\""Verify that the old parameter value 0 is modified 15.\n\""""},""attachments"":[],""testVersionId"":1102677},{""id"":3844447,""index"":5,""fields"":{""Action"":""\""The HSS9860 synchronizes ODB service data on CS side to the IMS network?\n\nIn the Huawei Operation  Maintenance System / HSS9860:\n\nMOD INSP: INSPT\u003dHSS9860, INSPN\u003dVOLTE_DATA_SYN_ODB_SWITCH, INSPV\u003d4095;\n\"""",""Data"":"""",""Expected Result"":""\""Verify that the old parameter value 0 is modified 4095.\n\""""},""attachments"":[],""testVersionId"":1102677},{""id"":3844448,""index"":6,""fields"":{""Action"":""\""The HSS9860 synchronizes Camel service data on CS side to the IMS network (bit 0,3,4,5 set to 1)?\n\nIn the Huawei Operation  Maintenance System / HSS9860:\n\nMOD INSP: INSPT\u003dHSS9860, INSPN\u003dVOLTE_DATA_SYN_CAMEL_SWITCH, INSPV\u003d57;\n\"""",""Data"":"""",""Expected Result"":""\""Verify that the old parameter value 0 is modified 57.\n\""""},""attachments"":[],""testVersionId"":1102677},{""id"":3844449,""index"":7,""fields"":{""Action"":""\""The HSS9860 synchronizes extension data (subscriber category, video call) on CS side to the IMS network (bit 0,1 set to 1)?\n\nIn the Huawei Operation  Maintenance System / HSS9860:\n\nMOD INSP: INSPT\u003dHSS9860, INSPN\u003dVOLTE_DATA_SYN_EXTENSION_SWITCH, INSPV\u003d3;\n\"""",""Data"":"""",""Expected Result"":""\""Verify that the old parameter value 0 is modified 3.\n\""""},""attachments"":[],""testVersionId"":1102677},{""id"":3844450,""index"":8,""fields"":{""Action"":""\""The HSS9860 doesnt store supplementary service data synchronized between CS and IMS domains in the IMS database?\n\nIn the Huawei Operation  Maintenance System / HSS9860:\n\nLST INSP: INSPT\u003dHSS9860, INSPN\u003dVOLTE_SYN_DATA_STORE_SWITCH;\n\"""",""Data"":"""",""Expected Result"":""\""Verify that the parameter value is 0.\n\nIf not do the MML command MOD INSP\n\""""},""attachments"":[],""testVersionId"":1102677},{""id"":3844451,""index"":9,""fields"":{""Action"":""\""The HSS9860 supports only the SRVCC subscription function and the \""rule-deactivated/\"" in an XML tag (bit 0,1 set to 1)?\n\nIn the Huawei Operation  Maintenance System / HSS9860:\n\nLST INSP: INSPT\u003dHSS9860, INSPN\u003dVOLTE_ENHANCED_FEATURE_SWITCH;\n\"""",""Data"":"""",""Expected Result"":""\""Verify that the parameter value is 3.\n\nIf not do the MML command MOD INSP\n\""""},""attachments"":[],""testVersionId"":1102677},{""id"":3844452,""index"":10,""fields"":{""Action"":""\""The shared data notification subscription of FE is disabled?\n\nIn the Huawei Operation  Maintenance System / HSS9860:\n\nLST INSP: INSPT\u003dFE, INSPN\u003dDATANOTIFYMODE;\n\"""",""Data"":"""",""Expected Result"":""\""Verify that the parameter value is 0.\n\nIf not do the MML command MOD INSP\n\""""},""attachments"":[],""testVersionId"":1102677},{""id"":3844453,""index"":11,""fields"":{""Action"":""\""The IMS-HSS inserts Call Forwarding Default service data in the operator defined data category (1: user defined data category)?\n\nIn the Huawei Operation  Maintenance System / HSS9860:\n\nLST INSP: INSPT\u003dIMS-HSS, INSPN\u003dSHCFDINUSER;\n\"""",""Data"":"""",""Expected Result"":""\""Verify that the parameter value is 0.\n\nIf not do the MML command MOD INSP\n\""""},""attachments"":[],""testVersionId"":1102677}]",,,,,,,,,,,,,,,"0|j1qcc8:",,,,,,,,,,,,,,11.0,,,,,,,,,,,"{""issueId"":8938974,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R8.1 (V100R008C10) feature non regression/HSS Features/WHFD-310280 - Supplementary service data consistency between CS and VoLTE,SANTSDM-1900,,Manual,TODO,,,,,,,,,,
KPSA Provisioning Interface Configuration,SANTSDM-1782,8938973,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:34 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,Functional,Manual,OAM,Regression,SOAP,System,,,"SUMMARY : the goal of this test is to configure the provisioning access for a provisioning tool like KPSA.

PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1782,,,,,,,,software,,,,,,"[{""id"":3844438,""index"":1,""fields"":{""Action"":""\""\n\tin LMT / USCDB\n\n\nTo configure port 8001 for provisioning\n\nMOD PORT: PORTNO\u003d8001, PORTTYPE\u003dPORT_FOR_PROV, PORTTYPENAME\u003dPORT_FOR_SOAP, DISABLEPORT\u003dNO, PORTBUSSTYPE\u003d\""USCDB\n\"""",""Data"":"""",""Expected Result"":""\""RETCODE \u003d 0 Operation succeeded\n\""""},""attachments"":[],""testVersionId"":1102676},{""id"":3844439,""index"":2,""fields"":{""Action"":""\""\n\tin LMT / USCDB\n\n\nTo declare the provisioning tool IP address and server name\n\nADD PROVISIONIP: ADDRID\u003d1, ADDRNAME\u003d\""dvdsie70\"", IPADDR\u003d\""**************\"", IPTYPE\u003dBOSS\n\"""",""Data"":"""",""Expected Result"":""\""RETCODE \u003d 0 Operation succeeded\n\""""},""attachments"":[],""testVersionId"":1102676},{""id"":3844440,""index"":3,""fields"":{""Action"":""\""\n\tin LMT / USCDB\n\n\nTo synchronize new data\n\nSYN PGWDATA\n\"""",""Data"":"""",""Expected Result"":""\""RETCODE \u003d 0 Operation succeeded\n\""""},""attachments"":[],""testVersionId"":1102676},{""id"":3844441,""index"":4,""fields"":{""Action"":""\""\n\tin LMT / CGP\n\n\nTo restart USPMU3 boards\n\nRST BRD: SRN\u003d0, SN\u003d10, LN\u003dFRONT,CONFIRM\u003dY\n\nRST BRD: SRN\u003d0, SN\u003d12, LN\u003dFRONT,CONFIRM\u003dY\n\"""",""Data"":"""",""Expected Result"":""\""RETCODE \u003d 0 Operation succeeded\n\n\n\n\n\""""},""attachments"":[],""testVersionId"":1102676},{""id"":3844442,""index"":5,""fields"":{""Action"":""\""\n\tin LMT / CGP\n\n\nTo restart PGW modules\n\nRST MODULE: MID\u003d164\n\nRST MODULE: MID\u003d165\n\nRST MODULE: MID\u003d166\n\nRST MODULE: MID\u003d205\n\nRST MODULE: MID\u003d206\n\"""",""Data"":"""",""Expected Result"":""\""RETCODE \u003d 0 Operation succeeded\n\""""},""attachments"":[],""testVersionId"":1102676}]",,,,,,,,,,,,,,,"0|j1qcc0:",,,,,,,,,,,,,,5.0,,,,,,,,,,,"{""issueId"":8938973,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R8.1 (V100R008C10) feature non regression/Provisioning Interface Management,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
DOIC AVP without trafic load,SANTSDM-1781,8938972,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:31 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,Manual,NonFunctional,Regression,System,,,,,"SUMMARY : Test performed with the help of SPS Huawei team

PRECONDITIONS : - SDM Huawei is configured as reporting node (Diameter server), and DOIC feature is activated
- SPS Huawei is configured as reacting node (Diameter client) and DOIC feature is activated",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1781,,,,,,,,software,,,,,,"[{""id"":3844437,""index"":1,""fields"":{""Action"":""\""- On S6a interface messages are exchanged between SDM and DRF\n- These messages carry the support from DRF of DOIC feature (OC-Supported-feature AVP)\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that the DRF send a DOIC service request with capability field that indicated the supported algorithm\n- Verify that the SDM reply a DOIC service response with the selected algorithm, and without OLR report information has the SDM is not overloaded.\n\""""},""attachments"":[],""testVersionId"":1102675}]",,,,,,,,,,,,,,,"0|j1qcbs:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8938972,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R19.1 (V900R019C10SPC319) feature non regression/DOIC (in front of SPS Huawei),SANTSDM-1907,,Manual,TODO,,,,,,,,,,
DOIC AVP with trafic load,SANTSDM-1780,8938971,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:31 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,Manual,NonFunctional,Regression,System,,,,,"SUMMARY : Test performed with the help of SPS Huawei team

PRECONDITIONS : - SDM Huawei is configured as reporting node (Diameter server), and DOIC feature is activated
- SDM Huawei reduction percentage is configured with 5 level :
 - CPU level 1 -- 10%
 - CPU level 2 -- 30%
 - CPU level 3 -- 50%
 - CPU level 4 -- 70%
 - CPU level 5 -- 90%
- SDM Huawei overload duration is configured at value 6s
- SPS Huawei is configured as reacting node (Diameter client) and DOIC feature is activated",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1780,,,,,,,,software,,,,,,"[{""id"":3844436,""index"":1,""fields"":{""Action"":""\""- the DRF send load traffic to the SDM at the level that reach CPU level 1.\n- the SDM send a DOIC report requesting\n - a discarding proportion at value 10%\n - an overload duration at value 6s\n - a report type at value Host_Report\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that DRF reduce the traffic load at 10% as resquested by the SDM\n- Verify that after the period of overload duration (6s) that the DRF stop overload control\n\""""},""attachments"":[],""testVersionId"":1102674}]",,,,,,,,,,,,,,,"0|j1qcbk:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8938971,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R19.1 (V900R019C10SPC319) feature non regression/DOIC (in front of SPS Huawei),SANTSDM-1907,,Manual,TODO,,,,,,,,,,
DOIC trafic on different FE,SANTSDM-1779,8938970,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:31 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,Manual,NonFunctional,Regression,System,,,,,"SUMMARY : Test performed with the help of SPS Huawei team

PRECONDITIONS : - SDM0 is configured as reporting node (Diameter server), and DOIC feature is activated
- SDM0 reduction percentage is configured with 5 level :
 - CPU level 1 -- 10%
 - CPU level 2 -- 30%
 - CPU level 3 -- 50%
 - CPU level 4 -- 70%
 - CPU level 5 -- 90%
- SDM0 overload duration is configured at value 6s
- SDM0 has the DOIC feature deactivated
- SPS Huawei is configured as reacting node (Diameter client) and DOIC feature is activated",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1779,,,,,,,,software,,,,,,"[{""id"":3844435,""index"":1,""fields"":{""Action"":""\""- the DRF send load traffic to the SDM0 and SDM1 at the level that reach CPU level 1.\n- the SDM0 send a DOIC report requesting an discarding proportion at value 10%\n- the SDM1 doesnt send a DOIC report\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that DRF reduce the traffic load at 10% as resquested by the SDM0\n- Verify that DRF doesnt reduce the traffic to SDM1\n\""""},""attachments"":[],""testVersionId"":1102673}]",,,,,,,,,,,,,,,"0|j1qcbc:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8938970,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R19.1 (V900R019C10SPC319) feature non regression/DOIC (in front of SPS Huawei),SANTSDM-1907,,Manual,TODO,,,,,,,,,,
DOIC with different trafic load level,SANTSDM-1778,8938969,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:31 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,Manual,NonFunctional,Regression,System,,,,,"SUMMARY : Test performed with the help of SPS Huawei team

PRECONDITIONS : - SDM Huawei is configured as reporting node (Diameter server), and DOIC feature is activated
- SDM Huawei reduction percentage is configured with 5 levels :
 - CPU level 1 -- 10%
 - CPU level 2 -- 30%
 - CPU level 3 -- 50%
 - CPU level 4 -- 70%
 - CPU level 5 -- 90%
- SDM Huawei overload duration is configured at value 6s
- SPS Huawei is configured as reacting node (Diameter client) and DOIC feature is activated",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1778,,,,,,,,software,,,,,,"[{""id"":3844431,""index"":1,""fields"":{""Action"":""\""- the DRF send load traffic to the SDM at the level that reach CPU level 1.\n- the SDM send a DOIC report requesting an discarding proportion at value 10%\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that DRF reduce the traffic load at 10% as resquested by the SDM\n\""""},""attachments"":[],""testVersionId"":1102672},{""id"":3844432,""index"":2,""fields"":{""Action"":""\""- the DRF increase load traffic to the SDM at the level that reach CPU level 2 in between the time period duration of the previous report.\n- the SDM send a DOIC report requesting an discarding proportion at value 30%\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that DRF reduce the traffic load at 30% as resquested by the SDM\n- Verify that the sequence number of the report is incremented by 1\n\n\""""},""attachments"":[],""testVersionId"":1102672},{""id"":3844433,""index"":3,""fields"":{""Action"":""\""- the DRF increase load traffic to the SDM at the level that reach CPU level 3 in between the time period duration of the previous report.\n- the SDM send a DOIC report requesting an discarding proportion at value 50%\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that DRF reduce the traffic load at 50% as resquested by the SDM\n- Verify that the sequence number of the report is incremented by 1\n\n\""""},""attachments"":[],""testVersionId"":1102672},{""id"":3844434,""index"":4,""fields"":{""Action"":""\""- the DRF decrease load traffic to the SDM at the level that reach below CPU level 1 in between the time period duration of the previous report.\n- the SDM send a DOIC report requesting an discarding proportion at value 0%, and overload duration at value 0s\n\"""",""Data"":"""",""Expected Result"":""\""- Verify that DRF reduce the traffic load at 0% as resquested by the SDM\n- Verify that the sequence number of the report is incremented by 1\n\""""},""attachments"":[],""testVersionId"":1102672}]",,,,,,,,,,,,,,,"0|j1qcb4:",,,,,,,,,,,,,,4.0,,,,,,,,,,,"{""issueId"":8938969,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R19.1 (V900R019C10SPC319) feature non regression/DOIC (in front of SPS Huawei),SANTSDM-1907,,Manual,TODO,,,,,,,,,,
DOIC: Capability Negotiation,SANTSDM-1777,8938968,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:31 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,Manual,NonFunctional,Regression,System,,,,,"SUMMARY : Test performed with OTIP59 (MME simulator) in front of SDM0.

The goal of this test is to check that the peer node supports DOIC



ULR with:


	AVP OC-Supported-Features


HSS9860 checks if the requests contains the OC-Supported-Features AVP

ULA with:


	AVP OC-Supported-Features


PRECONDITIONS : - SDM Huawei is configured as reporting node (Diameter server)
- Simulated MME is configured as reacting node (Diameter client)

The server is directly connected with the client.


	In the HSS9860 OM system window


The peer overload reports are used.

MOD DOICCFG with DOIC switch set to ON(ON)

ADD DOICPEER to add a DOIC peer (MME simulator)

MOD DOICCFG to configure the message discard ratio and DOIC validity period


	The CPU load level 1 is set to 2%
	The CPU load level 2 is set to 10%


The overload message reduction ratio is configured with 5 levels but we will use only the 2 first:


	CPU load level 1 -- 2% of messages discarding
	CPU load level 2 -- 5% of messages discarding


The overload report validity duration is 300s",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1777,,,,,,,,software,,,,,,"[{""id"":3844430,""index"":1,""fields"":{""Action"":""\""-The SAE-HSS can process the OC-Supported-Features AVP\n\"""",""Data"":"""",""Expected Result"":""\""ULR with:\n\n\n\tAVP OC-Supported-Features\n\n\nULA with:\n\n\n\tAVP OC-Supported-Features\n\n\""""},""attachments"":[],""testVersionId"":1102671}]",,,,,,,,,,,,,,,"0|j1qcaw:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8938968,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R19.1 (V900R019C10SPC319) feature non regression/DOIC (standalone SDM),SANTSDM-1907,,Manual,TODO,,,,,,,,,,
DOIC: to CPU load level 1,SANTSDM-1776,8938967,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:31 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,Manual,NonFunctional,Regression,System,,,,,"SUMMARY : Test performed with OTIP59 (MME simulator) in front of SDM0.

The goal of this test is to reach the CPU load level 1 (2%)



ULR with:


	AVP OC-Supported-Features


CPU load level under level 1 (2%)

ULA with:


	AVP OC-Supported-Features


CPU load level up to level 1 (=2%)

ULA with:


	AVP OC-Supported-Features
	AVP OC-OLR / OC-sequence-Number = ""SN=0""
	AVP OC-OLR / OC-Report-Type = HOST_REPORT
	AVP OC-OLR / OC-Reduction-Percentage = ""Reduction Percentage for load level 1""
	AVP OC-OLR / OC-Validity-Duration




After that, if the CPU load level doesnt change, the OC-Sequence-Number doesnt change .

PRECONDITIONS : - SDM Huawei is configured as reporting node (Diameter server)
- Simulated MME is configured as reacting node (Diameter client)

The server is directly connected with the client.


	In the HSS9860 OM system window


The peer overload reports are used.

MOD DOICCFG with DOIC switch set to ON(ON).

ADD DOICPEER to add a DOIC peer (MME simulator)

MOD DOICCFG to configure the message discard ratio and DOIC validity period


	The CPU load level 1 is set to 2%
	The CPU load level 2 is set to 10%


The reduction percentage is configured with 5 levels but we will use only the 2 first:


	CPU load level 1 -- 2% of messages discarding
	CPU load level 2 -- 5% of messages discarding


The overload report validity duration is 300s",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1776,,,,,,,,software,,,,,,"[{""id"":3844422,""index"":1,""fields"":{""Action"":""\""- On S6a interface messages are exchanged between SDM and simulated MME\n- These messages carry the support from DRF of DOIC feature (OC-Supported-feature AVP)\n\n- Set the simulated traffic load to 1000 msg/s to reach the CPU load level 1 (2%)\n\"""",""Data"":"""",""Expected Result"":""\""\n\n\n\n\""""},""attachments"":[],""testVersionId"":1102670},{""id"":3844423,""index"":2,""fields"":{""Action"":""\""None\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102670},{""id"":3844424,""index"":3,""fields"":{""Action"":""\""- The CPU load level is under the level 1 (2%)\n\"""",""Data"":"""",""Expected Result"":""\""ULR with:\n\n\n\tAVP OC-Supported-Features\n\n\nULA with:\n\n\n\tAVP OC-Supported-Features\n\n\""""},""attachments"":[],""testVersionId"":1102670},{""id"":3844425,""index"":4,""fields"":{""Action"":""\""None\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102670},{""id"":3844426,""index"":5,""fields"":{""Action"":""\""- The CPU load level is up to level 1 (\u003d2%)\n\"""",""Data"":"""",""Expected Result"":""\""ULR with:\n\n\n\tAVP OC-Supported-Features\n\n\nULA with:\n\n\n\tAVP OC-Supported-Features\n\tAVP OC-OLR / OC-sequence-Number \u003d \""SN\u003d0\""\n\tAVP OC-OLR / OC-Report-Type \u003d HOST_REPORT\n\tAVP OC-OLR / OC-Reduction-Percentage \u003d 2\n\tAVP OC-OLR / OC-Validity-Duration \u003d 300\n\n\""""},""attachments"":[],""testVersionId"":1102670},{""id"":3844427,""index"":6,""fields"":{""Action"":""\""None\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102670},{""id"":3844428,""index"":7,""fields"":{""Action"":""\""None\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102670},{""id"":3844429,""index"":8,""fields"":{""Action"":""\""The CPU load level doesnt change\n\"""",""Data"":"""",""Expected Result"":""\""The OC-Sequence-Number doesnt change\n\""""},""attachments"":[],""testVersionId"":1102670}]",,,,,,,,,,,,,,,"0|j1qcao:",,,,,,,,,,,,,,8.0,,,,,,,,,,,"{""issueId"":8938967,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R19.1 (V900R019C10SPC319) feature non regression/DOIC (standalone SDM),SANTSDM-1907,,Manual,TODO,,,,,,,,,,
DOIC: from CPU load level 1 to CPU load level 2,SANTSDM-1775,8938966,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:31 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,Manual,NonFunctional,Regression,System,,,,,"SUMMARY : Test performed with OTIP59 (MME simulator) in front of SDM0.

The goal of this test is to increase the CPU load level from level 1 (2%) to level 2 (10%)



ULR with:


	AVP OC-Supported-Features


CPU load level 1 (2%)

ULA with:


	AVP OC-Supported-Features
	AVP OC-OLR / OC-sequence-Number = ""old SN""
	AVP OC-OLR / OC-Report-Type = HOST_REPORT
	AVP OC-OLR / OC-Reduction-Percentage = ""Reduction Percentage for load level 1""
	AVP OC-OLR / OC-Validity-Duration


CPU load level 2 (10%)

ULA with:


	AVP OC-Supported-Features
	AVP OC-OLR / OC-sequence-Number = ""new SN""
	AVP OC-OLR / OC-Report-Type = HOST_REPORT
	AVP OC-OLR / OC-Reduction-Percentage = ""Reduction Percentage for load level 2""
	AVP OC-OLR / OC-Validity-Duration




After that, if the CPU load level doesnt change, the OC-Sequence-Number doesnt change .

PRECONDITIONS : - SDM Huawei is configured as reporting node (Diameter server)
- Simulated MME is configured as reacting node (Diameter client)

The server is directly connected with the client.


	In the HSS9860 OM system window


The peer overload reports are used.

MOD DOICCFG with DOIC switch set to ON(ON).

ADD DOICPEER to add a DOIC peer (MME simulator)

MOD DOICCFG to configure the message discard ratio and DOIC validity period




	The CPU load level 1 is set to 2%
	The CPU load level 2 is set to 10%




The reduction percentage is configured with 5 levels but we will use only the 2 first:


	CPU load level 1 -- 2% of messages discarding
	CPU load level 2 -- 5% of messages discarding


The overload report validity duration is 300s",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1775,,,,,,,,software,,,,,,"[{""id"":3844416,""index"":1,""fields"":{""Action"":""\""- On S6a interface messages are exchanged between SDM and simulated MME\n- These messages carry the support from DRF of DOIC feature (OC-Supported-feature AVP)\n\n- Set the simulated traffic load to 2500 msg/s to increase the CPU load level from level 1 (5%) to level 2 (10%)\n\"""",""Data"":"""",""Expected Result"":""\""\n\n\n\n\""""},""attachments"":[],""testVersionId"":1102669},{""id"":3844417,""index"":2,""fields"":{""Action"":""\""- The CPU load level is on level 1 (2%)\n\"""",""Data"":"""",""Expected Result"":""\""ULR with:\n\n\n\tAVP OC-Supported-Features\n\n\nULA with:\n\n\n\tAVP OC-Supported-Features\n\tAVP OC-OLR / OC-sequence-Number \u003d \""old SN\""\n\tAVP OC-OLR / OC-Report-Type \u003d HOST_REPORT\n\tAVP OC-OLR / OC-Reduction-Percentage \u003d 2\n\tAVP OC-OLR / OC-Validity-Duration \u003d 300\n\n\""""},""attachments"":[],""testVersionId"":1102669},{""id"":3844418,""index"":3,""fields"":{""Action"":""\""None\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102669},{""id"":3844419,""index"":4,""fields"":{""Action"":""\""- The CPU load level is on level 2 (10%)\n\"""",""Data"":"""",""Expected Result"":""\""ULR with:\n\n\n\tAVP OC-Supported-Features\n\n\nULA with:\n\n\n\tAVP OC-Supported-Features\n\tAVP OC-OLR / OC-sequence-Number \u003d \""new SN\""\n\tAVP OC-OLR / OC-Report-Type \u003d HOST_REPORT\n\tAVP OC-OLR / OC-Reduction-Percentage \u003d 5\n\tAVP OC-OLR / OC-Validity-Duration \u003d 300\n\n\""""},""attachments"":[],""testVersionId"":1102669},{""id"":3844420,""index"":5,""fields"":{""Action"":""\""None\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102669},{""id"":3844421,""index"":6,""fields"":{""Action"":""\""The CPU load level doesnt change\n\"""",""Data"":"""",""Expected Result"":""\""The 0C-Sequence-Number doesnt change\n\""""},""attachments"":[],""testVersionId"":1102669}]",,,,,,,,,,,,,,,"0|j1qcag:",,,,,,,,,,,,,,6.0,,,,,,,,,,,"{""issueId"":8938966,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R19.1 (V900R019C10SPC319) feature non regression/DOIC (standalone SDM),SANTSDM-1907,,Manual,TODO,,,,,,,,,,
DOIC: from CPU load level 2 to CPU load level 1,SANTSDM-1774,8938965,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:43 PM,15/Jul/25 5:31 PM,16/Jul/25 11:08 AM,,,,HUAWEI-SPECIFIC,,0,Manual,NonFunctional,Regression,System,,,,,"SUMMARY : Test performed with OTIP59 (MME simulator) in front of SDM0.

The goal of this test is to decrease the CPU load level from level 2 (10%) to level 1 (2%)



ULR with:


	AVP OC-Supported-Features


CPU load level 2 (10%)

ULA with:


	AVP OC-Supported-Features
	AVP OC-OLR / OC-sequence-Number = ""old SN""
	AVP OC-OLR / OC-Report-Type = HOST_REPORT
	AVP OC-OLR / OC-Reduction-Percentage = ""Reduction Percentage for load level 2""
	AVP OC-OLR / OC-Validity-Duration


CPU load level 1 (2%)

ULA with:


	AVP OC-Supported-Features
	AVP OC-OLR / OC-sequence-Number = ""new SN""
	AVP OC-OLR / OC-Report-Type = HOST_REPORT
	AVP OC-OLR / OC-Reduction-Percentage = ""Reduction Percentage for load level 1""
	AVP OC-OLR / OC-Validity-Duration




After that, if the CPU load level doesnt change, the OC-Sequence-Number doesnt change .

PRECONDITIONS : - SDM ZTE is configured as reporting node (Diameter server)
- Simulated MME is configured as reacting node (Diameter client)

The server is directly connected with the client.


	In the HSS9860 OM system window


The peer overload reports are used.

MOD DOICCFG with DOIC switch set to ON(ON).

ADD DOICPEER to add a DOIC peer (MME simulator)

MOD DOICCFG to configure the message discard ratio and DOIC validity period




	The CPU load level 1 is set to 2%
	The CPU load level 2 is set to 10%


The reduction percentage is configured with 5 levels but we will use only the 2 first:


	CPU load level 1 -- 2% of messages discarding
	CPU load level 2 -- 5% of messages discarding


The overload report validity duration is 10s",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1774,,,,,,,,software,,,,,,"[{""id"":3844411,""index"":1,""fields"":{""Action"":""\""- On S6a interface messages are exchanged between SDM and simulated MME\n- These messages carry the support from DRF of DOIC feature (OC-Supported-feature AVP)\n\n- Set the simulated traffic load up to 1000 msg/s to decrease the CPU load level from level 2 (10%) to level 1 (5%)\n\"""",""Data"":"""",""Expected Result"":""\""\n\n\n\n\""""},""attachments"":[],""testVersionId"":1102668},{""id"":3844412,""index"":2,""fields"":{""Action"":""\""- The CPU load level is on level 2 (10%)\n\"""",""Data"":"""",""Expected Result"":""\""ULR with:\n\n\n\tAVP OC-Supported-Features\n\n\nULA with:\n\n\n\tAVP OC-Supported-Features\n\tAVP OC-OLR / OC-sequence-Number \u003d \""old SN\""\n\tAVP OC-OLR / OC-Report-Type \u003d HOST_REPORT\n\tAVP OC-OLR / OC-Reduction-Percentage \u003d 5\n\tAVP OC-OLR / OC-Validity-Duration \u003d 10\n\n\""""},""attachments"":[],""testVersionId"":1102668},{""id"":3844413,""index"":3,""fields"":{""Action"":""\""- The CPU load level is on level 1 (2%)\n\"""",""Data"":"""",""Expected Result"":""\""ULR with:\n\n\n\tAVP OC-Supported-Features\n\n\nULA with:\n\n\n\tAVP OC-Supported-Features\n\tAVP OC-OLR / OC-sequence-Number \u003d \""new SN\""\n\tAVP OC-OLR / OC-Report-Type \u003d HOST_REPORT\n\tAVP OC-OLR / OC-Reduction-Percentage \u003d 2\n\tAVP OC-OLR / OC-Validity-Duration \u003d 10\n\n\""""},""attachments"":[],""testVersionId"":1102668},{""id"":3844414,""index"":4,""fields"":{""Action"":""\""None\"""",""Data"":"""",""Expected Result"":""\""None\""""},""attachments"":[],""testVersionId"":1102668},{""id"":3844415,""index"":5,""fields"":{""Action"":""\""The CPU load level doesnt change\n\"""",""Data"":"""",""Expected Result"":""\""The 0C-Sequence-Number doesnt change\n\""""},""attachments"":[],""testVersionId"":1102668}]",,,,,,,,,,,,,,,"0|j1qca8:",,,,,,,,,,,,,,5.0,,,,,,,,,,,"{""issueId"":8938965,""testStatuses"":[]}",,/HUAWEI NON REGRESSION FEATURE TESTCASES/HUAWEI R19.1 (V900R019C10SPC319) feature non regression/DOIC (standalone SDM),SANTSDM-1907,,Manual,TODO,,,,,,,,,,