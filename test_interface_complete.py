#!/usr/bin/env python3
"""
Test complet de l'interface web d'enrichissement
"""

import urllib.request
import urllib.parse
import json
import os
import time

def test_upload_and_enrich():
    """Test complet : upload + enrichissement"""
    
    print("🧪 Test complet de l'interface web")
    print("=" * 50)
    
    # 1. Vérifier que le serveur est accessible
    try:
        with urllib.request.urlopen("http://127.0.0.1:5000", timeout=5) as response:
            print("✅ Serveur Flask accessible")
    except:
        print("❌ Serveur Flask non accessible")
        return False
    
    # 2. Tester l'enrichissement avec un fichier existant
    file_id = "cb08b81d-b6dd-4971-ace6-4f4f2a65ee14"
    
    print(f"\n🔄 Test enrichissement avec file_id: {file_id}")
    
    # URL de l'API
    url = f"http://127.0.0.1:5000/process/{file_id}"
    
    # Données à envoyer
    data = {"format": "separate"}
    json_data = json.dumps(data).encode('utf-8')
    
    # Créer la requête
    req = urllib.request.Request(
        url, 
        data=json_data,
        headers={
            'Content-Type': 'application/json',
            'Content-Length': len(json_data)
        },
        method='POST'
    )
    
    try:
        print(f"📤 Envoi de la requête...")
        
        # Envoyer la requête
        with urllib.request.urlopen(req, timeout=60) as response:
            response_data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            print(f"📥 Status: {status_code}")
            
            # Parser la réponse JSON
            try:
                result = json.loads(response_data)
                print(f"📊 Réponse: {result}")
                
                if result.get('success'):
                    print("✅ Enrichissement réussi!")
                    
                    # Vérifier que le fichier de sortie existe
                    output_file = f"processed/enriched_{file_id}.csv"
                    if os.path.exists(output_file):
                        print(f"✅ Fichier de sortie créé: {output_file}")
                        
                        # Vérifier le contenu
                        with open(output_file, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            print(f"📊 Nombre de lignes: {len(lines)}")
                            if len(lines) > 0:
                                headers = lines[0].strip().split(',')
                                labels_count = sum(1 for h in headers if h.startswith('Labels'))
                                print(f"🏷️ Colonnes Labels trouvées: {labels_count}")
                                
                                if labels_count > 0:
                                    print("✅ Format Labels multiples confirmé!")
                                    return True
                                else:
                                    print("❌ Aucune colonne Labels trouvée")
                                    return False
                    else:
                        print(f"❌ Fichier de sortie non trouvé: {output_file}")
                        return False
                else:
                    print(f"❌ Erreur dans la réponse: {result.get('message')}")
                    return False
                    
            except json.JSONDecodeError:
                print(f"❌ Réponse non-JSON: {response_data}")
                return False
                
    except Exception as e:
        print(f"❌ Erreur de requête: {e}")
        return False

def test_download():
    """Test de téléchargement du fichier enrichi"""
    
    file_id = "cb08b81d-b6dd-4971-ace6-4f4f2a65ee14"
    download_url = f"http://127.0.0.1:5000/download/enriched_{file_id}.csv"
    
    print(f"\n📥 Test de téléchargement: {download_url}")
    
    try:
        with urllib.request.urlopen(download_url, timeout=10) as response:
            status_code = response.getcode()
            content_length = response.headers.get('Content-Length')
            
            print(f"📥 Status: {status_code}")
            print(f"📊 Taille: {content_length} bytes")
            
            if status_code == 200:
                print("✅ Téléchargement fonctionnel!")
                return True
            else:
                print(f"❌ Erreur de téléchargement: {status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Erreur de téléchargement: {e}")
        return False

if __name__ == "__main__":
    print("🎯 TEST COMPLET DE L'INTERFACE WEB")
    print("=" * 60)
    
    # Test 1: Enrichissement
    success1 = test_upload_and_enrich()
    
    # Test 2: Téléchargement
    success2 = test_download()
    
    print("\n" + "=" * 60)
    print("📋 RÉSULTATS DES TESTS:")
    print(f"   Enrichissement: {'✅ RÉUSSI' if success1 else '❌ ÉCHOUÉ'}")
    print(f"   Téléchargement: {'✅ RÉUSSI' if success2 else '❌ ÉCHOUÉ'}")
    
    if success1 and success2:
        print("\n🎉 TOUS LES TESTS RÉUSSIS - INTERFACE FONCTIONNELLE!")
    else:
        print("\n💥 CERTAINS TESTS ONT ÉCHOUÉ")
        
    print("=" * 60)
