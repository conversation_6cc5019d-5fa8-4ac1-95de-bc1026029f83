{% extends "base.html" %}

{% block title %}Analyse Test Sets - Enrichissement Tests Jira Xray{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i> Analyse des Test Sets - Expert Core Network
                </h3>
            </div>
            <div class="card-body">
                
                <!-- Informations sur le fichier -->
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> Fichier à Analyser</h5>
                    <p><strong>ID:</strong> {{ file_id }}</p>
                    <p><strong>Type:</strong> <span id="file-type">Enrichi</span></p>
                    <p><strong>Statut:</strong> <span id="analysis-status" class="badge bg-secondary">En attente</span></p>
                </div>

                <!-- Étapes d'analyse -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-search fa-3x text-primary mb-3"></i>
                                <h5>1. Analyse de Cohérence</h5>
                                <p class="small">Vérification associations tests ↔ test sets selon standards 3GPP</p>
                                <button id="btn-analyze" class="btn btn-primary" onclick="startAnalysis()">
                                    <i class="fas fa-play"></i> Démarrer l'Analyse
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-tools fa-3x text-warning mb-3"></i>
                                <h5>2. Correction Automatique</h5>
                                <p class="small">Application des recommandations d'amélioration</p>
                                <button id="btn-correct" class="btn btn-warning" onclick="startCorrection()" disabled>
                                    <i class="fas fa-magic"></i> Corriger Automatiquement
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-pie fa-3x text-success mb-3"></i>
                                <h5>3. Tableau de Bord</h5>
                                <p class="small">Métriques de qualité et conformité</p>
                                <button id="btn-dashboard" class="btn btn-success" onclick="generateDashboard()" disabled>
                                    <i class="fas fa-chart-bar"></i> Générer Dashboard
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Zone de résultats -->
                <div id="results-section" style="display: none;">
                    
                    <!-- Résultats d'analyse -->
                    <div id="analysis-results" class="card mt-4" style="display: none;">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-clipboard-list"></i> Résultats d'Analyse
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 id="total-tests" class="text-primary">-</h3>
                                        <p class="mb-0">Tests Analysés</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 id="misclassified-tests" class="text-warning">-</h3>
                                        <p class="mb-0">Tests Mal Classés</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 id="missing-labels" class="text-danger">-</h3>
                                        <p class="mb-0">Labels Manquants</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 id="missing-3gpp" class="text-info">-</h3>
                                        <p class="mb-0">Références 3GPP</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Recommandations -->
                            <div class="mt-4">
                                <h6><i class="fas fa-lightbulb"></i> Recommandations Prioritaires</h6>
                                <div id="recommendations-list">
                                    <!-- Sera rempli dynamiquement -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Résultats de correction -->
                    <div id="correction-results" class="card mt-4" style="display: none;">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-check-circle"></i> Corrections Appliquées
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 id="test-sets-changed" class="text-primary">-</h3>
                                        <p class="mb-0">Test Sets Modifiés</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 id="labels-added" class="text-success">-</h3>
                                        <p class="mb-0">Labels Ajoutés</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 id="gpp-refs-added" class="text-info">-</h3>
                                        <p class="mb-0">Références 3GPP</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 id="duplicates-removed" class="text-warning">-</h3>
                                        <p class="mb-0">Doublons Supprimés</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Fichier corrigé -->
                            <div class="mt-4 text-center">
                                <a id="download-corrected" href="#" class="btn btn-success btn-lg" style="display: none;">
                                    <i class="fas fa-download"></i> Télécharger Fichier Corrigé
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Tableau de bord qualité -->
                    <div id="dashboard-results" class="card mt-4" style="display: none;">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-pie"></i> Tableau de Bord Qualité
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="text-center">
                                        <h2 id="quality-score" class="text-success">-</h2>
                                        <p class="mb-0">Score de Conformité Global</p>
                                        <span id="quality-level" class="badge bg-success">-</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="text-center">
                                        <h2 id="health-status" class="text-info">-</h2>
                                        <p class="mb-0">Santé Globale</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Métriques détaillées -->
                            <div class="row mt-4">
                                <div class="col-md-3">
                                    <div class="metric-card">
                                        <h4 id="alignment-score" class="text-primary">-</h4>
                                        <p>Alignement Test Sets</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="metric-card">
                                        <h4 id="completeness-score" class="text-success">-</h4>
                                        <p>Complétude Labels</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="metric-card">
                                        <h4 id="gpp-coverage-score" class="text-info">-</h4>
                                        <p>Couverture 3GPP</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="metric-card">
                                        <h4 id="consistency-score" class="text-warning">-</h4>
                                        <p>Cohérence Labels</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Actions -->
                            <div class="mt-4 text-center">
                                <a id="view-dashboard" href="#" class="btn btn-primary btn-lg" target="_blank" style="display: none;">
                                    <i class="fas fa-external-link-alt"></i> Voir Tableau de Bord Complet
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions finales -->
                <div class="text-center mt-4">
                    <a href="{{ url_for('show_history') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Retour à l'Historique
                    </a>
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Nouveau Fichier
                    </a>
                </div>

            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
.metric-card {
    text-align: center;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    margin-bottom: 10px;
}
</style>

<script>
const fileId = '{{ file_id }}';
let analysisData = null;

// Démarrer l'analyse
async function startAnalysis() {
    const button = document.getElementById('btn-analyze');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyse en cours...';
    button.disabled = true;
    
    document.getElementById('analysis-status').textContent = 'Analyse en cours...';
    document.getElementById('analysis-status').className = 'badge bg-warning';
    
    try {
        const response = await fetch(`/analyze_file/${fileId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            analysisData = result.analysis;
            displayAnalysisResults(analysisData);
            
            document.getElementById('analysis-status').textContent = 'Analyse terminée';
            document.getElementById('analysis-status').className = 'badge bg-success';
            
            // Activer le bouton de correction
            document.getElementById('btn-correct').disabled = false;
        } else {
            showAlert('danger', result.message);
            document.getElementById('analysis-status').textContent = 'Erreur d\'analyse';
            document.getElementById('analysis-status').className = 'badge bg-danger';
        }
    } catch (error) {
        showAlert('danger', 'Erreur de communication avec le serveur');
        document.getElementById('analysis-status').textContent = 'Erreur';
        document.getElementById('analysis-status').className = 'badge bg-danger';
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Afficher les résultats d'analyse
function displayAnalysisResults(analysis) {
    document.getElementById('results-section').style.display = 'block';
    document.getElementById('analysis-results').style.display = 'block';
    
    // Statistiques principales
    document.getElementById('total-tests').textContent = analysis.total_tests;
    document.getElementById('misclassified-tests').textContent = analysis.misclassified_tests.length;
    document.getElementById('missing-labels').textContent = analysis.missing_labels.length;
    document.getElementById('missing-3gpp').textContent = analysis.missing_3gpp_refs.length;
    
    // Recommandations
    const recommendationsList = document.getElementById('recommendations-list');
    recommendationsList.innerHTML = '';
    
    if (analysis.recommended_actions && analysis.recommended_actions.length > 0) {
        analysis.recommended_actions.forEach(rec => {
            const alertClass = rec.priority === 'HIGH' ? 'alert-danger' : 
                             rec.priority === 'MEDIUM' ? 'alert-warning' : 'alert-info';
            
            const recDiv = document.createElement('div');
            recDiv.className = `alert ${alertClass}`;
            recDiv.innerHTML = `
                <strong>[${rec.priority}] ${rec.type}</strong><br>
                ${rec.description}<br>
                <small><em>Nombre de tests concernés: ${rec.count}</em></small>
            `;
            recommendationsList.appendChild(recDiv);
        });
    } else {
        recommendationsList.innerHTML = '<div class="alert alert-success">Aucune amélioration nécessaire !</div>';
    }
}

// Démarrer la correction
async function startCorrection() {
    const button = document.getElementById('btn-correct');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Correction en cours...';
    button.disabled = true;
    
    try {
        const response = await fetch(`/correct_file/${fileId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            displayCorrectionResults(result.corrections);
            
            // Activer le bouton de dashboard
            document.getElementById('btn-dashboard').disabled = false;
            
            // Mettre à jour le type de fichier
            document.getElementById('file-type').textContent = 'Corrigé';
        } else {
            showAlert('danger', result.message);
        }
    } catch (error) {
        showAlert('danger', 'Erreur de communication avec le serveur');
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Afficher les résultats de correction
function displayCorrectionResults(corrections) {
    document.getElementById('correction-results').style.display = 'block';
    
    document.getElementById('test-sets-changed').textContent = corrections.test_set_changes;
    document.getElementById('labels-added').textContent = corrections.labels_added;
    document.getElementById('gpp-refs-added').textContent = corrections.gpp_refs_added;
    document.getElementById('duplicates-removed').textContent = corrections.duplicates_removed;
    
    // Lien de téléchargement
    const downloadLink = document.getElementById('download-corrected');
    downloadLink.href = `/download/corrected_${fileId}.csv`;
    downloadLink.style.display = 'inline-block';
}

// Générer le tableau de bord
async function generateDashboard() {
    const button = document.getElementById('btn-dashboard');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Génération...';
    button.disabled = true;
    
    try {
        const response = await fetch(`/quality_dashboard/${fileId}`, {
            method: 'GET'
        });
        
        const result = await response.json();
        
        if (result.success) {
            displayDashboardResults(result.dashboard_data, result.dashboard_file);
        } else {
            showAlert('danger', result.message);
        }
    } catch (error) {
        showAlert('danger', 'Erreur de communication avec le serveur');
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Afficher les résultats du tableau de bord
function displayDashboardResults(dashboardData, dashboardFile) {
    document.getElementById('dashboard-results').style.display = 'block';
    
    const compliance = dashboardData.compliance_score;
    const overview = dashboardData.overview;
    const quality = dashboardData.quality_metrics;
    
    // Score global
    document.getElementById('quality-score').textContent = compliance.overall_score + '%';
    document.getElementById('quality-level').textContent = compliance.level;
    document.getElementById('quality-level').className = `badge bg-${compliance.color}`;
    
    // Santé globale
    document.getElementById('health-status').textContent = overview.overall_health;
    
    // Métriques détaillées
    document.getElementById('alignment-score').textContent = quality.test_set_alignment.score + '%';
    document.getElementById('completeness-score').textContent = quality.label_completeness.score + '%';
    document.getElementById('gpp-coverage-score').textContent = quality.gpp_coverage.score + '%';
    document.getElementById('consistency-score').textContent = quality.label_consistency.score + '%';
    
    // Lien vers le tableau de bord complet
    const viewDashboard = document.getElementById('view-dashboard');
    viewDashboard.href = `/download/${dashboardFile}`;
    viewDashboard.style.display = 'inline-block';
}

// Fonction pour afficher des alertes
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);
}

// Guide d'analyse (fonction appelée depuis le menu)
function showAnalysisHelp() {
    const helpContent = `
    <div class="modal fade" id="analysisHelpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Guide d'Analyse des Test Sets</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>🎯 Méthodologie d'Expert Core Network</h6>
                    <p>Cette analyse suit les standards 3GPP et les bonnes pratiques ISTQB pour valider la cohérence de vos Test Sets.</p>
                    
                    <h6>📋 Étapes d'Analyse :</h6>
                    <ol>
                        <li><strong>Analyse de Cohérence</strong> : Vérification des associations tests ↔ test sets</li>
                        <li><strong>Correction Automatique</strong> : Application des recommandations</li>
                        <li><strong>Tableau de Bord</strong> : Métriques de qualité et conformité</li>
                    </ol>
                    
                    <h6>🔍 Critères Analysés :</h6>
                    <ul>
                        <li>Alignement des tests avec les Test Sets appropriés</li>
                        <li>Complétude des labels techniques (2G/4G, MAP/Diameter, etc.)</li>
                        <li>Références 3GPP (TS 23.082, TS 29.272, etc.)</li>
                        <li>Cohérence des classifications ISTQB</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', helpContent);
    new bootstrap.Modal(document.getElementById('analysisHelpModal')).show();
}
</script>
{% endblock %}
