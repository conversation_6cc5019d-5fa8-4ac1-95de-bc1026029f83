#!/usr/bin/env python3
"""
Processeur pour extraire les colonnes Action, Data, Expected Result
depuis le champ JSON "Custom field (Manual Test Steps)"
"""

import pandas as pd
import json
import csv
import re
from typing import Dict, List, Tuple

class JSONStepsProcessor:
    """
    Processeur pour extraire les étapes de test depuis les données JSON
    """
    
    def __init__(self):
        pass
    
    def parse_test_steps_json(self, steps_json: str) -> List[Dict[str, str]]:
        """
        Parse le JSON des étapes de test et retourne une liste d'étapes
        (Basé sur votre script qui fonctionne)

        Args:
            steps_json: Chaîne JSON contenant les étapes

        Returns:
            Liste de dictionnaires avec action, data, expected_result
        """
        if not steps_json or steps_json.strip() == '' or steps_json == '[]':
            return []

        try:
            # Parser le JSON
            steps_data = json.loads(steps_json)

            if not isinstance(steps_data, list):
                return []

            extracted_steps = []

            for step in steps_data:
                if isinstance(step, dict) and 'fields' in step:
                    fields = step['fields']

                    # Extraire les champs avec nettoyage basique
                    action = self.clean_text(fields.get('Action', ''))
                    data = self.clean_text(fields.get('Data', ''))
                    expected_result = self.clean_text(fields.get('Expected Result', ''))

                    extracted_steps.append({
                        'action': action,
                        'data': data,
                        'expected_result': expected_result
                    })

            return extracted_steps

        except json.JSONDecodeError as e:
            print(f"⚠️ Erreur de parsing JSON: {e}")
            return []
        except Exception as e:
            print(f"⚠️ Erreur inattendue lors du parsing: {e}")
            return []

    def clean_text(self, text: str) -> str:
        """
        Nettoie le texte en supprimant les caractères d'échappement basiques
        (Basé sur votre script qui fonctionne)

        Args:
            text: Texte à nettoyer

        Returns:
            Texte nettoyé
        """
        if not text:
            return ''

        # Remplacer les caractères d'échappement courants
        text = text.replace('\\n', '\n')
        text = text.replace('\\t', '\t')
        text = text.replace('\u003d', '=')
        text = text.replace('""', '"')

        # Supprimer les guillemets en début et fin si présents
        text = text.strip()
        if text.startswith('"') and text.endswith('"'):
            text = text[1:-1]

        # Supprimer les guillemets d'échappement
        text = text.replace('\\"', '"')

        return text.strip()
    
    def _extract_with_regex(self, text: str) -> Dict[str, str]:
        """
        Extraction de secours avec des expressions régulières
        """
        result = {
            'Action': '',
            'Data': '',
            'Expected Result': ''
        }
        
        try:
            # Patterns pour extraire les champs
            action_pattern = r'"Action"\s*:\s*"([^"]*)"'
            data_pattern = r'"Data"\s*:\s*"([^"]*)"'
            expected_pattern = r'"Expected Result"\s*:\s*"([^"]*)"'
            
            action_match = re.search(action_pattern, text)
            data_match = re.search(data_pattern, text)
            expected_match = re.search(expected_pattern, text)
            
            if action_match:
                result['Action'] = action_match.group(1).strip()
            if data_match:
                result['Data'] = data_match.group(1).strip()
            if expected_match:
                result['Expected Result'] = expected_match.group(1).strip()
        
        except Exception as e:
            print(f"Erreur lors de l'extraction regex : {e}")
        
        return result
    
    def process_csv_file(self, input_file: str, output_file: str, progress_callback=None) -> Dict[str, int]:
        """
        Traite un fichier CSV pour extraire les colonnes Action, Data, Expected Result
        Utilise la même logique que votre script qui fonctionne
        """
        print(f"🚀 Début de l'extraction des étapes de test")
        print(f"📁 Fichier d'entrée: {input_file}")
        print(f"📁 Fichier de sortie: {output_file}")

        processed_tickets = 0
        total_steps = 0
        error_count = 0

        try:
            import csv

            # Lire le fichier d'entrée
            with open(input_file, 'r', newline='', encoding='utf-8') as csv_in:
                reader = csv.DictReader(csv_in)

                # Conserver TOUTES les colonnes originales
                available_columns = list(reader.fieldnames)
                print(f"📋 Colonnes originales trouvées: {len(available_columns)}")
                for col in available_columns:
                    print(f"   - {col}")

                # Ajouter les nouvelles colonnes d'étapes
                test_step_columns = ['Action', 'Data', 'Expected Result']
                output_fieldnames = available_columns + test_step_columns

                print(f"📋 Colonnes totales dans le fichier final: {len(output_fieldnames)}")
                print(f"📋 Colonnes d'étapes ajoutées: Action, Data, Expected Result")

                # Collecter toutes les lignes à écrire
                output_rows = []

                for row_num, row in enumerate(reader, 1):
                    try:
                        # Récupérer le JSON des étapes de test
                        steps_json = row.get('Custom field (Manual Test Steps)', '')

                        # Parser le JSON
                        steps = self.parse_test_steps_json(steps_json)

                        if steps:
                            # Créer une ligne pour chaque étape
                            for step in steps:
                                new_row = {}

                                # Copier TOUTES les colonnes originales
                                for col in available_columns:
                                    new_row[col] = row.get(col, '')

                                # Ajouter les colonnes d'étapes
                                new_row['Action'] = step['action']
                                new_row['Data'] = step['data']
                                new_row['Expected Result'] = step['expected_result']

                                output_rows.append(new_row)
                                total_steps += 1
                        else:
                            # Pas d'étapes, créer une ligne vide pour les étapes
                            new_row = {}

                            # Copier TOUTES les colonnes originales
                            for col in available_columns:
                                new_row[col] = row.get(col, '')

                            new_row['Action'] = ''
                            new_row['Data'] = ''
                            new_row['Expected Result'] = ''

                            output_rows.append(new_row)

                        processed_tickets += 1

                        if processed_tickets % 50 == 0:
                            print(f"📊 Traité {processed_tickets} tickets, {total_steps} étapes extraites...")

                    except Exception as e:
                        print(f"❌ Erreur ligne {row_num}: {e}")
                        error_count += 1
                        continue

            # Écrire le fichier de sortie avec des VIRGULES comme séparateur
            with open(output_file, 'w', newline='', encoding='utf-8') as csv_out:
                writer = csv.DictWriter(csv_out, fieldnames=output_fieldnames,
                                      delimiter=',', quoting=csv.QUOTE_MINIMAL)
                writer.writeheader()

                print(f"📁 Écriture avec séparateur virgule (,) pour compatibilité enrichissement")

                # Écrire les données en gardant toutes les colonnes
                for row in output_rows:
                    writer.writerow(row)

        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture des fichiers: {e}")
            return {'processed': 0, 'extracted': 0, 'errors': error_count, 'duplicated_rows': 0}

        # Résumé final
        print(f"✅ Extraction terminée!")
        print(f"📊 Statistiques:")
        print(f"   - Tickets traités: {processed_tickets}")
        print(f"   - Étapes extraites: {total_steps}")
        print(f"   - Erreurs: {error_count}")
        print(f"📁 Fichier généré: {output_file}")
        print(f"📋 Format: CSV avec séparateur virgule (,) - Compatible enrichissement")

        duplicated_rows = total_steps - processed_tickets if total_steps > processed_tickets else 0

        return {
            'processed': processed_tickets,
            'extracted': total_steps,
            'errors': error_count,
            'duplicated_rows': duplicated_rows
        }
    
    def preview_json_extraction(self, csv_file: str, max_samples: int = 5) -> List[Dict]:
        """
        Aperçu de l'extraction JSON pour validation
        """
        try:
            df = pd.read_csv(csv_file)
        except:
            try:
                df = pd.read_csv(csv_file, sep=';')
            except:
                return []

        json_column = 'Custom field (Manual Test Steps)'
        if json_column not in df.columns:
            possible_columns = [col for col in df.columns if 'manual' in col.lower() and 'test' in col.lower()]
            if possible_columns:
                json_column = possible_columns[0]
            else:
                return []

        samples = []
        count = 0

        for index, row in df.iterrows():
            if count >= max_samples:
                break

            json_content = row[json_column]
            if json_content and pd.notna(json_content) and str(json_content).strip():
                # Utiliser la nouvelle méthode parse_test_steps_json
                steps_list = self.parse_test_steps_json(json_content)

                # Créer un échantillon pour CHAQUE step
                if steps_list:
                    for step_index, step in enumerate(steps_list):
                        extracted = {
                            'Action': step['action'],
                            'Data': step['data'],
                            'Expected Result': step['expected_result']
                        }

                        # Indiquer le numéro du step s'il y en a plusieurs
                        step_suffix = f" (Step {step_index + 1}/{len(steps_list)})" if len(steps_list) > 1 else ""

                        sample = {
                            'row': index + 1,
                            'summary': row.get('Summary', 'N/A') + step_suffix,
                            'json_preview': str(json_content)[:100] + '...' if len(str(json_content)) > 100 else str(json_content),
                            'extracted': extracted
                        }
                        samples.append(sample)
                        count += 1

                        # Limiter le nombre total d'échantillons
                        if count >= max_samples:
                            break
                else:
                    # JSON vide ou invalide
                    extracted = {
                        'Action': '',
                        'Data': '',
                        'Expected Result': ''
                    }

                    sample = {
                        'row': index + 1,
                        'summary': row.get('Summary', 'N/A'),
                        'json_preview': str(json_content)[:100] + '...' if len(str(json_content)) > 100 else str(json_content),
                        'extracted': extracted
                    }
                    samples.append(sample)
                    count += 1

        return samples

def main():
    """
    Test du processeur JSON
    """
    print("=== TEST DU PROCESSEUR JSON STEPS ===")
    
    # Test avec des exemples JSON
    processor = JSONStepsProcessor()
    
    test_cases = [
        # Format liste avec fields
        '[{"id": 3763673, "fields": {"Action": "change HLR/EPC HSS profile...", "Data": "", "Expected Result": "Same data in SDM107 and SDM207"}}]',
        
        # Format objet direct
        '{"Action": "Test authentication", "Data": "User credentials", "Expected Result": "Authentication successful"}',
        
        # Format avec fields direct
        '{"fields": {"Action": "Execute backup", "Data": "Database backup", "Expected Result": "Backup completed"}}',
        
        # Chaîne vide
        '',
        
        # JSON malformé
        '{"Action": "Test incomplete'
    ]
    
    print("\n📋 Test d'extraction JSON :")
    for i, test_case in enumerate(test_cases):
        print(f"\n{i+1}. Test case :")
        print(f"   JSON : {test_case[:50]}{'...' if len(test_case) > 50 else ''}")
        
        result = processor.parse_test_steps_json(test_case)
        print(f"   Action : '{result['Action']}'")
        print(f"   Data : '{result['Data']}'")
        print(f"   Expected Result : '{result['Expected Result']}'")

# Test désactivé pour l'instant
# if __name__ == "__main__":
#     main()
