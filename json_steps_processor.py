#!/usr/bin/env python3
"""
Processeur pour extraire les colonnes Action, Data, Expected Result
depuis le champ JSON "Custom field (Manual Test Steps)"
"""

import pandas as pd
import json
import re
from typing import Dict, List, Tuple

class JSONStepsProcessor:
    """
    Processeur pour extraire les étapes de test depuis les données JSON
    """
    
    def __init__(self):
        pass
    
    def extract_test_steps(self, json_string: str) -> List[Dict[str, str]]:
        """
        Extrait TOUTES les étapes de test depuis une chaîne JSON

        Args:
            json_string: Chaîne JSON contenant les étapes de test

        Returns:
            Liste de Dict avec les clés 'Action', 'Data', 'Expected Result'
        """
        # Valeurs par défaut
        default_result = {
            'Action': '',
            'Data': '',
            'Expected Result': ''
        }

        if not json_string or pd.isna(json_string) or str(json_string).strip() == '':
            return [default_result]

        try:
            # Nettoyer la chaîne JSON si nécessaire
            json_clean = str(json_string).strip()
            results = []

            # Essayer de parser le JSON
            if json_clean.startswith('[') and json_clean.endswith(']'):
                # Format liste - traiter TOUS les éléments
                steps_list = json.loads(json_clean)
                if steps_list and len(steps_list) > 0:
                    for step in steps_list:
                        step_result = default_result.copy()
                        if isinstance(step, dict) and 'fields' in step:
                            fields = step['fields']
                            step_result['Action'] = str(fields.get('Action', '')).strip()
                            step_result['Data'] = str(fields.get('Data', '')).strip()
                            step_result['Expected Result'] = str(fields.get('Expected Result', '')).strip()
                        results.append(step_result)
                else:
                    results = [default_result]

            elif json_clean.startswith('{') and json_clean.endswith('}'):
                # Format objet direct
                step_obj = json.loads(json_clean)
                step_result = default_result.copy()
                if 'fields' in step_obj:
                    fields = step_obj['fields']
                    step_result['Action'] = str(fields.get('Action', '')).strip()
                    step_result['Data'] = str(fields.get('Data', '')).strip()
                    step_result['Expected Result'] = str(fields.get('Expected Result', '')).strip()
                else:
                    # Peut-être que les champs sont directement dans l'objet
                    step_result['Action'] = str(step_obj.get('Action', '')).strip()
                    step_result['Data'] = str(step_obj.get('Data', '')).strip()
                    step_result['Expected Result'] = str(step_obj.get('Expected Result', '')).strip()
                results = [step_result]
            else:
                results = [default_result]

        except json.JSONDecodeError as e:
            print(f"Erreur de parsing JSON : {e}")
            print(f"Contenu problématique : {json_string[:100]}...")

            # Essayer une extraction par regex en cas d'échec JSON
            regex_result = self._extract_with_regex(json_string)
            results = [regex_result]

        except Exception as e:
            print(f"Erreur inattendue lors de l'extraction : {e}")
            regex_result = self._extract_with_regex(json_string)
            results = [regex_result]

        # Nettoyer les valeurs "nan" et None pour tous les résultats
        for result in results:
            for key in result:
                if result[key] in ['nan', 'None', 'null', None]:
                    result[key] = ''

        return results if results else [default_result]
    
    def _extract_with_regex(self, text: str) -> Dict[str, str]:
        """
        Extraction de secours avec des expressions régulières
        """
        result = {
            'Action': '',
            'Data': '',
            'Expected Result': ''
        }
        
        try:
            # Patterns pour extraire les champs
            action_pattern = r'"Action"\s*:\s*"([^"]*)"'
            data_pattern = r'"Data"\s*:\s*"([^"]*)"'
            expected_pattern = r'"Expected Result"\s*:\s*"([^"]*)"'
            
            action_match = re.search(action_pattern, text)
            data_match = re.search(data_pattern, text)
            expected_match = re.search(expected_pattern, text)
            
            if action_match:
                result['Action'] = action_match.group(1).strip()
            if data_match:
                result['Data'] = data_match.group(1).strip()
            if expected_match:
                result['Expected Result'] = expected_match.group(1).strip()
        
        except Exception as e:
            print(f"Erreur lors de l'extraction regex : {e}")
        
        return result
    
    def process_csv_file(self, input_file: str, output_file: str, progress_callback=None) -> Dict[str, int]:
        """
        Traite un fichier CSV pour extraire les colonnes Action, Data, Expected Result
        Duplique les lignes pour les multiples steps

        Args:
            input_file: Fichier CSV d'entrée
            output_file: Fichier CSV de sortie
            progress_callback: Fonction de callback pour le progrès

        Returns:
            Dict avec les statistiques de traitement
        """
        print(f"Traitement du fichier CSV : {input_file}")

        # Lire le fichier CSV
        df = pd.read_csv(input_file)
        total_rows = len(df)

        print(f"Nombre de lignes à traiter : {total_rows}")
        print(f"Colonnes disponibles : {list(df.columns)}")

        # Vérifier si la colonne JSON existe
        json_column = 'Custom field (Manual Test Steps)'
        if json_column not in df.columns:
            print(f"⚠️  Colonne '{json_column}' non trouvée")
            # Chercher des variantes
            possible_columns = [col for col in df.columns if 'manual' in col.lower() and 'test' in col.lower()]
            if possible_columns:
                json_column = possible_columns[0]
                print(f"Utilisation de la colonne : {json_column}")
            else:
                print("Aucune colonne de test steps trouvée, création de colonnes vides")
                df['Action'] = ''
                df['Data'] = ''
                df['Expected Result'] = ''
                # Sauvegarder avec séparateur point-virgule
                df.to_csv(output_file, index=False, sep=';')
                return {'processed': 0, 'extracted': 0, 'errors': 0, 'duplicated_rows': 0}

        # Statistiques
        stats = {
            'processed': 0,
            'extracted': 0,
            'errors': 0,
            'duplicated_rows': 0
        }

        # Liste pour stocker toutes les nouvelles lignes
        new_rows = []

        # Traiter chaque ligne
        for index, row in df.iterrows():
            try:
                json_content = row[json_column]

                # Extraire TOUTES les étapes
                steps_list = self.extract_test_steps(json_content)

                stats['processed'] += 1

                # Créer une ligne pour chaque step
                for step_index, steps in enumerate(steps_list):
                    # Copier la ligne originale
                    new_row = row.copy()

                    # Ajouter les colonnes extraites
                    new_row['Action'] = steps['Action']
                    new_row['Data'] = steps['Data']
                    new_row['Expected Result'] = steps['Expected Result']

                    new_rows.append(new_row)

                    # Compter comme extrait si au moins un champ n'est pas vide
                    if any(steps[key] for key in steps):
                        stats['extracted'] += 1

                    # Compter les lignes dupliquées (sauf la première)
                    if step_index > 0:
                        stats['duplicated_rows'] += 1

                # Callback de progression
                if progress_callback:
                    progress = int((index + 1) / total_rows * 100)
                    progress_callback(progress, index + 1, total_rows)

            except Exception as e:
                print(f"Erreur ligne {index + 1}: {e}")
                stats['errors'] += 1
                # Ajouter la ligne originale sans modification en cas d'erreur
                new_row = row.copy()
                new_row['Action'] = ''
                new_row['Data'] = ''
                new_row['Expected Result'] = ''
                new_rows.append(new_row)

        # Créer le nouveau DataFrame avec toutes les lignes
        df_result = pd.DataFrame(new_rows)

        # Sauvegarder le fichier avec séparateur point-virgule et quoting pour protéger les JSON
        df_result.to_csv(output_file, index=False, sep=';', quoting=1)  # QUOTE_ALL

        print(f"Fichier traité sauvegardé : {output_file}")
        print(f"Lignes originales : {total_rows}")
        print(f"Lignes finales : {len(df_result)}")
        print(f"Lignes dupliquées : {stats['duplicated_rows']}")
        print(f"Statistiques : {stats}")

        return stats
    
    def preview_json_extraction(self, csv_file: str, max_samples: int = 5) -> List[Dict]:
        """
        Aperçu de l'extraction JSON pour validation
        """
        df = pd.read_csv(csv_file)
        
        json_column = 'Custom field (Manual Test Steps)'
        if json_column not in df.columns:
            possible_columns = [col for col in df.columns if 'manual' in col.lower() and 'test' in col.lower()]
            if possible_columns:
                json_column = possible_columns[0]
            else:
                return []
        
        samples = []
        count = 0
        
        for index, row in df.iterrows():
            if count >= max_samples:
                break
            
            json_content = row[json_column]
            if json_content and pd.notna(json_content) and str(json_content).strip():
                steps = self.extract_test_steps(json_content)
                
                sample = {
                    'row': index + 1,
                    'summary': row.get('Summary', 'N/A'),
                    'json_preview': str(json_content)[:100] + '...' if len(str(json_content)) > 100 else str(json_content),
                    'extracted': steps
                }
                samples.append(sample)
                count += 1
        
        return samples

def main():
    """
    Test du processeur JSON
    """
    print("=== TEST DU PROCESSEUR JSON STEPS ===")
    
    # Test avec des exemples JSON
    processor = JSONStepsProcessor()
    
    test_cases = [
        # Format liste avec fields
        '[{"id": 3763673, "fields": {"Action": "change HLR/EPC HSS profile...", "Data": "", "Expected Result": "Same data in SDM107 and SDM207"}}]',
        
        # Format objet direct
        '{"Action": "Test authentication", "Data": "User credentials", "Expected Result": "Authentication successful"}',
        
        # Format avec fields direct
        '{"fields": {"Action": "Execute backup", "Data": "Database backup", "Expected Result": "Backup completed"}}',
        
        # Chaîne vide
        '',
        
        # JSON malformé
        '{"Action": "Test incomplete'
    ]
    
    print("\n📋 Test d'extraction JSON :")
    for i, test_case in enumerate(test_cases):
        print(f"\n{i+1}. Test case :")
        print(f"   JSON : {test_case[:50]}{'...' if len(test_case) > 50 else ''}")
        
        result = processor.extract_test_steps(test_case)
        print(f"   Action : '{result['Action']}'")
        print(f"   Data : '{result['Data']}'")
        print(f"   Expected Result : '{result['Expected Result']}'")

if __name__ == "__main__":
    main()
