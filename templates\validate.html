{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Indicateur d'étapes -->
        <div class="step-indicator">
            <div class="step completed">
                <div class="step-number"><i class="fas fa-check"></i></div>
                <span>Télécharger</span>
            </div>
            <div class="step active">
                <div class="step-number">2</div>
                <span>Valider</span>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <span>Enrichir</span>
            </div>
            <div class="step">
                <div class="step-number">4</div>
                <span>Télécharger</span>
            </div>
        </div>

        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h3 class="card-title mb-0">
                    <i class="fas fa-check-circle"></i> Validation réussie !
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fas fa-file-csv"></i>
                    <strong>{{ filename }}</strong> - {{ message }}
                </div>

                <!-- Informations sur le fichier -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card stats-card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-chart-bar text-primary"></i> Statistiques du fichier
                                </h5>
                                <ul class="list-unstyled">
                                    <li><strong>Nombre de tests :</strong> {{ column_info.total_rows }}</li>
                                    <li><strong>Nombre de colonnes :</strong> {{ column_info.total_columns }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card stats-card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-columns text-info"></i> Colonnes détectées
                                </h5>
                                <div class="row">
                                    <div class="col-6">
                                        {% if column_info.has_summary %}
                                            <span class="badge bg-success mb-1">Summary ✓</span><br>
                                        {% endif %}
                                        {% if column_info.has_description %}
                                            <span class="badge bg-success mb-1">Description ✓</span><br>
                                        {% endif %}
                                        {% if column_info.has_component %}
                                            <span class="badge bg-success mb-1">Component/s ✓</span><br>
                                        {% endif %}
                                    </div>
                                    <div class="col-6">
                                        {% if column_info.has_action %}
                                            <span class="badge bg-success mb-1">Action ✓</span><br>
                                        {% endif %}
                                        {% if column_info.has_expected_result %}
                                            <span class="badge bg-success mb-1">Expected Result ✓</span><br>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Liste complète des colonnes -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-list"></i> Toutes les colonnes ({{ column_info.total_columns }})
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% for column in column_info.columns %}
                                <div class="col-md-4 mb-2">
                                    <span class="badge bg-light text-dark">{{ column }}</span>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Prédiction de l'enrichissement -->
                <div class="card mt-3">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-crystal-ball"></i> Prédiction de l'enrichissement
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-plus text-success"></i> Colonnes qui seront ajoutées :</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-tag text-primary"></i> <strong>Labels</strong> - Labels ISTQB automatiques</li>
                                    <li><i class="fas fa-layer-group text-info"></i> <strong>Test Set</strong> - Regroupement fonctionnel</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-edit text-warning"></i> Colonnes qui seront mises à jour :</h6>
                                <ul class="list-unstyled">
                                    {% if column_info.has_component %}
                                        <li><i class="fas fa-sitemap text-secondary"></i> <strong>Component/s</strong> - Si vide ou générique</li>
                                    {% else %}
                                        <li><i class="fas fa-sitemap text-success"></i> <strong>Component/s</strong> - Sera créée</li>
                                    {% endif %}
                                </ul>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle"></i>
                            <strong>Qualité prédite :</strong>
                            {% set quality_score = (column_info.has_summary and 1 or 0) + (column_info.has_description and 1 or 0) + (column_info.has_action and 1 or 0) + (column_info.has_expected_result and 1 or 0) %}
                            {% if quality_score >= 3 %}
                                <span class="badge bg-success">Excellente</span> - Toutes les informations nécessaires sont présentes
                            {% elif quality_score >= 2 %}
                                <span class="badge bg-warning">Bonne</span> - Enrichissement précis attendu
                            {% else %}
                                <span class="badge bg-secondary">Basique</span> - Enrichissement basé principalement sur le Summary
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Options d'enrichissement -->
                <div class="card mt-3">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-cogs"></i> Options d'Enrichissement
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">Format Standard</h6>
                                        <p class="card-text small">
                                            Tous les labels dans une seule colonne, séparés par des virgules
                                        </p>
                                        <div class="badge bg-info mb-2">Labels: "Functional, System, Manual"</div>
                                        <br>
                                        <a href="{{ url_for('enrich_file', file_id=file_id) }}?format=standard"
                                           class="btn btn-info">
                                            <i class="fas fa-magic"></i> Enrichir (Standard)
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">Format Jira Optimisé</h6>
                                        <p class="card-text small">
                                            Chaque label dans une colonne séparée (recommandé pour Jira)
                                        </p>
                                        <div class="badge bg-success mb-1">Labels: "Functional"</div>
                                        <div class="badge bg-success mb-1">Labels: "System"</div>
                                        <div class="badge bg-success mb-2">Labels: "Manual"</div>
                                        <br>
                                        <a href="{{ url_for('enrich_file', file_id=file_id) }}?format=separate"
                                           class="btn btn-success">
                                            <i class="fas fa-magic"></i> Enrichir (Jira)
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="text-center mt-4">
                    <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Choisir un autre fichier
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
