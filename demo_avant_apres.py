#!/usr/bin/env python3
"""
Démonstration AVANT/APRÈS pour montrer les améliorations
"""

import pandas as pd
import os
from test_enrichment_tool import TestEnrichmentTool
from test_enrichment_tool_v2 import TestEnrichmentToolV2
from test_set_corrector import TestSetCorrector

def print_header(title):
    print("\n" + "="*70)
    print(f"  {title}")
    print("="*70)

def show_file_structure(filename, title):
    """Affiche la structure d'un fichier CSV"""
    if not os.path.exists(filename):
        print(f"❌ Fichier {filename} non trouvé")
        return
    
    df = pd.read_csv(filename)
    print(f"\n📁 {title}")
    print(f"   Fichier: {filename}")
    print(f"   Tests: {len(df)}")
    print(f"   Colonnes: {len(df.columns)}")
    
    # Compter les colonnes Labels
    label_columns = [col for col in df.columns if col == 'Labels' or col.startswith('Labels')]
    print(f"   Colonnes Labels: {len(label_columns)}")
    
    # Afficher les en-têtes
    print(f"   En-têtes: {list(df.columns)}")
    
    # Afficher le premier test
    if len(df) > 0:
        print(f"\n   Premier test: {df.iloc[0]['Summary']}")
        print(f"   Composant: {df.iloc[0].get('Component/s', 'N/A')}")
        print(f"   Test Set: {df.iloc[0].get('Test Set', 'N/A')}")
        
        # Afficher les labels
        labels = []
        for col in df.columns:
            if col == 'Labels' or col.startswith('Labels'):
                value = df.iloc[0][col]
                if pd.notna(value) and value != '':
                    labels.append(str(value))
        
        print(f"   Labels: {labels}")
        print(f"   Nombre de labels: {len(labels)}")

def create_comparison():
    """Crée une comparaison complète AVANT/APRÈS"""
    
    print_header("🔬 DÉMONSTRATION AVANT/APRÈS - NOUVELLES FONCTIONNALITÉS")
    
    if not os.path.exists('example_tests.csv'):
        print("❌ Fichier example_tests.csv non trouvé")
        return
    
    print("""
🎯 Cette démonstration montre les différences entre :
   • AVANT : Format standard (ancien)
   • APRÈS : Format Jira optimisé + analyse + correction
    """)
    
    # 1. Format original
    show_file_structure('example_tests.csv', 'FICHIER ORIGINAL')
    
    # 2. Enrichissement format standard (ancien)
    print_header("📊 ENRICHISSEMENT FORMAT STANDARD (ANCIEN)")
    tool_standard = TestEnrichmentTool()
    tool_standard.enrich_csv('example_tests.csv', 'demo_standard.csv')
    show_file_structure('demo_standard.csv', 'ENRICHI FORMAT STANDARD')
    
    # 3. Enrichissement format Jira optimisé (nouveau)
    print_header("🚀 ENRICHISSEMENT FORMAT JIRA OPTIMISÉ (NOUVEAU)")
    tool_v2 = TestEnrichmentToolV2()
    tool_v2.enrich_csv_with_separate_labels('example_tests.csv', 'demo_jira.csv')
    show_file_structure('demo_jira.csv', 'ENRICHI FORMAT JIRA')
    
    # 4. Correction automatique (nouveau)
    print_header("🔧 CORRECTION AUTOMATIQUE (NOUVEAU)")
    corrector = TestSetCorrector()
    corrections = corrector.correct_test_file('demo_jira.csv', 'demo_corrected.csv', apply_corrections=True)
    
    print(f"\n📊 CORRECTIONS APPLIQUÉES:")
    print(f"   • Test Sets modifiés: {corrections['test_set_changes']}")
    print(f"   • Labels ajoutés: {corrections['labels_added']}")
    print(f"   • Références 3GPP ajoutées: {corrections['gpp_refs_added']}")
    
    show_file_structure('demo_corrected.csv', 'FICHIER CORRIGÉ FINAL')
    
    # 5. Comparaison détaillée
    print_header("📋 COMPARAISON DÉTAILLÉE")
    
    df_original = pd.read_csv('example_tests.csv')
    df_standard = pd.read_csv('demo_standard.csv')
    df_jira = pd.read_csv('demo_jira.csv')
    df_corrected = pd.read_csv('demo_corrected.csv')
    
    print(f"""
📊 ÉVOLUTION DES DONNÉES:

   ORIGINAL:
   • Tests: {len(df_original)}
   • Colonnes: {len(df_original.columns)}
   • Labels par test: 0
   
   STANDARD (ancien):
   • Tests: {len(df_standard)}
   • Colonnes: {len(df_standard.columns)}
   • Colonnes Labels: 1
   • Labels par test: ~6 (combinés dans 1 colonne)
   
   JIRA OPTIMISÉ (nouveau):
   • Tests: {len(df_jira)}
   • Colonnes: {len(df_jira.columns)}
   • Colonnes Labels: {len([col for col in df_jira.columns if col == 'Labels'])}
   • Labels par test: ~6 (séparés dans {len([col for col in df_jira.columns if col == 'Labels'])} colonnes)
   
   CORRIGÉ FINAL (nouveau):
   • Tests: {len(df_corrected)}
   • Colonnes: {len(df_corrected.columns)}
   • Colonnes Labels: {len([col for col in df_corrected.columns if col == 'Labels' or col.startswith('Labels')])}
   • Labels par test: ~10+ (avec corrections automatiques)
    """)
    
    # 6. Exemple concret
    print_header("🎯 EXEMPLE CONCRET - PREMIER TEST")
    
    print("\n📝 ÉVOLUTION DU PREMIER TEST:")
    
    # Original
    original_test = df_original.iloc[0]
    print(f"\n   ORIGINAL:")
    print(f"   • Summary: {original_test['Summary']}")
    print(f"   • Component: {original_test.get('Component/s', 'Vide')}")
    print(f"   • Labels: Aucun")
    
    # Standard
    standard_test = df_standard.iloc[0]
    print(f"\n   STANDARD (ancien):")
    print(f"   • Summary: {standard_test['Summary']}")
    print(f"   • Component: {standard_test.get('Component/s', 'N/A')}")
    print(f"   • Labels: {standard_test.get('Labels', 'N/A')}")
    print(f"   • Test Set: {standard_test.get('Test Set', 'N/A')}")
    
    # Jira optimisé
    jira_test = df_jira.iloc[0]
    jira_labels = []
    for col in df_jira.columns:
        if col == 'Labels':
            value = df_jira.iloc[0][col]
            if pd.notna(value) and value != '':
                jira_labels.append(str(value))
    
    print(f"\n   JIRA OPTIMISÉ (nouveau):")
    print(f"   • Summary: {jira_test['Summary']}")
    print(f"   • Component: {jira_test.get('Component/s', 'N/A')}")
    print(f"   • Labels séparés: {jira_labels}")
    print(f"   • Test Set: {jira_test.get('Test Set', 'N/A')}")
    
    # Corrigé final
    corrected_test = df_corrected.iloc[0]
    corrected_labels = []
    for col in df_corrected.columns:
        if col == 'Labels' or col.startswith('Labels'):
            value = df_corrected.iloc[0][col]
            if pd.notna(value) and value != '':
                corrected_labels.append(str(value))
    
    print(f"\n   CORRIGÉ FINAL (nouveau):")
    print(f"   • Summary: {corrected_test['Summary']}")
    print(f"   • Component: {corrected_test.get('Component/s', 'N/A')}")
    print(f"   • Labels corrigés: {corrected_labels}")
    print(f"   • Test Set: {corrected_test.get('Test Set', 'N/A')}")
    
    # 7. Avantages
    print_header("🎉 AVANTAGES DES NOUVELLES FONCTIONNALITÉS")
    
    print(f"""
✅ COLONNES LABELS SÉPARÉES:
   • Chaque label dans sa propre colonne
   • Format parfait pour import Jira Xray
   • Pas de parsing nécessaire
   
✅ LABELS TECHNIQUES AJOUTÉS:
   • Standards 3GPP automatiquement appliqués
   • Labels techniques (2G/4G, MAP/Diameter)
   • Références normatives (TS 23.082, etc.)
   
✅ TEST SETS CORRIGÉS:
   • Réaffectation automatique selon expertise Core Network
   • Cohérence avec standards ISTQB
   • Traçabilité améliorée
   
✅ ANALYSE DE QUALITÉ:
   • Détection automatique des incohérences
   • Score de conformité global
   • Métriques de suivi continues
    """)
    
    # Nettoyer les fichiers de démonstration
    for file in ['demo_standard.csv', 'demo_jira.csv', 'demo_corrected.csv']:
        if os.path.exists(file):
            os.remove(file)
    
    print_header("✅ DÉMONSTRATION TERMINÉE")
    print("""
💡 POUR TESTER DANS L'INTERFACE WEB:
   1. python run_app.py
   2. Télécharger example_tests.csv
   3. Choisir "Format Jira Optimisé" (IMPORTANT!)
   4. Cliquer "Analyser Test Sets" après enrichissement
   5. Suivre les 3 étapes d'analyse
   
🎯 VOUS VERREZ ALORS TOUTES CES AMÉLIORATIONS !
    """)

if __name__ == "__main__":
    create_comparison()
