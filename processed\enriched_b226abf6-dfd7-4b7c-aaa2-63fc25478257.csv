Summary;Issue key;Issue id;Issue Type;Status;Project key;Project name;Project type;Project lead;Project description;Project url;Priority;Resolution;Assignee;Reporter;Creator;Created;Updated;Last Viewed;Resolved;Affects Version/s;Fix Version/s;Component/s;Due Date;Votes;Description;Environment;Watchers;Log Work;Original Estimate;Remaining Estimate;Time Spent;Work Ratio;Σ Original Estimate;Σ Remaining Estimate;Σ Time Spent;Security Level;Attachment;Custom field (Automation status);Custom field (Branch);Custom field (Business Gain);Custom field (CDR/MC);Custom field (Category List);Custom field (Change completion date);Custom field (Change start date);Custom field (Change type);Custom field (Cost);Custom field (Cucumber Scenario);Custom field (Cucumber Test Type);Customer Request Type;Custom field (Dataset values);Custom field (Dataset values).1;Custom field (Date MEP);Custom field (Date UAT);Demande SLA 16H;Demande SLA 16H simplified;Custom field (Domain List);Durée de traitement;Durée de traitement simplified;<PERSON><PERSON><PERSON> de <PERSON>;<PERSON><PERSON><PERSON> simplified;<PERSON><PERSON><PERSON> de prise en charge;<PERSON><PERSON><PERSON> de prise en charge simplified;Custom field (Effects);Custom field (Entity List);Custom field (Environment);Custom field (Epic Link);Custom field (External Contributor/s);Custom field (External issue ID);Fermeture apres x jours;Fermeture apres x jours simplified;Custom field (First Backlog Transition);Custom field (First key);Custom field (First-Name);Custom field (Flagged);Custom field (Generic Test Definition);Custom field (Groups);Custom field (Groups).1;Custom field (Impact);Custom field (Impacted Entity);Custom field (Jira Project Type);Custom field (Last-Name);Custom field (Linked major incidents);Custom field (List Entity);Custom field (MVP Macro Budget (K€));Custom field (Mail);Custom field (Manual Test Steps);Custom field (Operational categorization);Custom field (Organizations);Custom field (Original story points);Custom field (Overcoast);Custom field (Parent Key);Custom field (Parent Link);Custom field (Penalties);Custom field (Platform);Custom field (Pre-Conditions association with a Test);Prise en compte;Prise en compte simplified;Custom field (Product categorization);Custom field (QC);Custom field (Qualification Date);Custom field (Rank);Custom field (Ref. Project CARTO);Custom field (Reference Code);Custom field (Request participants);Resolution Time SLA;Resolution Time SLA simplified;Response Time SLA;Response Time SLA simplified;Custom field (Result);Custom field (Review date);Custom field (Revision);Satisfaction score (out of 5);Custom field (Scoring);Sprint;Custom field (Steps Count);Custom field (Structure Index Monitor);Custom field (Support);Custom field (Target end);Custom field (Target start);Custom field (Team);Custom field (Team List);Temps d&#39,attribution;Temps d&#39,attribution simplified;Temps première réponse;Temps première réponse simplified;Custom field (Test Execution Status);Custom field (Test Plans associated with a Test);Custom field (Test Repository Path);Custom field (Test Sets association with a Test);Custom field (Test Sets association with a Test).1;Custom field (Test Type);Custom field (TestRunStatus);Time to close after resolution;Time to close after resolution simplified;Time to first response;Time to first response simplified;Time to resolution;Time to resolution simplified;Custom field (TimeRecup (deprecated));Custom field (User Activity);Custom field (Workaround (deprecated));Comment;Action;Data;Expected Result;Test Set;Labels;Labels;Labels;Labels;Labels;Labels;Labels;Labels;Labels
test File Storage DB;SANTSDM-1896;8939088;Test;To Do;SANTSDM;SANDBOX_TEST_SDM;software;<EMAIL>;;;Lowest;;;<EMAIL>;<EMAIL>;11/Jul/25 6:44 PM;12/Jul/25 3:45 PM;17/Jul/25 9:59 AM;;;;ZTE-SPECIFIC;;0;;;;;;;;;;;;;;;;;;;;;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;SANTSDM-1896;;;;;;;;software;;;;;;[];;;;;;;;;;;;;;;0|j1qd1k:;;;;;;;;;;;;;;0.0;;;;;;;;;;;"{""issueId"":8939088,""testStatuses"":[]}";;;;;Manual;TODO;;;;;;;;;;;;;;General Tests;Functional;System;Manual;Regression;;;;;
SOAP provisionning;SANTSDM-1895;8939087;Test;To Do;SANTSDM;SANDBOX_TEST_SDM;software;<EMAIL>;;;Lowest;;;<EMAIL>;<EMAIL>;11/Jul/25 6:44 PM;15/Jul/25 5:34 PM;15/Jul/25 12:34 PM;;;;IMS-HSS;;0;"SUMMARY : None
PRECONDITIONS : None";;;;;;;;;;;;;;;;;;;;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;SANTSDM-1895;;;;;;;;software;;;;;;"[{""id"":3844657,""index"":1,""fields"":{""Action"":""\""- Verify that user provisionning can be executed through SOAP command\n\"""",""Data"":"""",""Expected Result"":""\""- User provisionning operate with success with SOAP command\n\""""},""attachments"":[],""testVersionId"":1102789}]";;;;;;;;;;;;;;;0|j1qd1c:;;;;;;;;;;;;;;1.0;;;;;;;;;;;"{""issueId"":8939087,""testStatuses"":[]}";;/SDM COMMON TESTCASES/NON REGRESSION Testcases/COMMON Non Regression/Subscriber management;;;Manual;TODO;;;;;;;;;;;- Verify that user provisionning can be executed through SOAP command;;- User provisionning operate with success with SOAP command;IMS Cx/Dx Interface;Functional;System;Manual;Regression;4G;IMS;Diameter;Cx;3GPP_TS_29.229
MML provisionning;SANTSDM-1894;8939086;Test;To Do;SANTSDM;SANDBOX_TEST_SDM;software;<EMAIL>;;;Lowest;;;<EMAIL>;<EMAIL>;11/Jul/25 6:44 PM;15/Jul/25 5:34 PM;17/Jul/25 10:39 AM;;;;IMS-HSS;;0;"SUMMARY : None
PRECONDITIONS : None";;;;;;;;;;;;;;;;;;;;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;SANTSDM-1894;;;;;;;;software;;;;;;"[{""id"":3844656,""index"":1,""fields"":{""Action"":""\""- Verify that user provisionning can be executed through MMLcommand\n\"""",""Data"":"""",""Expected Result"":""\""- User provisionning operate with success with MML command\n\""""},""attachments"":[],""testVersionId"":1102788}]";;;;;;;;;;;;;;;0|j1qd14:;;;;;;;;;;;;;;1.0;;;;;;;;;;;"{""issueId"":8939086,""testStatuses"":[]}";;/SDM COMMON TESTCASES/NON REGRESSION Testcases/COMMON Non Regression/Subscriber management;;;Manual;TODO;;;;;;;;;;;- Verify that user provisionning can be executed through MMLcommand;;- User provisionning operate with success with MML command;IMS Cx/Dx Interface;Functional;System;Manual;Regression;4G;IMS;Diameter;Cx;3GPP_TS_29.229
