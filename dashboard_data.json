{"overview": {"total_tests": 27, "total_test_sets": 16, "total_components": 6, "tests_with_issues": 25, "overall_health": "NEEDS_ATTENTION", "last_analysis": "2025-07-16 12:19:54"}, "quality_metrics": {"test_set_alignment": {"score": 85.2, "status": "good", "details": "4 tests mal classés"}, "label_completeness": {"score": 22.2, "status": "poor", "details": "21 tests avec labels manquants"}, "gpp_coverage": {"score": 51.9, "status": "poor", "details": "13 tests sans référence 3GPP"}, "label_consistency": {"score": 40.7, "status": "poor", "details": "16 tests avec incohérences"}}, "test_set_distribution": {"total_test_sets": 16, "largest_test_set": {"name": "EPC HSS General Services", "count": 4}, "smallest_test_set": {"name": "Resiliency and Redundancy", "count": 1}, "distribution": {"EPC HSS General Services": 4, "Security General Tests": 4, "HLR Call Forwarding Services": 3, "Backup and Restore Operations": 3, "OAM and Provisioning": 2, "HLR CAMEL Services": 1, "EPC HSS Location Management": 1, "HLR Call Barring Services": 1, "IMS T-ADS Services": 1, "IMS General Services": 1}, "balance_score": 66.3}, "label_analysis": {"total_labels": 27, "unique_labels": 2, "most_used": [["Functional", 14], ["NonFunctional", 13]], "label_types": {"functional": 27, "technical": 0, "domain": 0}, "coverage_analysis": {"has_functional_classification": 100.0, "has_technical_labels": 0.0, "has_domain_labels": 0.0}}, "compliance_score": {"overall_score": 51.7, "level": "NEEDS_IMPROVEMENT", "color": "red", "breakdown": {"test_set_alignment": 85.2, "label_completeness": 22.2, "gpp_coverage": 51.9, "label_consistency": 40.7}, "weights": {"test_set_alignment": 0.3, "label_completeness": 0.25, "gpp_coverage": 0.2, "label_consistency": 0.25}}, "recommendations": [{"priority": "HIGH", "title": "Réorganiser les Test Sets", "description": "4 tests mal classés", "action": "Utiliser le correcteur automatique", "impact": "Améliore la traçabilité et la maintenance"}, {"priority": "MEDIUM", "title": "Compléter les Labels", "description": "21 tests avec labels manquants", "action": "Ajouter les labels techniques obligatoires", "impact": "Améliore la recherche et le reporting"}, {"priority": "LOW", "title": "Ajouter Références 3GPP", "description": "13 tests sans référence", "action": "Mapper avec les spécifications 3GPP", "impact": "Améliore la traçabilité normative"}], "trends": {"test_growth": {"current_month": 27, "trend": "stable", "projection": 32}, "quality_evolution": {"current_score": 85.2, "previous_score": 82.1, "trend": "improving"}, "coverage_gaps": ["Tests 5G manquants", "Couverture IMS incomplète", "Tests de performance limités"]}}