#!/usr/bin/env python3
"""
Test que tout fonctionne avec des virgules uniquement
"""

import pandas as pd
import os
from json_steps_processor import J<PERSON>NStepsProcessor
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def print_header(title):
    print("\n" + "="*70)
    print(f"  {title}")
    print("="*70)

def test_workflow_complet_virgules():
    """
    Test du workflow complet avec virgules uniquement
    """
    print_header("🔧 TEST WORKFLOW COMPLET AVEC VIRGULES UNIQUEMENT")
    
    # Créer un fichier avec des données JSON comme votre exemple
    data = {
        'Summary': ['test File Storage DB', 'SOAP provisionning', 'MML provisionning'],
        'Issue key': ['SANTSDM-1896', 'SANTSDM-1895', 'SANTSDM-1894'],
        'Priority': ['Lowest', 'Lowest', 'Lowest'],
        'Component/s': ['ZTE-SPECIFIC', '', ''],
        'Description': ['', 'SUMMARY : None\nPRECONDITIONS : None', 'SUMMARY : None\nPRECONDITIONS : None'],
        'Custom field (Manual Test Steps)': [
            '[]',  # JSON vide
            '[{"id":3844657,"index":1,"fields":{"Action":"- Verify that user provisionning can be executed through SOAP command","Data":"","Expected Result":"- User provisionning operate with success with SOAP command"},"attachments":[],"testVersionId":1102789}]',
            '[{"id":3844656,"index":1,"fields":{"Action":"- Verify that user provisionning can be executed through MMLcommand","Data":"","Expected Result":"- User provisionning operate with success with MML command"},"attachments":[],"testVersionId":1102788}]'
        ],
        'Custom field (Test Repository Path)': ['', '/SDM COMMON TESTCASES/NON REGRESSION Testcases/COMMON Non Regression/Subscriber management', '/SDM COMMON TESTCASES/NON REGRESSION Testcases/COMMON Non Regression/Subscriber management'],
        'Custom field (Test Type)': ['Manual', 'Manual', 'Manual']
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_workflow_virgules.csv', index=False)
    
    print(f"📁 Fichier créé avec {len(data)} colonnes et {len(df)} tests")
    
    # Vérifier le séparateur du fichier d'entrée
    with open('test_workflow_virgules.csv', 'r', encoding='utf-8') as f:
        first_line = f.readline()
    
    virgules_entree = first_line.count(',')
    points_virgules_entree = first_line.count(';')
    print(f"📊 Fichier d'entrée : {virgules_entree} virgules, {points_virgules_entree} points-virgules")
    
    # Étape 1 : Extraction JSON
    print(f"\n🔧 ÉTAPE 1 : EXTRACTION JSON")
    processor = JSONStepsProcessor()
    stats = processor.process_csv_file('test_workflow_virgules.csv', 'test_workflow_extracted.csv')
    
    print(f"📊 Extraction : {stats['processed']} tests → {stats['extracted']} étapes")
    
    # Vérifier le séparateur du fichier extrait
    if os.path.exists('test_workflow_extracted.csv'):
        with open('test_workflow_extracted.csv', 'r', encoding='utf-8') as f:
            extracted_first_line = f.readline()
        
        virgules_extrait = extracted_first_line.count(',')
        points_virgules_extrait = extracted_first_line.count(';')
        print(f"📊 Fichier extrait : {virgules_extrait} virgules, {points_virgules_extrait} points-virgules")
        
        if virgules_extrait > points_virgules_extrait:
            print("✅ Fichier extrait utilise des virgules (correct)")
            separateur_extrait = ','
        else:
            print("❌ Fichier extrait utilise des points-virgules (problème)")
            separateur_extrait = ';'
    else:
        print("❌ Fichier extrait non généré")
        return False
    
    # Étape 2 : Enrichissement
    print(f"\n🎯 ÉTAPE 2 : ENRICHISSEMENT")
    try:
        tool = TestEnrichmentToolEnhanced()
        tool.enrich_csv_enhanced('test_workflow_extracted.csv', 'test_workflow_enriched.csv', 'separate')
        
        print("✅ Enrichissement réussi")
        
        # Vérifier le fichier enrichi
        if os.path.exists('test_workflow_enriched.csv'):
            with open('test_workflow_enriched.csv', 'r', encoding='utf-8') as f:
                enriched_content = f.read()
                enriched_lines = enriched_content.strip().split('\n')
            
            if enriched_lines:
                enriched_first_line = enriched_lines[0]
                virgules_enrichi = enriched_first_line.count(',')
                points_virgules_enrichi = enriched_first_line.count(';')
                
                print(f"📊 Fichier enrichi : {len(enriched_lines)} lignes")
                print(f"📊 Séparateurs : {virgules_enrichi} virgules, {points_virgules_enrichi} points-virgules")
                
                if virgules_enrichi > points_virgules_enrichi:
                    print("✅ Fichier enrichi utilise des virgules (correct)")
                    separateur_enrichi = ','
                else:
                    print("❌ Fichier enrichi utilise des points-virgules (problème)")
                    separateur_enrichi = ';'
                
                # Compter les colonnes
                colonnes_enrichi = virgules_enrichi + 1 if separateur_enrichi == ',' else points_virgules_enrichi + 1
                print(f"📊 Colonnes finales : {colonnes_enrichi}")
                
                # Compter les Labels
                labels_count = enriched_first_line.count('Labels')
                print(f"📊 Colonnes Labels : {labels_count}")
                
                # Vérifier les colonnes essentielles
                essential_columns = ['Summary', 'Action', 'Data', 'Expected Result', 'Component/s', 'Test Set']
                missing_essential = []
                for col in essential_columns:
                    if col not in enriched_first_line:
                        missing_essential.append(col)
                
                print(f"📊 Colonnes essentielles manquantes : {len(missing_essential)}")
                if missing_essential:
                    for col in missing_essential:
                        print(f"     - {col}")
                
                # Afficher un échantillon
                print(f"\n📋 Échantillon du fichier enrichi :")
                header_parts = enriched_first_line.split(separateur_enrichi)
                for i, col in enumerate(header_parts[:10]):
                    print(f"   {i+1}. {col}")
                if len(header_parts) > 10:
                    print(f"   ... et {len(header_parts) - 10} autres colonnes")
                
                success = (separateur_extrait == ',' and separateur_enrichi == ',' and 
                          labels_count >= 8 and len(missing_essential) == 0)
                
                print(f"\n🎯 RÉSULTAT FINAL :")
                print(f"✅ Extraction avec virgules : {'OUI' if separateur_extrait == ',' else 'NON'}")
                print(f"✅ Enrichissement avec virgules : {'OUI' if separateur_enrichi == ',' else 'NON'}")
                print(f"✅ Labels en colonnes séparées : {'OUI' if labels_count >= 8 else 'NON'}")
                print(f"✅ Colonnes essentielles présentes : {'OUI' if len(missing_essential) == 0 else 'NON'}")
                
                if success:
                    print(f"\n🎉 WORKFLOW COMPLET AVEC VIRGULES FONCTIONNE !")
                    print(f"📊 Résumé :")
                    print(f"   Fichier d'entrée : {len(data)} colonnes")
                    print(f"   Fichier extrait : {virgules_extrait + 1} colonnes")
                    print(f"   Fichier enrichi : {colonnes_enrichi} colonnes")
                    print(f"   Tests : {stats['processed']} → {len(enriched_lines)-1} lignes finales")
                    print(f"   Format : Virgules (,) partout")
                else:
                    print(f"\n⚠️  Le workflow fonctionne mais avec des ajustements nécessaires")
            else:
                print("❌ Fichier enrichi vide")
                success = False
            
            # Nettoyer
            os.remove('test_workflow_enriched.csv')
        else:
            print("❌ Fichier enrichi non généré")
            success = False
    
    except Exception as e:
        print(f"❌ Erreur lors de l'enrichissement : {e}")
        success = False
    
    # Nettoyer
    os.remove('test_workflow_virgules.csv')
    if os.path.exists('test_workflow_extracted.csv'):
        os.remove('test_workflow_extracted.csv')
    
    return success

def test_fichier_complexe_virgules():
    """
    Test avec un fichier complexe comme le vôtre mais avec virgules
    """
    print_header("📋 TEST FICHIER COMPLEXE AVEC VIRGULES")
    
    # Créer un fichier avec beaucoup de colonnes
    data = {}
    
    # Colonnes de base
    base_columns = [
        'Summary', 'Issue key', 'Issue id', 'Issue Type', 'Status', 'Project key',
        'Project name', 'Priority', 'Component/s', 'Description', 'Reporter',
        'Created', 'Updated', 'Custom field (Manual Test Steps)',
        'Custom field (Test Repository Path)', 'Custom field (Test Type)'
    ]
    
    # Ajouter beaucoup de colonnes custom fields
    for i in range(20):
        base_columns.append(f'Custom field ({i})')
    
    # Ajouter les colonnes Action, Data, Expected Result
    base_columns.extend(['Action', 'Data', 'Expected Result'])
    
    # Créer les données
    for col in base_columns:
        if col == 'Summary':
            data[col] = ['Test Complex 1', 'Test Complex 2']
        elif col == 'Component/s':
            data[col] = ['HLR', 'HSS']
        elif col == 'Action':
            data[col] = ['Action complexe 1', 'Action complexe 2']
        elif col == 'Data':
            data[col] = ['Data complexe 1', 'Data complexe 2']
        elif col == 'Expected Result':
            data[col] = ['Result complexe 1', 'Result complexe 2']
        else:
            data[col] = ['', '']
    
    df = pd.DataFrame(data)
    df.to_csv('test_complexe_virgules.csv', index=False)
    
    print(f"📁 Fichier complexe créé avec {len(data)} colonnes")
    
    # Test d'enrichissement direct
    try:
        tool = TestEnrichmentToolEnhanced()
        tool.enrich_csv_enhanced('test_complexe_virgules.csv', 'test_complexe_enriched.csv', 'separate')
        
        print("✅ Enrichissement du fichier complexe réussi")
        
        # Vérifier le résultat
        if os.path.exists('test_complexe_enriched.csv'):
            with open('test_complexe_enriched.csv', 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
            
            if lines:
                header = lines[0]
                virgules = header.count(',')
                points_virgules = header.count(';')
                
                print(f"📊 Fichier enrichi complexe :")
                print(f"   Lignes : {len(lines)}")
                print(f"   Virgules : {virgules}, Points-virgules : {points_virgules}")
                print(f"   Colonnes : {virgules + 1 if virgules > points_virgules else points_virgules + 1}")
                
                labels_count = header.count('Labels')
                print(f"   Colonnes Labels : {labels_count}")
                
                success = virgules > points_virgules and labels_count >= 8
                print(f"✅ Fichier complexe avec virgules : {'OUI' if success else 'NON'}")
            else:
                success = False
            
            # Nettoyer
            os.remove('test_complexe_enriched.csv')
        else:
            success = False
    
    except Exception as e:
        print(f"❌ Erreur enrichissement complexe : {e}")
        success = False
    
    # Nettoyer
    os.remove('test_complexe_virgules.csv')
    
    return success

def main():
    """
    Test complet avec virgules uniquement
    """
    print("🧪 TEST COMPLET AVEC VIRGULES UNIQUEMENT")
    
    # Test 1 : Workflow complet
    test1 = test_workflow_complet_virgules()
    
    # Test 2 : Fichier complexe
    test2 = test_fichier_complexe_virgules()
    
    print_header("📊 RÉSULTATS FINAUX")
    
    print(f"✅ Workflow complet avec virgules : {'OK' if test1 else 'ÉCHEC'}")
    print(f"✅ Fichier complexe avec virgules : {'OK' if test2 else 'ÉCHEC'}")
    
    if test1 and test2:
        print(f"\n🎉 TOUS LES TESTS AVEC VIRGULES RÉUSSIS !")
        print(f"✅ Extraction JSON génère des fichiers avec virgules")
        print(f"✅ Enrichissement lit et écrit avec virgules")
        print(f"✅ Workflow complet compatible")
        print(f"✅ Fichiers complexes supportés")
        print(f"🎯 Votre problème de séparateur est résolu !")
    else:
        print(f"\n⚠️  Des ajustements sont encore nécessaires")

if __name__ == "__main__":
    main()
