#!/usr/bin/env python3
"""
Générateur de fichier CSV pour import des Test Sets
Format : Summary;Status;Labels;Labels_1;Labels_2;Labels_3;Labels_4;Labels_5;Labels_6;Labels_7;Labels_8;Labels_9;;Description;Comment
"""

import pandas as pd
import os
from core_network_rules_complete import CoreNetworkRulesComplete

class TestSetCSVGenerator:
    """
    Générateur de fichier CSV pour import des Test Sets avec descriptions et commentaires
    """
    
    def __init__(self):
        self.rules = CoreNetworkRulesComplete()
        
        # Descriptions en anglais pour chaque Test Set
        self.test_set_descriptions = {
            # HLR Domain
            'HLR Call Forwarding Services': 'Comprehensive testing of call forwarding services including CFU, CFB, CFNR according to 3GPP TS 23.082 specifications',
            'HLR Call Barring Services': 'Validation of call barring services and restrictions according to 3GPP TS 23.088 specifications',
            'HLR CAMEL Services': 'Testing of CAMEL intelligent network services and procedures according to 3GPP TS 23.078',
            'HLR Location Management': 'Location update and management procedures testing according to 3GPP TS 23.012',
            'HLR Authentication Services': 'Authentication procedures and security validation according to 3GPP TS 29.002',
            
            # EPC-HSS Domain
            'EPC HSS Authentication': 'EPC-HSS authentication procedures via S6a interface according to 3GPP TS 29.272',
            'EPC HSS Location Management': 'Location update and management in EPC-HSS environment according to 3GPP TS 29.272',
            'EPC HSS Subscription Management': 'Subscription data management and provisioning according to 3GPP TS 29.272',
            'EPC HSS Notification Services': 'Notification and alert services testing according to 3GPP TS 29.272',
            
            # IMS Domain
            'IMS VoLTE Services': 'Voice over LTE services and SRVCC procedures according to 3GPP TS 23.216',
            'IMS Cx/Dx Interface': 'Cx/Dx interface testing between IMS and HSS according to 3GPP TS 29.229',
            'IMS Sh Interface': 'Sh interface testing for application server communication according to 3GPP TS 29.329',
            'IMS T-ADS Services': 'Telephony Application Server services testing in IMS environment',
            
            # Performance Domain
            'Performance Load Testing': 'Load testing and performance validation according to 3GPP TS 32.401',
            'Performance Diameter Routing': 'Diameter routing performance and optimization testing according to 3GPP TS 29.212',
            'Performance Scalability': 'System scalability and capacity testing under various load conditions',
            
            # Security Domain
            'Security Vulnerability Assessment': 'Security vulnerability scanning and assessment according to 3GPP TS 33.102',
            'Security CIS Compliance': 'Center for Internet Security compliance testing and validation',
            'Security RBAC Management': 'Role-Based Access Control testing and management validation',
            'Security Authentication': 'Security authentication procedures and validation according to 3GPP TS 33.401',
            
            # OAM Domain
            'OAM SOAP Interface': 'SOAP interface testing for operations and maintenance according to 3GPP TS 32.101',
            'OAM Provisioning': 'Provisioning and configuration management testing according to 3GPP TS 32.101',
            'OAM Monitoring': 'System monitoring and supervision testing according to 3GPP TS 32.101',
            
            # Backup Domain
            'Backup and Restore Operations': 'Backup and restore procedures testing according to 3GPP TS 32.101',
            'Backup Scheduling': 'Automated backup scheduling and management testing',
            
            # Resiliency Domain
            'Resiliency and Redundancy': 'System resiliency and redundancy testing according to 3GPP TS 23.007',
            'High Availability': 'High availability and failover testing procedures'
        }
        
        # Commentaires en français vulgarisés
        self.test_set_comments = {
            # HLR Domain
            'HLR Call Forwarding Services': 'Tests des services de renvoi d\'appel (CFU, CFB, CFNR) - Vérification que les appels sont bien renvoyés selon les paramètres configurés',
            'HLR Call Barring Services': 'Tests des services d\'interdiction d\'appel - Vérification que certains appels sont bloqués selon les restrictions configurées',
            'HLR CAMEL Services': 'Tests des services intelligents CAMEL - Vérification des services à valeur ajoutée comme la facturation prépayée',
            'HLR Location Management': 'Tests de gestion de localisation HLR - Vérification que le système sait où se trouvent les abonnés',
            'HLR Authentication Services': 'Tests d\'authentification HLR - Vérification que seuls les abonnés autorisés peuvent accéder au réseau',
            
            # EPC-HSS Domain
            'EPC HSS Authentication': 'Tests d\'authentification 4G via interface S6a - Vérification de l\'identité des abonnés LTE',
            'EPC HSS Location Management': 'Tests de gestion de localisation 4G - Suivi des déplacements des abonnés dans le réseau LTE',
            'EPC HSS Subscription Management': 'Tests de gestion des abonnements 4G - Gestion des profils et services des abonnés LTE',
            'EPC HSS Notification Services': 'Tests des notifications 4G - Vérification des alertes et notifications système',
            
            # IMS Domain
            'IMS VoLTE Services': 'Tests des services voix sur 4G (VoLTE) - Vérification de la qualité des appels voix sur réseau LTE',
            'IMS Cx/Dx Interface': 'Tests de l\'interface Cx/Dx IMS - Communication entre serveurs d\'application et base de données abonnés',
            'IMS Sh Interface': 'Tests de l\'interface Sh IMS - Accès aux données abonnés par les serveurs d\'application',
            'IMS T-ADS Services': 'Tests des services téléphoniques IMS - Services voix avancés dans l\'environnement IMS',
            
            # Performance Domain
            'Performance Load Testing': 'Tests de charge système - Vérification que le système supporte le nombre d\'utilisateurs prévu',
            'Performance Diameter Routing': 'Tests de performance du routage Diameter - Optimisation des temps de réponse des messages',
            'Performance Scalability': 'Tests de montée en charge - Vérification que le système peut grandir selon les besoins',
            
            # Security Domain
            'Security Vulnerability Assessment': 'Tests de sécurité et vulnérabilités - Recherche de failles de sécurité dans le système',
            'Security CIS Compliance': 'Tests de conformité sécurité CIS - Vérification des bonnes pratiques de sécurité informatique',
            'Security RBAC Management': 'Tests de gestion des droits d\'accès - Vérification que chaque utilisateur a les bons droits',
            'Security Authentication': 'Tests de sécurité d\'authentification - Vérification des procédures de connexion sécurisée',
            
            # OAM Domain
            'OAM SOAP Interface': 'Tests de l\'interface SOAP d\'administration - Vérification des outils de gestion à distance',
            'OAM Provisioning': 'Tests de provisioning - Vérification de la configuration automatique des équipements',
            'OAM Monitoring': 'Tests de supervision système - Vérification des outils de surveillance et d\'alerte',
            
            # Backup Domain
            'Backup and Restore Operations': 'Tests de sauvegarde et restauration - Vérification que les données peuvent être récupérées',
            'Backup Scheduling': 'Tests de planification des sauvegardes - Vérification des sauvegardes automatiques programmées',
            
            # Resiliency Domain
            'Resiliency and Redundancy': 'Tests de résilience système - Vérification que le système continue à fonctionner en cas de panne',
            'High Availability': 'Tests de haute disponibilité - Vérification du temps de fonctionnement continu du système'
        }
    
    def generate_test_sets_csv(self, output_file: str):
        """
        Génère le fichier CSV pour import des Test Sets
        """
        print("Génération du fichier CSV pour import des Test Sets...")
        
        # Récupérer tous les Test Sets
        all_test_sets = self.rules.get_all_test_sets()
        
        # Préparer les données
        csv_data = []
        
        for test_set_name, config in all_test_sets.items():
            # Récupérer les labels obligatoires
            mandatory_labels = config.get('mandatory_labels', [])
            
            # Préparer les colonnes Labels (jusqu'à 9 colonnes)
            labels_data = {}
            labels_data['Labels'] = mandatory_labels[0] if len(mandatory_labels) > 0 else ''
            
            for i in range(1, 10):  # Labels_1 à Labels_9
                if i < len(mandatory_labels):
                    labels_data[f'Labels_{i}'] = mandatory_labels[i]
                else:
                    labels_data[f'Labels_{i}'] = ''
            
            # Créer l'entrée
            entry = {
                'Summary': test_set_name,
                'Status': 'Draft',  # Statut par défaut
                **labels_data,
                '': '',  # Colonne vide selon le format
                'Description': self.test_set_descriptions.get(test_set_name, f'Testing procedures for {test_set_name}'),
                'Comment': self.test_set_comments.get(test_set_name, f'Tests relatifs à {test_set_name}')
            }
            
            csv_data.append(entry)
        
        # Créer le DataFrame
        df = pd.DataFrame(csv_data)
        
        # Réorganiser les colonnes selon le format demandé
        columns_order = [
            'Summary', 'Status', 'Labels', 'Labels_1', 'Labels_2', 'Labels_3', 
            'Labels_4', 'Labels_5', 'Labels_6', 'Labels_7', 'Labels_8', 'Labels_9',
            '', 'Description', 'Comment'
        ]
        
        df = df[columns_order]
        
        # Sauvegarder avec séparateur point-virgule
        df.to_csv(output_file, sep=';', index=False, encoding='utf-8-sig')
        
        print(f"Fichier CSV généré : {output_file}")
        print(f"Nombre de Test Sets : {len(csv_data)}")
        
        # Afficher un aperçu
        print("\nAperçu des premiers Test Sets :")
        for i, entry in enumerate(csv_data[:3]):
            print(f"\n{i+1}. {entry['Summary']}")
            print(f"   Labels: {entry['Labels']}, {entry['Labels_1']}, {entry['Labels_2']}...")
            print(f"   Description: {entry['Description'][:80]}...")
            print(f"   Comment: {entry['Comment'][:80]}...")
        
        return output_file
    
    def get_test_sets_statistics(self):
        """
        Retourne des statistiques sur les Test Sets
        """
        all_test_sets = self.rules.get_all_test_sets()
        
        stats = {
            'total_test_sets': len(all_test_sets),
            'domains': len(self.rules.test_sets_complete),
            'with_descriptions': len([ts for ts in all_test_sets.keys() if ts in self.test_set_descriptions]),
            'with_comments': len([ts for ts in all_test_sets.keys() if ts in self.test_set_comments])
        }
        
        return stats

def main():
    """
    Fonction principale pour tester le générateur
    """
    print("=== GÉNÉRATEUR DE FICHIER CSV POUR TEST SETS ===")
    
    generator = TestSetCSVGenerator()
    
    # Générer le fichier CSV
    output_file = "test_sets_import.csv"
    generator.generate_test_sets_csv(output_file)
    
    # Afficher les statistiques
    stats = generator.get_test_sets_statistics()
    print(f"\n📊 STATISTIQUES :")
    print(f"   Total Test Sets     : {stats['total_test_sets']}")
    print(f"   Domaines couverts   : {stats['domains']}")
    print(f"   Avec descriptions   : {stats['with_descriptions']}")
    print(f"   Avec commentaires   : {stats['with_comments']}")
    
    print(f"\n✅ Fichier prêt pour import : {output_file}")
    print(f"📋 Format : Summary;Status;Labels;Labels_1;...;Labels_9;;Description;Comment")

if __name__ == "__main__":
    main()
