#!/usr/bin/env python3
"""
Test des corrections finales : guillemets, colonnes complètes, labels séparés
"""

import pandas as pd
import os
from json_steps_processor import JSONStepsProcessor
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def print_header(title):
    print("\n" + "="*70)
    print(f"  {title}")
    print("="*70)

def test_suppression_guillemets_dans_champs():
    """
    Test que les guillemets sont supprimés des champs Action, Data, Expected Result
    """
    print_header("🔧 TEST SUPPRESSION GUILLEMETS DANS CHAMPS")
    
    # Créer un fichier avec des guillemets dans les champs
    data = {
        'Summary': ['Test Guillemets'],
        'Description': ['Test avec guillemets'],
        'Component/s': [''],
        'Priority': ['High'],
        'Issue key': ['TEST-001'],
        'Custom field (Manual Test Steps)': [
            '[{"id":1,"fields":{"Action":"\\"Action avec guillemets\\"","Data":"\\"Data avec guillemets\\"","Expected Result":"\\"Result avec guillemets\\""}}]'
        ]
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_guillemets_champs.csv', index=False)
    
    print("📁 Fichier créé avec guillemets dans les champs JSON")
    
    # Extraction
    processor = JSONStepsProcessor()
    stats = processor.process_csv_file('test_guillemets_champs.csv', 'test_guillemets_extracted.csv')
    
    print(f"📊 Extraction : {stats['processed']} tests → {stats['extracted']} étapes")
    
    # Vérifier le contenu extrait
    if os.path.exists('test_guillemets_extracted.csv'):
        with open('test_guillemets_extracted.csv', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📁 Fichier extrait : {len(lines)} lignes")
        
        if len(lines) > 1:
            # Analyser la ligne de données
            data_line = lines[1].strip()
            parts = data_line.split(';')
            
            # Les 3 dernières colonnes sont Action, Data, Expected Result
            action = parts[-3] if len(parts) >= 3 else ''
            data = parts[-2] if len(parts) >= 2 else ''
            expected = parts[-1] if len(parts) >= 1 else ''
            
            print(f"📋 Champs extraits :")
            print(f"   Action : '{action}'")
            print(f"   Data : '{data}'")
            print(f"   Expected Result : '{expected}'")
            
            # Vérifier qu'il n'y a pas de guillemets indésirables
            has_quotes = '"' in action or '"' in data or '"' in expected
            print(f"\n✅ Guillemets supprimés : {'NON' if has_quotes else 'OUI'}")
            
            success = not has_quotes
        else:
            print("❌ Pas de ligne de données")
            success = False
        
        # Nettoyer
        os.remove('test_guillemets_extracted.csv')
    else:
        print("❌ Fichier extrait non généré")
        success = False
    
    # Nettoyer
    os.remove('test_guillemets_champs.csv')
    
    return success

def test_conservation_toutes_colonnes():
    """
    Test que toutes les colonnes originales sont conservées
    """
    print_header("📋 TEST CONSERVATION TOUTES COLONNES")
    
    # Créer un fichier avec beaucoup de colonnes
    data = {
        'Summary': ['Test Colonnes'],
        'Description': ['Test conservation'],
        'Component/s': ['HLR'],
        'Priority': ['High'],
        'Issue key': ['TEST-002'],
        'Assignee': ['John Doe'],
        'Reporter': ['Jane Smith'],
        'Labels': ['existing-label'],
        'Status': ['Open'],
        'Resolution': [''],
        'Custom field (Manual Test Steps)': [
            '[{"id":1,"fields":{"Action":"Test action","Data":"Test data","Expected Result":"Test result"}}]'
        ]
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_toutes_colonnes.csv', index=False)
    
    print(f"📁 Fichier créé avec {len(data)} colonnes originales")
    
    # Extraction
    processor = JSONStepsProcessor()
    stats = processor.process_csv_file('test_toutes_colonnes.csv', 'test_colonnes_extracted.csv')
    
    print(f"📊 Extraction : {stats['processed']} tests → {stats['extracted']} étapes")
    
    # Vérifier les colonnes
    if os.path.exists('test_colonnes_extracted.csv'):
        with open('test_colonnes_extracted.csv', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if lines:
            header = lines[0].strip()
            columns = header.split(';')
            
            print(f"📋 Colonnes dans le fichier extrait : {len(columns)}")
            print(f"   Colonnes originales : {len(data)}")
            print(f"   Colonnes ajoutées : 3 (Action, Data, Expected Result)")
            print(f"   Total attendu : {len(data) + 3}")
            
            # Vérifier que toutes les colonnes originales sont présentes
            original_columns = list(data.keys())
            missing_columns = []
            for col in original_columns:
                if col not in header:
                    missing_columns.append(col)
            
            print(f"\n📊 Vérification des colonnes :")
            print(f"   Colonnes manquantes : {len(missing_columns)}")
            if missing_columns:
                for col in missing_columns:
                    print(f"     - {col}")
            
            # Vérifier les nouvelles colonnes
            new_columns = ['Action', 'Data', 'Expected Result']
            missing_new = []
            for col in new_columns:
                if col not in header:
                    missing_new.append(col)
            
            print(f"   Nouvelles colonnes manquantes : {len(missing_new)}")
            if missing_new:
                for col in missing_new:
                    print(f"     - {col}")
            
            success = len(missing_columns) == 0 and len(missing_new) == 0 and len(columns) == len(data) + 3
            print(f"\n✅ Toutes colonnes conservées : {'OUI' if success else 'NON'}")
        else:
            print("❌ Fichier vide")
            success = False
        
        # Nettoyer
        os.remove('test_colonnes_extracted.csv')
    else:
        print("❌ Fichier extrait non généré")
        success = False
    
    # Nettoyer
    os.remove('test_toutes_colonnes.csv')
    
    return success

def test_enrichissement_labels_separes():
    """
    Test que l'enrichissement après extraction JSON crée des colonnes Labels séparées
    """
    print_header("🎯 TEST ENRICHISSEMENT LABELS SÉPARÉS")
    
    # Créer un fichier avec des tests techniques
    data = {
        'Summary': ['CFU Activation', 'S6a ULR Test'],
        'Description': ['Test CFU activation', 'Test S6a ULR'],
        'Component/s': ['', ''],
        'Priority': ['High', 'Medium'],
        'Custom field (Manual Test Steps)': [
            '[{"id":1,"fields":{"Action":"Activate CFU service","Data":"MSISDN=123456789","Expected Result":"CFU activated successfully"}}]',
            '[{"id":2,"fields":{"Action":"Send ULR request","Data":"IMSI=123456789012345","Expected Result":"ULA response received"}}]'
        ]
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_labels_separes.csv', index=False)
    
    print("📁 Fichier créé avec tests techniques")
    
    # Étape 1 : Extraction JSON
    processor = JSONStepsProcessor()
    stats = processor.process_csv_file('test_labels_separes.csv', 'test_labels_extracted.csv')
    
    print(f"🔧 Extraction : {stats['processed']} tests → {stats['extracted']} étapes")
    
    # Étape 2 : Enrichissement avec format 'separate'
    try:
        tool = TestEnrichmentToolEnhanced()
        tool.enrich_csv_enhanced('test_labels_extracted.csv', 'test_labels_enriched.csv', 'separate')
        
        print("🎯 Enrichissement réussi")
        
        # Vérifier le fichier enrichi
        if os.path.exists('test_labels_enriched.csv'):
            with open('test_labels_enriched.csv', 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if lines:
                header = lines[0].strip()
                columns = header.split(';')
                
                print(f"📋 Fichier enrichi : {len(lines)} lignes, {len(columns)} colonnes")
                
                # Compter les colonnes Labels
                labels_columns = [col for col in columns if col.strip() == 'Labels']
                labels_count = len(labels_columns)
                
                print(f"📊 Colonnes Labels identiques : {labels_count}")
                
                # Vérifier les colonnes essentielles
                essential_columns = ['Summary', 'Action', 'Data', 'Expected Result', 'Component/s', 'Test Set']
                missing_essential = []
                for col in essential_columns:
                    if col not in header:
                        missing_essential.append(col)
                
                print(f"📊 Colonnes essentielles manquantes : {len(missing_essential)}")
                if missing_essential:
                    for col in missing_essential:
                        print(f"     - {col}")
                
                # Vérifier le contenu d'une ligne
                if len(lines) > 1:
                    data_line = lines[1].strip()
                    parts = data_line.split(';')
                    
                    print(f"\n📋 Exemple de ligne enrichie :")
                    for i, col in enumerate(columns[:10]):  # Afficher les 10 premières colonnes
                        if i < len(parts):
                            print(f"   {col} : {parts[i]}")
                
                success = labels_count >= 8 and len(missing_essential) == 0
                print(f"\n✅ Labels en colonnes séparées : {'OUI' if labels_count >= 8 else 'NON'}")
                print(f"✅ Colonnes essentielles présentes : {'OUI' if len(missing_essential) == 0 else 'NON'}")
            else:
                print("❌ Fichier enrichi vide")
                success = False
            
            # Nettoyer
            os.remove('test_labels_enriched.csv')
        else:
            print("❌ Fichier enrichi non généré")
            success = False
    
    except Exception as e:
        print(f"❌ Erreur lors de l'enrichissement : {e}")
        success = False
    
    # Nettoyer
    os.remove('test_labels_separes.csv')
    if os.path.exists('test_labels_extracted.csv'):
        os.remove('test_labels_extracted.csv')
    
    return success

def main():
    """
    Test complet des corrections finales
    """
    print("🧪 TEST DES CORRECTIONS FINALES")
    
    # Test 1 : Suppression guillemets dans champs
    test1 = test_suppression_guillemets_dans_champs()
    
    # Test 2 : Conservation toutes colonnes
    test2 = test_conservation_toutes_colonnes()
    
    # Test 3 : Enrichissement labels séparés
    test3 = test_enrichissement_labels_separes()
    
    print_header("📊 RÉSULTATS DES CORRECTIONS FINALES")
    
    print(f"✅ Suppression guillemets dans champs : {'OK' if test1 else 'ÉCHEC'}")
    print(f"✅ Conservation toutes colonnes : {'OK' if test2 else 'ÉCHEC'}")
    print(f"✅ Enrichissement labels séparés : {'OK' if test3 else 'ÉCHEC'}")
    
    if test1 and test2 and test3:
        print(f"\n🎉 TOUTES LES CORRECTIONS FINALES FONCTIONNENT !")
        print(f"✅ Champs Action/Data/Expected Result sans guillemets")
        print(f"✅ Toutes les colonnes originales conservées")
        print(f"✅ Enrichissement avec colonnes Labels séparées")
        print(f"✅ Workflow complet optimisé")
        print(f"🎯 L'application est parfaitement configurée")
    else:
        print(f"\n⚠️  CERTAINES CORRECTIONS NÉCESSITENT DES AJUSTEMENTS")

if __name__ == "__main__":
    main()
