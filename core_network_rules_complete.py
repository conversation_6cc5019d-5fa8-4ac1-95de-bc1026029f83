#!/usr/bin/env python3
"""
R<PERSON>gles complètes de labellisation et Test Sets pour Core Network
Basé sur l'analyse de cohérence et les standards 3GPP
"""

import re

class CoreNetworkRulesComplete:
    """
    Système complet de règles pour l'enrichissement Core Network
    """
    
    def __init__(self):
        # Références 3GPP complètes
        self.gpp_references = {
            # HLR/2G/3G
            'call_forwarding': '3GPP_TS_23.082',
            'call_barring': '3GPP_TS_23.088',
            'camel': '3GPP_TS_23.078',
            'map': '3GPP_TS_29.002',
            'hlr': '3GPP_TS_23.012',
            
            # EPC/HSS/4G
            'epc': '3GPP_TS_23.401',
            's6a': '3GPP_TS_29.272',
            'diameter': '3GPP_TS_29.229',
            'authentication': '3GPP_TS_29.272',
            'location_update': '3GPP_TS_29.272',
            'hss': '3GPP_TS_29.328',
            
            # IMS/VoLTE
            'ims': '3GPP_TS_23.228',
            'volte': '3GPP_TS_23.216',
            'srvcc': '3GPP_TS_23.216',
            'cx_dx': '3GPP_TS_29.229',
            'sh': '3GPP_TS_29.329',
            
            # Diameter Routing
            'diameter_routing': '3GPP_TS_29.212',
            'rx_interface': '3GPP_TS_29.214',
            'gx_interface': '3GPP_TS_29.212',
            
            # Performance & QoS
            'qos': '3GPP_TS_23.107',
            'performance': '3GPP_TS_32.401',
            
            # Security
            'security': '3GPP_TS_33.102',
            'authentication_security': '3GPP_TS_33.401'
        }
        
        # Labels techniques complets
        self.technical_labels = {
            # Domaines techniques
            'network_domains': ['2G', '3G', '4G', '5G'],
            'protocols': ['MAP', 'Diameter', 'SOAP', 'HTTP', 'SNMP'],
            'interfaces': ['S6a', 'S6d', 'Cx', 'Dx', 'Sh', 'Rx', 'Gx', 'Swx'],
            
            # Types de tests ISTQB
            'test_types': ['Functional', 'NonFunctional'],
            'test_levels': ['Unit', 'Integration', 'System', 'Acceptance'],
            'test_methods': ['Manual', 'Automated'],
            'test_objectives': ['Regression', 'Smoke', 'Sanity', 'Performance', 'Security'],
            
            # Domaines fonctionnels
            'functional_domains': [
                'Authentication', 'Authorization', 'CallForwarding', 'CallBarring',
                'LocationUpdate', 'Routing', 'Provisioning', 'Configuration',
                'Backup', 'Restore', 'Monitoring', 'Logging'
            ],
            
            # Domaines non-fonctionnels
            'nonfunctional_domains': [
                'Performance', 'Security', 'Reliability', 'Scalability',
                'Availability', 'Maintainability', 'Usability', 'Portability'
            ],
            
            # Composants Core Network
            'components': ['HLR', 'EPC-HSS', 'IMS-HSS', 'MME', 'SGSN', 'GGSN', 'P-GW', 'S-GW'],
            
            # Vendors
            'vendors': ['ZTE', 'Huawei', 'Nokia', 'Ericsson', 'Cisco']
        }
        
        # Test Sets complets organisés par domaines
        self.test_sets_complete = {
            # HLR (2G/3G) Test Sets
            'hlr_domain': {
                'HLR Call Forwarding Services': {
                    'description': 'Tests des services de renvoi d\'appel selon 3GPP TS 23.082',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', '2G', 'MAP', 'CallForwarding'],
                    'gpp_ref': '3GPP_TS_23.082',
                    'patterns': [r'call.*forward', r'cfu', r'cfb', r'cfnr', r'renvoi']
                },
                'HLR Call Barring Services': {
                    'description': 'Tests des services d\'interdiction d\'appel selon 3GPP TS 23.088',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', '2G', 'MAP', 'CallBarring'],
                    'gpp_ref': '3GPP_TS_23.088',
                    'patterns': [r'call.*barr', r'interdiction', r'restriction']
                },
                'HLR CAMEL Services': {
                    'description': 'Tests des services CAMEL selon 3GPP TS 23.078',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', '2G', 'MAP', 'CAMEL'],
                    'gpp_ref': '3GPP_TS_23.078',
                    'patterns': [r'camel', r'intelligent.*network']
                },
                'HLR Location Management': {
                    'description': 'Tests de gestion de localisation HLR selon 3GPP TS 23.012',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', '2G', 'MAP', 'LocationUpdate'],
                    'gpp_ref': '3GPP_TS_23.012',
                    'patterns': [r'location.*update', r'sri', r'ati', r'localisation']
                },
                'HLR Authentication Services': {
                    'description': 'Tests d\'authentification HLR selon 3GPP TS 29.002',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', '2G', 'MAP', 'Authentication'],
                    'gpp_ref': '3GPP_TS_29.002',
                    'patterns': [r'authentication', r'auth', r'sai', r'triplet']
                }
            },
            
            # EPC-HSS (4G) Test Sets
            'epc_hss_domain': {
                'EPC HSS Authentication': {
                    'description': 'Tests d\'authentification EPC-HSS via S6a selon 3GPP TS 29.272',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', '4G', 'Diameter', 'S6a', 'Authentication'],
                    'gpp_ref': '3GPP_TS_29.272',
                    'patterns': [r'authentication', r'air', r'aia', r'auth.*vector']
                },
                'EPC HSS Location Management': {
                    'description': 'Tests de gestion de localisation EPC-HSS selon 3GPP TS 29.272',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', '4G', 'Diameter', 'S6a', 'LocationUpdate'],
                    'gpp_ref': '3GPP_TS_29.272',
                    'patterns': [r'update.*location', r'ulr', r'ula', r'location.*update']
                },
                'EPC HSS Subscription Management': {
                    'description': 'Tests de gestion des souscriptions EPC-HSS selon 3GPP TS 29.272',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', '4G', 'Diameter', 'S6a', 'Provisioning'],
                    'gpp_ref': '3GPP_TS_29.272',
                    'patterns': [r'subscription', r'profile', r'provisioning', r'idr', r'ida']
                },
                'EPC HSS Notification Services': {
                    'description': 'Tests des services de notification EPC-HSS selon 3GPP TS 29.272',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', '4G', 'Diameter', 'S6a'],
                    'gpp_ref': '3GPP_TS_29.272',
                    'patterns': [r'notification', r'nor', r'noa', r'notify']
                }
            },
            
            # IMS Test Sets
            'ims_domain': {
                'IMS VoLTE Services': {
                    'description': 'Tests des services VoLTE IMS selon 3GPP TS 23.216',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', '4G', 'IMS', 'VoLTE'],
                    'gpp_ref': '3GPP_TS_23.216',
                    'patterns': [r'volte', r'voice.*over.*lte', r'srvcc']
                },
                'IMS Cx/Dx Interface': {
                    'description': 'Tests de l\'interface Cx/Dx IMS selon 3GPP TS 29.229',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', '4G', 'IMS', 'Diameter', 'Cx'],
                    'gpp_ref': '3GPP_TS_29.229',
                    'patterns': [r'cx', r'dx', r'sar', r'saa', r'mar', r'maa']
                },
                'IMS Sh Interface': {
                    'description': 'Tests de l\'interface Sh IMS selon 3GPP TS 29.329',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', '4G', 'IMS', 'Diameter', 'Sh'],
                    'gpp_ref': '3GPP_TS_29.329',
                    'patterns': [r'sh', r'udr', r'uda', r'pur', r'pua']
                },
                'IMS T-ADS Services': {
                    'description': 'Tests des services T-ADS IMS',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', '4G', 'IMS', 'TADS'],
                    'gpp_ref': '3GPP_TS_23.228',
                    'patterns': [r't-ads', r'tads', r'telephony.*application']
                }
            },
            
            # Performance Test Sets
            'performance_domain': {
                'Performance Load Testing': {
                    'description': 'Tests de charge et performance selon 3GPP TS 32.401',
                    'mandatory_labels': ['NonFunctional', 'System', 'Manual', 'Regression', 'Performance', 'Load'],
                    'gpp_ref': '3GPP_TS_32.401',
                    'patterns': [r'performance', r'load', r'stress', r'capacity', r'throughput']
                },
                'Performance Diameter Routing': {
                    'description': 'Tests de performance du routage Diameter selon 3GPP TS 29.212',
                    'mandatory_labels': ['NonFunctional', 'System', 'Manual', 'Regression', 'Performance', 'Diameter', 'Routing'],
                    'gpp_ref': '3GPP_TS_29.212',
                    'patterns': [r'diameter.*routing', r'routing.*performance', r'dra']
                },
                'Performance Scalability': {
                    'description': 'Tests de scalabilité et montée en charge',
                    'mandatory_labels': ['NonFunctional', 'System', 'Manual', 'Regression', 'Performance', 'Scalability'],
                    'gpp_ref': '3GPP_TS_32.401',
                    'patterns': [r'scalability', r'scaling', r'montee.*charge']
                }
            },
            
            # Security Test Sets
            'security_domain': {
                'Security Vulnerability Assessment': {
                    'description': 'Tests d\'évaluation des vulnérabilités selon 3GPP TS 33.102',
                    'mandatory_labels': ['NonFunctional', 'System', 'Manual', 'Regression', 'Security', 'Vulnerability'],
                    'gpp_ref': '3GPP_TS_33.102',
                    'patterns': [r'vulnerability', r'scan', r'penetration', r'security.*test']
                },
                'Security CIS Compliance': {
                    'description': 'Tests de conformité CIS (Center for Internet Security)',
                    'mandatory_labels': ['NonFunctional', 'System', 'Manual', 'Regression', 'Security', 'CIS'],
                    'gpp_ref': '3GPP_TS_33.102',
                    'patterns': [r'cis', r'center.*internet.*security', r'hardening']
                },
                'Security RBAC Management': {
                    'description': 'Tests de gestion RBAC (Role-Based Access Control)',
                    'mandatory_labels': ['NonFunctional', 'System', 'Manual', 'Regression', 'Security', 'RBAC'],
                    'gpp_ref': '3GPP_TS_33.102',
                    'patterns': [r'rbac', r'role.*based', r'access.*control', r'authorization']
                },
                'Security Authentication': {
                    'description': 'Tests de sécurité d\'authentification selon 3GPP TS 33.401',
                    'mandatory_labels': ['NonFunctional', 'System', 'Manual', 'Regression', 'Security', 'Authentication'],
                    'gpp_ref': '3GPP_TS_33.401',
                    'patterns': [r'security.*auth', r'auth.*security', r'credential']
                }
            },
            
            # OAM Test Sets
            'oam_domain': {
                'OAM SOAP Interface': {
                    'description': 'Tests de l\'interface SOAP pour OAM',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', 'OAM', 'SOAP'],
                    'gpp_ref': '3GPP_TS_32.101',
                    'patterns': [r'soap', r'web.*service', r'oam.*interface']
                },
                'OAM Provisioning': {
                    'description': 'Tests de provisioning et configuration OAM',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', 'OAM', 'Provisioning'],
                    'gpp_ref': '3GPP_TS_32.101',
                    'patterns': [r'provisioning', r'configuration', r'management', r'mml']
                },
                'OAM Monitoring': {
                    'description': 'Tests de monitoring et supervision OAM',
                    'mandatory_labels': ['Functional', 'System', 'Manual', 'Regression', 'OAM', 'Monitoring'],
                    'gpp_ref': '3GPP_TS_32.101',
                    'patterns': [r'monitoring', r'supervision', r'alarm', r'snmp']
                }
            },
            
            # Backup/Restore Test Sets
            'backup_domain': {
                'Backup and Restore Operations': {
                    'description': 'Tests des opérations de sauvegarde et restauration',
                    'mandatory_labels': ['NonFunctional', 'System', 'Manual', 'Regression', 'BackupRestore'],
                    'gpp_ref': '3GPP_TS_32.101',
                    'patterns': [r'backup', r'restore', r'recovery', r'sauvegarde']
                },
                'Backup Scheduling': {
                    'description': 'Tests de planification des sauvegardes',
                    'mandatory_labels': ['NonFunctional', 'System', 'Manual', 'Regression', 'BackupRestore', 'Scheduling'],
                    'gpp_ref': '3GPP_TS_32.101',
                    'patterns': [r'schedule.*backup', r'planification', r'automatic.*backup']
                }
            },
            
            # Resiliency Test Sets
            'resiliency_domain': {
                'Resiliency and Redundancy': {
                    'description': 'Tests de résilience et redondance',
                    'mandatory_labels': ['NonFunctional', 'System', 'Manual', 'Regression', 'Resiliency'],
                    'gpp_ref': '3GPP_TS_23.007',
                    'patterns': [r'resiliency', r'redundancy', r'failover', r'geo.*redundancy']
                },
                'High Availability': {
                    'description': 'Tests de haute disponibilité',
                    'mandatory_labels': ['NonFunctional', 'System', 'Manual', 'Regression', 'Availability'],
                    'gpp_ref': '3GPP_TS_23.007',
                    'patterns': [r'high.*availability', r'ha', r'availability', r'uptime']
                }
            }
        }
    
    def get_all_test_sets(self):
        """Retourne tous les test sets organisés"""
        all_sets = {}
        for domain, sets in self.test_sets_complete.items():
            all_sets.update(sets)
        return all_sets
    
    def get_test_set_by_pattern(self, text: str):
        """Trouve le test set approprié selon les patterns"""
        text_lower = text.lower()
        
        for domain, sets in self.test_sets_complete.items():
            for test_set_name, config in sets.items():
                for pattern in config['patterns']:
                    if re.search(pattern, text_lower):
                        return test_set_name, config
        
        return None, None
    
    def get_mandatory_labels_for_test_set(self, test_set_name: str):
        """Retourne les labels obligatoires pour un test set"""
        for domain, sets in self.test_sets_complete.items():
            if test_set_name in sets:
                return sets[test_set_name]['mandatory_labels']
        return []
    
    def get_gpp_reference_for_test_set(self, test_set_name: str):
        """Retourne la référence 3GPP pour un test set"""
        for domain, sets in self.test_sets_complete.items():
            if test_set_name in sets:
                return sets[test_set_name].get('gpp_ref', '')
        return ''
