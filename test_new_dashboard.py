#!/usr/bin/env python3
"""
Test du nouveau dashboard centré sur le dernier fichier
"""

import urllib.request
import json

def test_new_dashboard_api():
    """Test de la nouvelle API du dashboard"""
    
    print("🧪 Test du Nouveau Dashboard")
    print("=" * 50)
    
    try:
        # Test de l'API des statistiques du dernier fichier
        with urllib.request.urlopen('http://127.0.0.1:5000/api/latest-file-stats', timeout=10) as response:
            data = json.loads(response.read().decode('utf-8'))
            
            print("📊 Statistiques du dernier fichier:")
            print(f"   📁 Fichier: {data.get('filename', 'N/A')}")
            print(f"   📅 Traité le: {data.get('processed_date', 'N/A')}")
            print(f"   🧪 Tests: {data.get('total_tests', 0)}")
            print(f"   🏷️ Colonnes Labels: {data.get('labels_columns', 0)}")
            
            # Composants
            components = data.get('components_distribution', {})
            print(f"   🔧 Composants uniques: {len(components)}")
            if components:
                top_component = max(components.items(), key=lambda x: x[1])
                print(f"      Top composant: {top_component[0]} ({top_component[1]} tests)")
            
            # Test Sets
            testsets = data.get('testsets_distribution', {})
            print(f"   📋 Test Sets uniques: {len(testsets)}")
            if testsets:
                top_testset = max(testsets.items(), key=lambda x: x[1])
                print(f"      Top Test Set: {top_testset[0]} ({top_testset[1]} tests)")
            
            # Fichiers récents
            recent_files = data.get('recent_files', [])
            print(f"   📚 Fichiers récents: {len(recent_files)}")
            
            print("\n✅ API du nouveau dashboard fonctionne!")
            return True
            
    except Exception as e:
        print(f"❌ Erreur API: {e}")
        return False

def test_dashboard_page():
    """Test de la page du dashboard"""
    
    try:
        with urllib.request.urlopen('http://127.0.0.1:5000/dashboard', timeout=10) as response:
            html_content = response.read().decode('utf-8')
            
            # Vérifier que les nouveaux éléments sont présents
            checks = [
                ('Dernier Fichier Traité', 'Titre du dashboard'),
                ('current-file-tests', 'Statistique tests du fichier'),
                ('current-file-labels', 'Statistique colonnes labels'),
                ('current-file-components', 'Statistique composants'),
                ('current-file-testsets', 'Statistique test sets'),
                ('testSetsChart', 'Graphique Test Sets'),
                ('top-components', 'Section top composants'),
                ('top-testsets', 'Section top test sets'),
                ('api/latest-file-stats', 'Appel à la nouvelle API')
            ]
            
            print("\n🔍 Vérification de la page dashboard:")
            all_good = True
            
            for check_text, description in checks:
                if check_text in html_content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description} - MANQUANT")
                    all_good = False
            
            if all_good:
                print("\n✅ Page dashboard mise à jour correctement!")
                return True
            else:
                print("\n❌ Certains éléments manquent dans la page")
                return False
                
    except Exception as e:
        print(f"❌ Erreur page: {e}")
        return False

if __name__ == "__main__":
    print("🎯 TEST COMPLET DU NOUVEAU DASHBOARD")
    print("=" * 60)
    
    # Vérifier que le serveur est accessible
    try:
        with urllib.request.urlopen("http://127.0.0.1:5000", timeout=5) as response:
            print("✅ Serveur Flask accessible")
    except:
        print("❌ Serveur Flask non accessible")
        print("   Lancez d'abord: python app.py")
        exit(1)
    
    # Tests
    api_success = test_new_dashboard_api()
    page_success = test_dashboard_page()
    
    print("\n" + "=" * 60)
    print("📋 RÉSULTATS:")
    print(f"   API Dashboard: {'✅ RÉUSSI' if api_success else '❌ ÉCHOUÉ'}")
    print(f"   Page Dashboard: {'✅ RÉUSSI' if page_success else '❌ ÉCHOUÉ'}")
    
    if api_success and page_success:
        print("\n🎉 NOUVEAU DASHBOARD ENTIÈREMENT FONCTIONNEL!")
        print("🎯 Fonctionnalités:")
        print("   • Focus sur le dernier fichier traité")
        print("   • Camembert des composants du fichier")
        print("   • Camembert des Test Sets du fichier")
        print("   • Top 5 composants et Test Sets")
        print("   • Informations détaillées du fichier")
    else:
        print("\n💥 PROBLÈMES DÉTECTÉS DANS LE DASHBOARD")
        
    print("=" * 60)
