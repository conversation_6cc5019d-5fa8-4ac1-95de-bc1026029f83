#!/usr/bin/env python3
"""
Test final de validation de toutes les fonctionnalités
"""

import pandas as pd
import os
from json_steps_processor import J<PERSON>NStepsProcessor
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def print_header(title):
    print("\n" + "="*70)
    print(f"  {title}")
    print("="*70)

def test_apercu_json():
    """
    Test de l'aperçu JSON
    """
    print_header("👁️ TEST APERÇU JSON")
    
    processor = JSONStepsProcessor()
    samples = processor.preview_json_extraction('example_multiple_steps.csv', max_samples=3)
    
    print(f"📊 Nombre d'échantillons : {len(samples)}")
    
    if samples:
        print(f"\n📋 Aperçu des extractions :")
        for i, sample in enumerate(samples):
            print(f"\n{i+1}. Ligne {sample['row']} - {sample['summary']}")
            print(f"   Action : {sample['extracted']['Action']}")
            print(f"   Data : {sample['extracted']['Data']}")
            print(f"   Expected Result : {sample['extracted']['Expected Result']}")
        
        # Vérifier qu'il y a des multiples steps détectés
        multiple_steps_detected = any('+' in sample['extracted']['Action'] for sample in samples)
        print(f"\n✅ Multiples steps détectés : {'OUI' if multiple_steps_detected else 'NON'}")
        
        return len(samples) > 0 and multiple_steps_detected
    else:
        print("❌ Aucun échantillon trouvé")
        return False

def test_extraction_complete():
    """
    Test de l'extraction complète
    """
    print_header("🔧 TEST EXTRACTION COMPLÈTE")
    
    processor = JSONStepsProcessor()
    stats = processor.process_csv_file('example_multiple_steps.csv', 'test_final_extraction.csv')
    
    print(f"📊 Statistiques d'extraction :")
    print(f"   Tickets traités : {stats['processed']}")
    print(f"   Étapes extraites : {stats['extracted']}")
    print(f"   Lignes dupliquées : {stats['duplicated_rows']}")
    print(f"   Erreurs : {stats['errors']}")
    
    # Vérifier le fichier généré
    if os.path.exists('test_final_extraction.csv'):
        with open('test_final_extraction.csv', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"\n📁 Fichier généré :")
        print(f"   Lignes totales : {len(lines)}")
        print(f"   Séparateur point-virgule : {';' in lines[0]}")
        
        # Vérifier les colonnes
        header = lines[0].strip().replace('"', '')
        expected_columns = ['Summary', 'Action', 'Data', 'Expected Result']
        columns_present = all(col in header for col in expected_columns)
        print(f"   Colonnes requises présentes : {'✅ OUI' if columns_present else '❌ NON'}")
        
        # Nettoyer
        os.remove('test_final_extraction.csv')
        
        return stats['extracted'] > stats['processed'] and columns_present
    else:
        print("❌ Fichier non généré")
        return False

def test_enrichissement_avec_steps():
    """
    Test de l'enrichissement avec les steps extraits
    """
    print_header("🎯 TEST ENRICHISSEMENT AVEC STEPS")
    
    # Étape 1 : Extraction
    processor = JSONStepsProcessor()
    processor.process_csv_file('example_multiple_steps.csv', 'test_final_steps.csv')
    
    # Étape 2 : Enrichissement
    tool = TestEnrichmentToolEnhanced()
    tool.enrich_csv_enhanced('test_final_steps.csv', 'test_final_enriched.csv', 'separate')
    
    # Vérifier le résultat
    if os.path.exists('test_final_enriched.csv'):
        with open('test_final_enriched.csv', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📁 Fichier enrichi :")
        print(f"   Lignes totales : {len(lines)}")
        print(f"   Séparateur point-virgule : {';' in lines[0]}")
        
        # Vérifier les colonnes essentielles
        header = lines[0].strip()
        essential_columns = ['Summary', 'Action', 'Data', 'Expected Result', 'Component/s', 'Test Set']
        labels_count = header.count('Labels')
        
        print(f"   Colonnes Labels identiques : {labels_count}")
        print(f"   Colonnes essentielles : {'✅ Toutes présentes' if all(col in header for col in essential_columns) else '❌ Manquantes'}")
        
        # Vérifier qu'il n'y a pas de valeurs "nan"
        content = ''.join(lines)
        nan_count = content.lower().count('nan')
        print(f"   Valeurs 'nan' indésirables : {nan_count}")
        
        # Nettoyer
        os.remove('test_final_steps.csv')
        os.remove('test_final_enriched.csv')
        
        return labels_count >= 8 and nan_count == 0 and len(lines) > 6
    else:
        print("❌ Fichier enrichi non généré")
        return False

def test_format_final():
    """
    Test du format final avec votre exemple réel
    """
    print_header("📋 TEST FORMAT FINAL")
    
    # Créer un fichier avec votre exemple exact
    data = {
        'Summary': ['Subscriber Query/Export'],
        'Description': ['Test subscriber export functionality'],
        'Component/s': [''],
        'Custom field (Manual Test Steps)': [
            '[{"id":3665376,"index":1,"fields":{"Action":"Export/Query Subscribers","Data":"","Expected Result":"Verify if subscribers are recorded correctly"}},{"id":3665377,"index":2,"fields":{"Action":"Verify if can be exported automatically to an external SFTP server","Data":"","Expected Result":"The subscribers are on the external server"}}]'
        ]
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_format_real.csv', index=False)
    
    # Traitement complet
    processor = JSONStepsProcessor()
    processor.process_csv_file('test_format_real.csv', 'test_format_steps.csv')
    
    tool = TestEnrichmentToolEnhanced()
    tool.enrich_csv_enhanced('test_format_steps.csv', 'test_format_final.csv', 'separate')
    
    # Vérifier le résultat
    if os.path.exists('test_format_final.csv'):
        with open('test_format_final.csv', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📊 Résultat avec votre exemple :")
        print(f"   1 test original → {len(lines)-1} lignes finales")
        print(f"   Duplication réussie : {'✅ OUI' if len(lines) == 3 else '❌ NON'}")
        
        # Afficher les actions extraites
        if len(lines) >= 3:
            line1_parts = lines[1].split(';')
            line2_parts = lines[2].split(';')
            
            # Trouver l'index de la colonne Action
            header_parts = lines[0].split(';')
            action_index = None
            for i, col in enumerate(header_parts):
                if 'Action' in col:
                    action_index = i
                    break
            
            if action_index and len(line1_parts) > action_index and len(line2_parts) > action_index:
                action1 = line1_parts[action_index].strip('"')
                action2 = line2_parts[action_index].strip('"')
                
                print(f"   Action 1 : {action1}")
                print(f"   Action 2 : {action2}")
                
                actions_different = action1 != action2
                print(f"   Actions différentes : {'✅ OUI' if actions_different else '❌ NON'}")
        
        # Nettoyer
        os.remove('test_format_real.csv')
        os.remove('test_format_steps.csv')
        os.remove('test_format_final.csv')
        
        return len(lines) == 3
    else:
        print("❌ Fichier final non généré")
        return False

def main():
    """
    Test final complet
    """
    print("🧪 VALIDATION FINALE DE TOUTES LES FONCTIONNALITÉS")
    
    # Test 1 : Aperçu JSON
    test1 = test_apercu_json()
    
    # Test 2 : Extraction complète
    test2 = test_extraction_complete()
    
    # Test 3 : Enrichissement avec steps
    test3 = test_enrichissement_avec_steps()
    
    # Test 4 : Format final
    test4 = test_format_final()
    
    print_header("📊 RÉSULTATS FINAUX")
    
    print(f"✅ Aperçu JSON : {'OK' if test1 else 'ÉCHEC'}")
    print(f"✅ Extraction complète : {'OK' if test2 else 'ÉCHEC'}")
    print(f"✅ Enrichissement avec steps : {'OK' if test3 else 'ÉCHEC'}")
    print(f"✅ Format final : {'OK' if test4 else 'ÉCHEC'}")
    
    if test1 and test2 and test3 and test4:
        print(f"\n🎉 TOUTES LES FONCTIONNALITÉS VALIDÉES !")
        print(f"✅ Aperçu JSON fonctionne correctement")
        print(f"✅ Extraction avec multiples steps opérationnelle")
        print(f"✅ Séparateur point-virgule dans tous les fichiers")
        print(f"✅ Enrichissement préserve les colonnes Action/Data/Expected Result")
        print(f"✅ Format final parfait pour import")
        print(f"🎯 L'application web est prête pour utilisation")
    else:
        print(f"\n⚠️  CERTAINES FONCTIONNALITÉS NÉCESSITENT DES AJUSTEMENTS")
        print(f"💡 Vérifiez les erreurs ci-dessus")

if __name__ == "__main__":
    main()
