#!/usr/bin/env python3
"""
Test de la fonctionnalité colonnes séparées via l'application web
"""

import pandas as pd
import os
from test_enrichment_tool_v2 import TestEnrichmentToolV2

def test_with_example_file():
    """
    Test avec le fichier d'exemple existant
    """
    print("=== TEST COLONNES SÉPARÉES AVEC FICHIER D'EXEMPLE ===\n")
    
    if not os.path.exists('example_tests.csv'):
        print("❌ Fichier example_tests.csv non trouvé")
        return
    
    # Enrichir avec la nouvelle version
    tool = TestEnrichmentToolV2()
    output_file = 'example_tests_separate_labels.csv'
    
    print("Enrichissement en cours...")
    tool.enrich_csv_with_separate_labels('example_tests.csv', output_file)
    
    # Analyser le résultat
    print("\n=== ANALYSE DU RÉSULTAT ===")
    df = pd.read_csv(output_file)
    
    print(f"Nombre de tests : {len(df)}")
    print(f"Nombre total de colonnes : {len(df.columns)}")
    
    # Compter les colonnes Labels
    label_columns = [i for i, col in enumerate(df.columns) if col == 'Labels']
    print(f"Nombre de colonnes 'Labels' : {len(label_columns)}")
    
    # Afficher les en-têtes
    print(f"\nEn-têtes des colonnes :")
    for i, col in enumerate(df.columns):
        if col == 'Labels':
            print(f"  Colonne {i+1}: {col} (Labels)")
        elif col in ['Summary', 'Component/s', 'Test Set']:
            print(f"  Colonne {i+1}: {col}")
    
    # Afficher quelques exemples
    print(f"\n=== EXEMPLES DE TESTS ENRICHIS ===")
    
    for index in [0, 5, 10]:  # Afficher 3 exemples
        if index < len(df):
            row = df.iloc[index]
            print(f"\n--- Test {index+1}: {row['Summary']} ---")
            print(f"Composant: {row['Component/s']}")
            print(f"Test Set: {row['Test Set']}")
            print("Labels:")
            
            labels_found = []
            for col_idx in label_columns:
                label_value = row.iloc[col_idx]
                if pd.notna(label_value) and label_value != '':
                    labels_found.append(label_value)
                    print(f"  • {label_value}")
            
            print(f"Total labels: {len(labels_found)}")
    
    # Vérification pour Jira
    print(f"\n=== VÉRIFICATION JIRA ===")
    
    # Vérifier qu'il n'y a qu'un label par colonne
    issues = 0
    for col_idx in label_columns:
        for row_idx, value in enumerate(df.iloc[:, col_idx]):
            if pd.notna(value) and value != '':
                if ',' in str(value):
                    print(f"❌ Problème ligne {row_idx+1}: '{value}' contient plusieurs labels")
                    issues += 1
    
    if issues == 0:
        print("✅ Format parfait pour Jira : un seul label par colonne")
    else:
        print(f"❌ {issues} problème(s) détecté(s)")
    
    # Statistiques finales
    total_labels = 0
    for col_idx in label_columns:
        non_empty = df.iloc[:, col_idx].notna() & (df.iloc[:, col_idx] != '')
        total_labels += non_empty.sum()
    
    print(f"\n📊 Statistiques finales :")
    print(f"   • Total labels assignés : {total_labels}")
    print(f"   • Moyenne par test : {total_labels / len(df):.1f}")
    print(f"   • Colonnes Labels créées : {len(label_columns)}")
    
    print(f"\n✅ Fichier de sortie créé : {output_file}")
    print("🎯 Prêt pour import dans Jira Xray !")

def compare_formats():
    """
    Compare les deux formats (standard vs séparé)
    """
    print("\n=== COMPARAISON DES FORMATS ===\n")
    
    if not os.path.exists('example_tests.csv'):
        print("❌ Fichier example_tests.csv non trouvé")
        return
    
    # Format standard
    from test_enrichment_tool import TestEnrichmentTool
    tool_standard = TestEnrichmentTool()
    tool_standard.enrich_csv('example_tests.csv', 'format_standard.csv')
    
    # Format séparé
    tool_separate = TestEnrichmentToolV2()
    tool_separate.enrich_csv_with_separate_labels('example_tests.csv', 'format_separate.csv')
    
    # Comparer
    df_standard = pd.read_csv('format_standard.csv')
    df_separate = pd.read_csv('format_separate.csv')
    
    print("FORMAT STANDARD :")
    print(f"  • Colonnes : {len(df_standard.columns)}")
    print(f"  • Colonnes Labels : 1")
    print(f"  • Exemple : {df_standard.iloc[0]['Labels']}")
    
    print("\nFORMAT SÉPARÉ :")
    print(f"  • Colonnes : {len(df_separate.columns)}")
    label_cols = [col for col in df_separate.columns if col == 'Labels']
    print(f"  • Colonnes Labels : {len(label_cols)}")
    
    # Afficher les labels du premier test
    first_row_labels = []
    for i, col in enumerate(df_separate.columns):
        if col == 'Labels':
            value = df_separate.iloc[0, i]
            if pd.notna(value) and value != '':
                first_row_labels.append(value)
    
    print(f"  • Exemple : {first_row_labels}")
    
    # Nettoyer
    os.remove('format_standard.csv')
    os.remove('format_separate.csv')
    
    print("\n✅ Comparaison terminée")

if __name__ == "__main__":
    test_with_example_file()
    compare_formats()
