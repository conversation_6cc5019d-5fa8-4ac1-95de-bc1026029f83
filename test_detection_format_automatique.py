#!/usr/bin/env python3
"""
Test de la détection automatique du format 'separate' pour les fichiers avec Action/Data/Expected Result
"""

import pandas as pd
import os
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def test_detection_format_automatique():
    """
    Test que l'enrichissement détecte automatiquement le format 'separate' 
    pour les fichiers contenant Action, Data, Expected Result
    """
    print("🧪 TEST DÉTECTION AUTOMATIQUE FORMAT 'SEPARATE'")
    print("="*70)
    
    # Créer un fichier avec les colonnes Action, Data, Expected Result (comme après extraction JSON)
    data = {
        'Summary': ['S6a ULR', 'VoLTE Call'],
        'Issue key': ['TEST-001', 'TEST-002'],
        'Component/s': ['EPC-HSS', 'IMS-HSS'],
        'Priority': ['High', 'Medium'],
        'Description': ['Test Update Location Request', 'Test VoLTE call setup'],
        'Action': ['Send ULR', 'Setup call'],
        'Data': ['IMSI=123456789012345', ''],
        'Expected Result': ['HSS responds with ULA', 'Call established']
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_detection_format.csv', index=False)
    
    print(f"📁 Fichier créé avec colonnes Action/Data/Expected Result")
    print(f"   Ce fichier devrait automatiquement utiliser le format 'separate'")
    print(f"   Résultat attendu : Plusieurs colonnes 'Labels' identiques")
    
    # Test d'enrichissement avec format automatique
    try:
        tool = TestEnrichmentToolEnhanced()
        
        # Simuler la détection automatique comme dans l'application
        df_check = pd.read_csv('test_detection_format.csv', nrows=0)
        columns = df_check.columns.tolist()
        
        has_extracted_columns = all(col in columns for col in ['Action', 'Data', 'Expected Result'])
        
        if has_extracted_columns:
            detected_format = 'separate'
            print(f"✅ Détection automatique : Format 'separate' détecté")
        else:
            detected_format = 'standard'
            print(f"❌ Détection automatique : Format 'standard' détecté (problème)")
        
        # Enrichissement avec le format détecté
        tool.enrich_csv_enhanced('test_detection_format.csv', 'test_detection_enriched.csv', detected_format)
        
        print("✅ Enrichissement terminé")
        
        # Analyser le résultat
        if os.path.exists('test_detection_enriched.csv'):
            with open('test_detection_enriched.csv', 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
            
            if lines:
                header_line = lines[0]
                header_parts = header_line.split(',')
                
                print(f"\n📋 ANALYSE DU RÉSULTAT :")
                print(f"   Lignes totales : {len(lines)}")
                print(f"   Colonnes totales : {len(header_parts)}")
                
                # Compter les colonnes Labels
                labels_count = header_parts.count('Labels')
                print(f"   Colonnes 'Labels' : {labels_count}")
                
                # Vérifier les colonnes essentielles
                essential_columns = ['Summary', 'Action', 'Data', 'Expected Result', 'Component/s', 'Test Set']
                missing_essential = []
                for col in essential_columns:
                    if col not in header_line:
                        missing_essential.append(col)
                
                print(f"   Colonnes essentielles manquantes : {len(missing_essential)}")
                
                # Afficher l'en-tête Labels
                if labels_count > 1:
                    labels_header = ','.join(['Labels'] * labels_count)
                    print(f"\n📋 Format Labels généré :")
                    print(f"   {labels_header}")
                    
                    # Afficher les données
                    print(f"\n📋 Données des tests :")
                    labels_positions = [i for i, col in enumerate(header_parts) if col == 'Labels']
                    
                    for i, line in enumerate(lines[1:], 1):
                        if line.strip():
                            data_parts = line.split(',')
                            
                            # Extraire les labels
                            test_labels = []
                            for pos in labels_positions:
                                if pos < len(data_parts):
                                    label = data_parts[pos].strip()
                                    if label:
                                        test_labels.append(label)
                            
                            summary = data_parts[0] if len(data_parts) > 0 else f"Test {i}"
                            print(f"   Test {i} ({summary}): {','.join(test_labels)}")
                
                # Vérifier le succès
                success = (detected_format == 'separate' and labels_count >= 6 and 
                          len(missing_essential) == 0)
                
                print(f"\n🎯 RÉSULTAT DE LA DÉTECTION :")
                print(f"✅ Format 'separate' détecté : {'OUI' if detected_format == 'separate' else 'NON'}")
                print(f"✅ Colonnes Labels multiples : {'OUI' if labels_count >= 6 else 'NON'} ({labels_count})")
                print(f"✅ Colonnes essentielles présentes : {'OUI' if len(missing_essential) == 0 else 'NON'}")
                
                if success:
                    print(f"\n🎉 DÉTECTION AUTOMATIQUE PARFAITE !")
                    print(f"📊 Résumé :")
                    print(f"   Fichier avec Action/Data/Expected Result → Format 'separate'")
                    print(f"   {labels_count} colonnes 'Labels' créées automatiquement")
                    print(f"   Format: Labels,Labels,Labels,Labels,Labels,Labels")
                    print(f"🎯 Même résultat que l'enrichissement direct !")
                else:
                    print(f"\n⚠️  La détection automatique nécessite des ajustements")
            else:
                print("❌ Fichier enrichi vide")
                success = False
            
            # Nettoyer
            os.remove('test_detection_enriched.csv')
        else:
            print("❌ Fichier enrichi non généré")
            success = False
    
    except Exception as e:
        print(f"❌ Erreur lors du test : {e}")
        import traceback
        print(traceback.format_exc())
        success = False
    
    # Nettoyer
    os.remove('test_detection_format.csv')
    
    return success

def test_comparaison_formats():
    """
    Test de comparaison entre enrichissement direct et après extraction JSON
    """
    print("\n" + "="*70)
    print("📋 TEST COMPARAISON FORMATS")
    print("="*70)
    
    # Créer un fichier standard (sans Action/Data/Expected Result)
    data_standard = {
        'Summary': ['S6a ULR', 'VoLTE Call'],
        'Component/s': ['EPC-HSS', 'IMS-HSS'],
        'Description': ['Test Update Location Request', 'Test VoLTE call setup']
    }
    
    df_standard = pd.DataFrame(data_standard)
    df_standard.to_csv('test_standard.csv', index=False)
    
    # Créer un fichier avec Action/Data/Expected Result
    data_extracted = {
        'Summary': ['S6a ULR', 'VoLTE Call'],
        'Component/s': ['EPC-HSS', 'IMS-HSS'],
        'Description': ['Test Update Location Request', 'Test VoLTE call setup'],
        'Action': ['Send ULR', 'Setup call'],
        'Data': ['IMSI=123456789012345', ''],
        'Expected Result': ['HSS responds with ULA', 'Call established']
    }
    
    df_extracted = pd.DataFrame(data_extracted)
    df_extracted.to_csv('test_extracted.csv', index=False)
    
    print(f"📁 Fichiers créés pour comparaison")
    
    try:
        tool = TestEnrichmentToolEnhanced()
        
        # Test 1 : Fichier standard
        print(f"\n🔧 Test 1 : Fichier standard")
        tool.enrich_csv_enhanced('test_standard.csv', 'test_standard_enriched.csv', 'separate')
        
        # Test 2 : Fichier avec extraction JSON
        print(f"\n🔧 Test 2 : Fichier avec extraction JSON")
        tool.enrich_csv_enhanced('test_extracted.csv', 'test_extracted_enriched.csv', 'separate')
        
        # Comparer les résultats
        print(f"\n📊 COMPARAISON DES RÉSULTATS :")
        
        # Analyser fichier standard
        with open('test_standard_enriched.csv', 'r', encoding='utf-8') as f:
            standard_header = f.readline().strip()
        standard_labels = standard_header.count('Labels')
        
        # Analyser fichier extrait
        with open('test_extracted_enriched.csv', 'r', encoding='utf-8') as f:
            extracted_header = f.readline().strip()
        extracted_labels = extracted_header.count('Labels')
        
        print(f"   Fichier standard : {standard_labels} colonnes Labels")
        print(f"   Fichier extrait : {extracted_labels} colonnes Labels")
        
        # Vérifier la cohérence
        coherent = standard_labels == extracted_labels and standard_labels >= 6
        
        print(f"\n✅ Formats cohérents : {'OUI' if coherent else 'NON'}")
        print(f"✅ Même nombre de colonnes Labels : {'OUI' if standard_labels == extracted_labels else 'NON'}")
        
        if coherent:
            print(f"\n🎉 FORMATS PARFAITEMENT COHÉRENTS !")
            print(f"📊 Les deux types de fichiers génèrent le même format")
            print(f"   {standard_labels} colonnes 'Labels' dans les deux cas")
        
        # Nettoyer
        os.remove('test_standard_enriched.csv')
        os.remove('test_extracted_enriched.csv')
        
        success = coherent
        
    except Exception as e:
        print(f"❌ Erreur lors de la comparaison : {e}")
        success = False
    
    # Nettoyer
    os.remove('test_standard.csv')
    os.remove('test_extracted.csv')
    
    return success

def main():
    """
    Test complet de la détection automatique
    """
    print("🧪 VALIDATION DÉTECTION AUTOMATIQUE FORMAT")
    
    # Test 1 : Détection automatique
    test1 = test_detection_format_automatique()
    
    # Test 2 : Comparaison formats
    test2 = test_comparaison_formats()
    
    print("\n" + "="*70)
    print("📊 RÉSULTATS FINAUX")
    print("="*70)
    
    print(f"✅ Détection automatique format : {'OK' if test1 else 'ÉCHEC'}")
    print(f"✅ Cohérence des formats : {'OK' if test2 else 'ÉCHEC'}")
    
    if test1 and test2:
        print(f"\n🎉 DÉTECTION AUTOMATIQUE PARFAITE !")
        print(f"✅ Fichiers avec Action/Data/Expected Result → Format 'separate' automatique")
        print(f"✅ Même nombre de colonnes Labels que l'enrichissement direct")
        print(f"✅ Format cohérent : Labels,Labels,Labels,Labels,Labels,Labels")
        print(f"✅ Votre problème d'en-tête différent est résolu !")
        print(f"🎯 Maintenant tous les enrichissements utilisent le même format !")
    else:
        print(f"\n⚠️  Des ajustements sont encore nécessaires")

if __name__ == "__main__":
    main()
