#!/usr/bin/env python3
"""
Version simplifiée de l'application pour test
"""

from flask import Flask, render_template_string

app = Flask(__name__)

@app.route('/')
def index():
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <title>Test Application Flask</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3>🎉 Application Flask Fonctionnelle !</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h4>✅ Test Réussi</h4>
                            <p>L'application Flask fonctionne correctement sur votre système.</p>
                        </div>
                        
                        <h5>📋 Prochaines étapes :</h5>
                        <ol>
                            <li>Arrêtez cette application (Ctrl+C)</li>
                            <li>Lancez l'application complète avec : <code>python run_app.py</code></li>
                            <li>Accédez à l'interface d'enrichissement</li>
                        </ol>
                        
                        <div class="mt-4">
                            <a href="/test" class="btn btn-primary">Tester une autre route</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
    ''')

@app.route('/test')
def test():
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <title>Test Route</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="alert alert-info">
            <h4>🔧 Test de Route</h4>
            <p>Cette route fonctionne également !</p>
            <a href="/" class="btn btn-secondary">Retour à l'accueil</a>
        </div>
    </div>
</body>
</html>
    ''')

if __name__ == '__main__':
    print("🚀 Lancement de l'application de test...")
    print("📱 Accédez à: http://127.0.0.1:5000")
    print("🛑 Arrêt avec Ctrl+C")
    app.run(debug=True, host='127.0.0.1', port=5000)
