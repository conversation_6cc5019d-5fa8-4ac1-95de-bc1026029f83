#!/usr/bin/env python3
"""
Processeur asynchrone pour l'enrichissement de gros fichiers
"""

import threading
import time
import json
import os
from test_enrichment_tool import TestEnrichmentTool
from create_excel_output import create_excel_output

class AsyncProcessor:
    def __init__(self):
        self.jobs = {}  # Stockage des tâches en cours
        
    def start_enrichment(self, file_id, input_file, output_file, format_type='standard'):
        """
        Démarre un enrichissement en arrière-plan
        """
        job_data = {
            'status': 'starting',
            'progress': 0,
            'current_row': 0,
            'total_rows': 0,
            'message': 'Initialisation...',
            'start_time': time.time(),
            'error': None,
            'format': format_type
        }

        self.jobs[file_id] = job_data

        # Lancer le traitement dans un thread séparé
        thread = threading.Thread(
            target=self._process_file,
            args=(file_id, input_file, output_file, format_type)
        )
        thread.daemon = True
        thread.start()

        return file_id
    
    def _process_file(self, file_id, input_file, output_file, format_type='standard'):
        """
        Traite le fichier en arrière-plan
        """
        try:
            # Callback pour mettre à jour le progrès
            def progress_callback(progress, current_row, total_rows):
                if file_id in self.jobs:
                    self.jobs[file_id].update({
                        'status': 'processing',
                        'progress': progress,
                        'current_row': current_row,
                        'total_rows': total_rows,
                        'message': f'Traitement en cours... {current_row}/{total_rows} tests'
                    })

            # Mettre à jour le statut
            self.jobs[file_id]['status'] = 'processing'
            self.jobs[file_id]['message'] = f'Lecture du fichier (format {format_type})...'

            # Enrichissement avec la version améliorée (correction intégrée)
            from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced
            tool = TestEnrichmentToolEnhanced()
            tool.enrich_csv_enhanced(input_file, output_file, format_type, progress_callback)

            # Création du fichier Excel
            self.jobs[file_id]['message'] = 'Création du fichier Excel...'
            self.jobs[file_id]['progress'] = 95

            excel_file = output_file.replace('.csv', '.xlsx')
            create_excel_output(output_file, excel_file)

            # Terminé avec succès
            self.jobs[file_id].update({
                'status': 'completed',
                'progress': 100,
                'message': f'Enrichissement terminé avec succès (format {format_type})',
                'end_time': time.time(),
                'csv_file': os.path.basename(output_file),
                'excel_file': os.path.basename(excel_file),
                'format': format_type
            })

        except Exception as e:
            # Erreur
            self.jobs[file_id].update({
                'status': 'error',
                'progress': -1,
                'message': f'Erreur: {str(e)}',
                'error': str(e),
                'end_time': time.time()
            })
    
    def get_job_status(self, file_id):
        """
        Récupère le statut d'une tâche
        """
        return self.jobs.get(file_id, None)
    
    def cleanup_old_jobs(self, max_age_hours=24):
        """
        Nettoie les anciennes tâches
        """
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        jobs_to_remove = []
        for job_id, job_data in self.jobs.items():
            if 'end_time' in job_data:
                age = current_time - job_data['end_time']
                if age > max_age_seconds:
                    jobs_to_remove.append(job_id)
        
        for job_id in jobs_to_remove:
            del self.jobs[job_id]
        
        return len(jobs_to_remove)

# Instance globale du processeur
async_processor = AsyncProcessor()

class ProgressTracker:
    """
    Classe pour suivre le progrès d'un enrichissement
    """
    def __init__(self, file_id):
        self.file_id = file_id
        self.start_time = time.time()
        self.last_update = time.time()
        
    def update(self, progress, current_row, total_rows, message=""):
        """
        Met à jour le progrès
        """
        current_time = time.time()
        elapsed = current_time - self.start_time
        
        # Estimation du temps restant
        if progress > 0:
            estimated_total = elapsed * 100 / progress
            eta = estimated_total - elapsed
        else:
            eta = 0
        
        # Vitesse de traitement
        if current_time - self.last_update > 0:
            speed = (current_row / elapsed) if elapsed > 0 else 0
        else:
            speed = 0
        
        # Mettre à jour les données
        if self.file_id in async_processor.jobs:
            async_processor.jobs[self.file_id].update({
                'progress': progress,
                'current_row': current_row,
                'total_rows': total_rows,
                'message': message or f'Traitement... {current_row}/{total_rows}',
                'elapsed_time': elapsed,
                'eta': eta,
                'speed': speed
            })
        
        self.last_update = current_time

def estimate_processing_time(file_size_mb):
    """
    Estime le temps de traitement basé sur la taille du fichier
    """
    # Estimation basée sur des tests empiriques
    # Environ 1000 tests par seconde sur une machine moyenne
    base_time_per_mb = 2  # secondes par MB
    return file_size_mb * base_time_per_mb

def get_file_complexity_score(df):
    """
    Calcule un score de complexité basé sur le contenu du fichier
    """
    score = 0
    
    # Plus de colonnes = plus complexe
    score += len(df.columns) * 0.1
    
    # Contenu des descriptions
    if 'Description' in df.columns:
        avg_desc_length = df['Description'].astype(str).str.len().mean()
        score += avg_desc_length / 100
    
    # Nombre de lignes
    score += len(df) / 1000
    
    return min(score, 10)  # Score maximum de 10

def optimize_processing_strategy(file_path):
    """
    Détermine la meilleure stratégie de traitement
    """
    import pandas as pd
    
    # Analyser le fichier
    file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
    
    # Lire un échantillon pour analyser la complexité
    sample_df = pd.read_csv(file_path, nrows=100)
    complexity = get_file_complexity_score(sample_df)
    
    # Déterminer la stratégie
    if file_size > 10 or complexity > 5:
        return {
            'strategy': 'chunked',
            'chunk_size': 500,
            'estimated_time': estimate_processing_time(file_size),
            'complexity': complexity
        }
    else:
        return {
            'strategy': 'standard',
            'chunk_size': None,
            'estimated_time': estimate_processing_time(file_size),
            'complexity': complexity
        }

if __name__ == "__main__":
    # Test du processeur asynchrone
    print("Test du processeur asynchrone...")
    
    processor = AsyncProcessor()
    
    # Simuler une tâche
    job_id = "test_job"
    processor.jobs[job_id] = {
        'status': 'processing',
        'progress': 50,
        'message': 'Test en cours...'
    }
    
    print(f"Statut de la tâche: {processor.get_job_status(job_id)}")
    
    # Nettoyer
    cleaned = processor.cleanup_old_jobs()
    print(f"Tâches nettoyées: {cleaned}")
