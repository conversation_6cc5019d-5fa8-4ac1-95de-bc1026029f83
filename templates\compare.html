{% extends "base.html" %}

{% block title %}Comparaison - Enrichissement Tests Jira Xray{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-exchange-alt"></i> Comparaison Avant/Après Enrichissement
                </h3>
            </div>
            <div class="card-body">
                
                <!-- Résumé de la comparaison -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card border-secondary">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-import"></i> Fichier Original
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <h4 class="text-secondary">{{ data.original.rows }}</h4>
                                        <p class="card-text">Tests</p>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-secondary">{{ data.original.columns }}</h4>
                                        <p class="card-text">Colonnes</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-export"></i> Fichier Enrichi
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <h4 class="text-success">{{ data.enriched.rows }}</h4>
                                        <p class="card-text">Tests</p>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-success">{{ data.enriched.columns }}</h4>
                                        <p class="card-text">Colonnes</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Colonnes ajoutées -->
                {% if data.added_columns %}
                <div class="alert alert-success">
                    <h5><i class="fas fa-plus-circle"></i> Colonnes Ajoutées</h5>
                    <div class="d-flex flex-wrap gap-2">
                        {% for column in data.added_columns %}
                            <span class="badge bg-success">{{ column }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Onglets de comparaison -->
                <ul class="nav nav-tabs" id="comparisonTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="columns-tab" data-bs-toggle="tab" data-bs-target="#columns" type="button" role="tab">
                            <i class="fas fa-columns"></i> Colonnes
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="sample-tab" data-bs-toggle="tab" data-bs-target="#sample" type="button" role="tab">
                            <i class="fas fa-table"></i> Échantillon
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis" type="button" role="tab">
                            <i class="fas fa-chart-pie"></i> Analyse
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="comparisonTabsContent">
                    
                    <!-- Onglet Colonnes -->
                    <div class="tab-pane fade show active" id="columns" role="tabpanel">
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6><i class="fas fa-file-import text-secondary"></i> Colonnes Originales</h6>
                                <div class="list-group">
                                    {% for column in data.original.columns_list %}
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            {{ column }}
                                            {% if column in data.enriched.columns_list %}
                                                <span class="badge bg-secondary">Conservée</span>
                                            {% else %}
                                                <span class="badge bg-warning">Supprimée</span>
                                            {% endif %}
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-file-export text-success"></i> Colonnes Enrichies</h6>
                                <div class="list-group">
                                    {% for column in data.enriched.columns_list %}
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            {{ column }}
                                            {% if column in data.original.columns_list %}
                                                <span class="badge bg-secondary">Originale</span>
                                            {% else %}
                                                <span class="badge bg-success">Nouvelle</span>
                                            {% endif %}
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Onglet Échantillon -->
                    <div class="tab-pane fade" id="sample" role="tabpanel">
                        <div class="mt-3">
                            <h6><i class="fas fa-eye"></i> Aperçu des 5 Premiers Tests</h6>
                            
                            <!-- Fichier Original -->
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <strong>Fichier Original</strong>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    {% for column in data.original.columns_list[:5] %}
                                                        <th>{{ column }}</th>
                                                    {% endfor %}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for row in data.original.sample %}
                                                    <tr>
                                                        {% for column in data.original.columns_list[:5] %}
                                                            <td>
                                                                {% set value = row[column] if row[column] else '' %}
                                                                {% if value|length > 50 %}
                                                                    {{ value[:50] }}...
                                                                {% else %}
                                                                    {{ value }}
                                                                {% endif %}
                                                            </td>
                                                        {% endfor %}
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Fichier Enrichi -->
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <strong>Fichier Enrichi</strong>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    {% for column in ['Summary', 'Component/s', 'Test Set', 'Labels'] %}
                                                        {% if column in data.enriched.columns_list %}
                                                            <th>{{ column }}</th>
                                                        {% endif %}
                                                    {% endfor %}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for row in data.enriched.sample %}
                                                    <tr>
                                                        {% for column in ['Summary', 'Component/s', 'Test Set', 'Labels'] %}
                                                            {% if column in data.enriched.columns_list %}
                                                                <td>
                                                                    {% set value = row[column] if row[column] else '' %}
                                                                    {% if column == 'Labels' %}
                                                                        {% for label in value.split(', ') %}
                                                                            <span class="badge bg-primary me-1">{{ label }}</span>
                                                                        {% endfor %}
                                                                    {% elif value|length > 30 %}
                                                                        {{ value[:30] }}...
                                                                    {% else %}
                                                                        {{ value }}
                                                                    {% endif %}
                                                                </td>
                                                            {% endif %}
                                                        {% endfor %}
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Onglet Analyse -->
                    <div class="tab-pane fade" id="analysis" role="tabpanel">
                        <div class="mt-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-chart-bar"></i> Améliorations Apportées</h6>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-unstyled">
                                                <li class="mb-2">
                                                    <i class="fas fa-plus text-success"></i>
                                                    <strong>{{ data.added_columns|length }}</strong> nouvelle(s) colonne(s) ajoutée(s)
                                                </li>
                                                <li class="mb-2">
                                                    <i class="fas fa-tags text-primary"></i>
                                                    Labels ISTQB appliqués automatiquement
                                                </li>
                                                <li class="mb-2">
                                                    <i class="fas fa-sitemap text-info"></i>
                                                    Composants techniques identifiés
                                                </li>
                                                <li class="mb-2">
                                                    <i class="fas fa-layer-group text-warning"></i>
                                                    Tests organisés en Test Sets
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-download"></i> Fichiers Disponibles</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-grid gap-2">
                                                <a href="{{ url_for('download_file', filename='enriched_' + data.file_id + '.csv') }}" 
                                                   class="btn btn-success">
                                                    <i class="fas fa-file-csv"></i> Télécharger CSV Enrichi
                                                </a>
                                                <a href="{{ url_for('download_file', filename='enriched_' + data.file_id + '.xlsx') }}" 
                                                   class="btn btn-primary">
                                                    <i class="fas fa-file-excel"></i> Télécharger Excel Complet
                                                </a>
                                                <a href="{{ url_for('preview_file', file_id=data.file_id) }}" 
                                                   class="btn btn-outline-info">
                                                    <i class="fas fa-eye"></i> Aperçu Détaillé
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="text-center mt-4">
                    <a href="{{ url_for('show_history') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-history"></i> Retour à l'Historique
                    </a>
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Nouveau Fichier
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
