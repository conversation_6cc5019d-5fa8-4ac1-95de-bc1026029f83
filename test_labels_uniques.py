#!/usr/bin/env python3
"""
Test de la répartition unique des labels dans les colonnes séparées
"""

import pandas as pd
import os
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def print_header(title):
    print("\n" + "="*70)
    print(f"  {title}")
    print("="*70)

def test_labels_uniques_repartition():
    """
    Test que chaque label est unique dans chaque colonne Labels
    """
    print_header("🏷️ TEST LABELS UNIQUES DANS COLONNES SÉPARÉES")
    
    # Créer un fichier avec des tests ayant différents nombres de labels
    data = {
        'Summary': [
            'Test avec 2 labels',
            'Test avec 4 labels', 
            'Test avec 6 labels',
            'Test sans labels'
        ],
        'Component/s': ['HLR', 'HSS', 'MME', ''],
        'Action': ['Action 1', 'Action 2', 'Action 3', 'Action 4'],
        'Data': ['Data 1', 'Data 2', 'Data 3', 'Data 4'],
        'Expected Result': ['Result 1', 'Result 2', 'Result 3', 'Result 4']
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_labels_uniques.csv', index=False)
    
    print(f"📁 Fichier créé avec {len(data['Summary'])} tests")
    print(f"   Test 1: HLR → devrait avoir System, Manual, Regression")
    print(f"   Test 2: HSS → devrait avoir System, Manual, 4G, Diameter, S6a")
    print(f"   Test 3: MME → devrait avoir System, Manual, 4G, LTE, EPC, MME")
    print(f"   Test 4: Vide → devrait avoir COMMON")
    
    # Test d'enrichissement avec format 'separate'
    try:
        tool = TestEnrichmentToolEnhanced()
        tool.enrich_csv_enhanced('test_labels_uniques.csv', 'test_labels_uniques_enriched.csv', 'separate')
        
        print("✅ Enrichissement terminé")
        
        # Analyser le fichier enrichi
        if os.path.exists('test_labels_uniques_enriched.csv'):
            with open('test_labels_uniques_enriched.csv', 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if lines:
                header_line = lines[0].strip()
                header_parts = header_line.split(',')
                
                print(f"\n📋 Analyse du fichier enrichi :")
                print(f"   Lignes totales : {len(lines)}")
                print(f"   Colonnes totales : {len(header_parts)}")
                
                # Compter les colonnes Labels
                labels_columns_count = header_parts.count('Labels')
                print(f"   Colonnes 'Labels' : {labels_columns_count}")
                
                # Afficher l'en-tête
                print(f"\n📋 En-tête du fichier :")
                labels_positions = []
                for i, col in enumerate(header_parts):
                    if col == 'Labels':
                        labels_positions.append(i)
                        print(f"   {i+1}. {col} ← Position {i}")
                    elif i < 10:  # Afficher les 10 premières colonnes non-Labels
                        print(f"   {i+1}. {col}")
                
                print(f"   Positions des colonnes Labels : {labels_positions}")
                
                # Analyser les données de chaque test
                print(f"\n📋 Analyse des labels par test :")
                for i, line in enumerate(lines[1:], 1):
                    if line.strip():
                        data_parts = line.strip().split(',')
                        
                        # Extraire les labels de ce test
                        test_labels = []
                        for pos in labels_positions:
                            if pos < len(data_parts):
                                label = data_parts[pos].strip()
                                if label:  # Ignorer les labels vides
                                    test_labels.append(label)
                        
                        summary = data_parts[0] if len(data_parts) > 0 else f"Test {i}"
                        print(f"   Test {i} ({summary[:30]}...): {test_labels}")
                
                # Vérifier que les labels sont uniques par ligne
                success = True
                for i, line in enumerate(lines[1:], 1):
                    if line.strip():
                        data_parts = line.strip().split(',')
                        test_labels = []
                        for pos in labels_positions:
                            if pos < len(data_parts):
                                label = data_parts[pos].strip()
                                if label:
                                    test_labels.append(label)
                        
                        # Vérifier l'unicité
                        unique_labels = list(set(test_labels))
                        if len(test_labels) != len(unique_labels):
                            print(f"❌ Test {i}: Labels dupliqués détectés")
                            success = False
                        else:
                            print(f"✅ Test {i}: Labels uniques ({len(test_labels)} labels)")
                
                print(f"\n🎯 RÉSULTAT FINAL :")
                print(f"✅ Colonnes Labels créées : {labels_columns_count}")
                print(f"✅ Labels uniques par test : {'OUI' if success else 'NON'}")
                print(f"✅ Format attendu respecté : {'OUI' if labels_columns_count >= 4 and success else 'NON'}")
                
                if success and labels_columns_count >= 4:
                    print(f"\n🎉 LABELS UNIQUES PARFAITEMENT RÉPARTIS !")
                    print(f"📊 Résumé :")
                    print(f"   {labels_columns_count} colonnes 'Labels' créées")
                    print(f"   Chaque label dans sa propre colonne")
                    print(f"   Colonnes vides pour les tests avec moins de labels")
                    print(f"   Format: Labels,Labels,Labels,Labels,...")
                else:
                    print(f"\n⚠️  Des ajustements sont nécessaires")
            else:
                print("❌ Fichier enrichi vide")
                success = False
            
            # Nettoyer
            os.remove('test_labels_uniques_enriched.csv')
        else:
            print("❌ Fichier enrichi non généré")
            success = False
    
    except Exception as e:
        print(f"❌ Erreur lors de l'enrichissement : {e}")
        import traceback
        print(traceback.format_exc())
        success = False
    
    # Nettoyer
    os.remove('test_labels_uniques.csv')
    
    return success

def test_exemple_format_attendu():
    """
    Test avec l'exemple exact que vous avez donné
    """
    print_header("📋 TEST AVEC EXEMPLE FORMAT ATTENDU")
    
    # Créer un fichier avec l'exemple exact
    data = {
        'Summary': ['Test System Manual Regression', 'Test System Manual Regression 2G'],
        'Component/s': ['HLR', 'HLR'],
        'Action': ['Action test 1', 'Action test 2'],
        'Data': ['Data test 1', 'Data test 2'],
        'Expected Result': ['Result test 1', 'Result test 2']
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_exemple_format.csv', index=False)
    
    print(f"📁 Fichier créé pour reproduire votre exemple")
    print(f"   Test 1: HLR → System, Manual, Regression")
    print(f"   Test 2: HLR → System, Manual, Regression, 2G")
    print(f"   Format attendu:")
    print(f"     Labels,Labels,Labels,Labels,Labels,Labels")
    print(f"     System,Manual,Regression,,,")
    print(f"     System,Manual,Regression,2G,,")
    
    # Test d'enrichissement
    try:
        tool = TestEnrichmentToolEnhanced()
        tool.enrich_csv_enhanced('test_exemple_format.csv', 'test_exemple_enriched.csv', 'separate')
        
        print("✅ Enrichissement terminé")
        
        # Vérifier le résultat
        if os.path.exists('test_exemple_enriched.csv'):
            with open('test_exemple_enriched.csv', 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 Contenu du fichier enrichi :")
            lines = content.strip().split('\n')
            for i, line in enumerate(lines[:5]):  # Afficher les 5 premières lignes
                print(f"   {i+1}. {line}")
            
            # Vérifier le format
            if lines:
                header = lines[0]
                labels_count = header.count('Labels')
                
                print(f"\n📊 Vérification du format :")
                print(f"   Colonnes 'Labels' : {labels_count}")
                
                if len(lines) >= 3:
                    # Analyser les lignes de données
                    data_line_1 = lines[1].split(',')
                    data_line_2 = lines[2].split(',')
                    
                    # Trouver les positions des colonnes Labels
                    header_parts = header.split(',')
                    labels_positions = [i for i, col in enumerate(header_parts) if col == 'Labels']
                    
                    print(f"   Positions Labels : {labels_positions}")
                    
                    # Extraire les labels de chaque ligne
                    labels_line_1 = [data_line_1[pos] if pos < len(data_line_1) else '' for pos in labels_positions]
                    labels_line_2 = [data_line_2[pos] if pos < len(data_line_2) else '' for pos in labels_positions]
                    
                    print(f"   Ligne 1 Labels : {labels_line_1}")
                    print(f"   Ligne 2 Labels : {labels_line_2}")
                    
                    # Vérifier que c'est conforme à l'exemple
                    expected_line_1 = ['System', 'Manual', 'Regression', '', '', ''][:labels_count]
                    expected_line_2 = ['System', 'Manual', 'Regression', '2G', '', ''][:labels_count]
                    
                    success = (labels_line_1[:3] == expected_line_1[:3] and 
                              labels_line_2[:4] == expected_line_2[:4])
                    
                    print(f"✅ Format conforme à l'exemple : {'OUI' if success else 'NON'}")
                else:
                    success = False
            else:
                success = False
            
            # Nettoyer
            os.remove('test_exemple_enriched.csv')
        else:
            success = False
    
    except Exception as e:
        print(f"❌ Erreur : {e}")
        success = False
    
    # Nettoyer
    os.remove('test_exemple_format.csv')
    
    return success

def main():
    """
    Test complet des labels uniques
    """
    print("🧪 TEST COMPLET LABELS UNIQUES DANS COLONNES SÉPARÉES")
    
    # Test 1 : Répartition unique
    test1 = test_labels_uniques_repartition()
    
    # Test 2 : Exemple format attendu
    test2 = test_exemple_format_attendu()
    
    print_header("📊 RÉSULTATS FINAUX")
    
    print(f"✅ Labels uniques répartition : {'OK' if test1 else 'ÉCHEC'}")
    print(f"✅ Exemple format attendu : {'OK' if test2 else 'ÉCHEC'}")
    
    if test1 and test2:
        print(f"\n🎉 LABELS UNIQUES PARFAITEMENT IMPLÉMENTÉS !")
        print(f"✅ Chaque label dans sa propre colonne 'Labels'")
        print(f"✅ Nombre de colonnes adapté au maximum de labels")
        print(f"✅ Colonnes vides pour les tests avec moins de labels")
        print(f"✅ Format exactement comme demandé")
        print(f"🎯 Votre demande de labels uniques est satisfaite !")
    else:
        print(f"\n⚠️  Des ajustements sont encore nécessaires")

if __name__ == "__main__":
    main()
