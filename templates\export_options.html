{% extends "base.html" %}

{% block title %}Options d'Export - Enrichissement Tests Jira Xray{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-download"></i> Options d'Export Avancées
                </h3>
            </div>
            <div class="card-body">
                
                <!-- Informations sur le fichier -->
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> Fichier à Exporter</h5>
                    <p><strong>ID:</strong> {{ file_id }}</p>
                    <p><strong>Tests enrichis:</strong> <span id="test-count">-</span></p>
                    <p><strong>Composants:</strong> <span id="component-count">-</span></p>
                </div>

                <!-- Formats d'export -->
                <div class="row">
                    
                    <!-- Formats Standards -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-file"></i> Formats Standards
                                </h5>
                            </div>
                            <div class="card-body">
                                
                                <!-- CSV -->
                                <div class="d-grid gap-2 mb-3">
                                    <a href="{{ url_for('download_file', filename='enriched_' + file_id + '.csv') }}" 
                                       class="btn btn-outline-success">
                                        <i class="fas fa-file-csv"></i> CSV Standard
                                        <small class="d-block text-muted">Format par défaut, compatible avec tous les outils</small>
                                    </a>
                                </div>

                                <!-- Excel supprimé - CSV uniquement -->

                                <!-- TSV -->
                                <div class="d-grid gap-2 mb-3">
                                    <a href="{{ url_for('export_file', file_id=file_id, format_type='tsv') }}" 
                                       class="btn btn-outline-secondary">
                                        <i class="fas fa-file-alt"></i> TSV (Tab-Separated)
                                        <small class="d-block text-muted">Séparateur tabulation, compatible Unix</small>
                                    </a>
                                </div>

                            </div>
                        </div>
                    </div>

                    <!-- Formats Spécialisés -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-cogs"></i> Formats Spécialisés
                                </h5>
                            </div>
                            <div class="card-body">
                                
                                <!-- Jira Import -->
                                <div class="d-grid gap-2 mb-3">
                                    <a href="{{ url_for('export_jira_format', file_id=file_id) }}" 
                                       class="btn btn-success">
                                        <i class="fas fa-upload"></i> Format Jira Xray
                                        <small class="d-block text-muted">Optimisé pour l'import direct dans Jira</small>
                                    </a>
                                </div>

                                <!-- JSON -->
                                <div class="d-grid gap-2 mb-3">
                                    <a href="{{ url_for('export_file', file_id=file_id, format_type='json') }}" 
                                       class="btn btn-outline-warning">
                                        <i class="fas fa-code"></i> JSON Structuré
                                        <small class="d-block text-muted">Groupé par composants, pour APIs</small>
                                    </a>
                                </div>

                                <!-- XML -->
                                <div class="d-grid gap-2 mb-3">
                                    <a href="{{ url_for('export_file', file_id=file_id, format_type='xml') }}" 
                                       class="btn btn-outline-info">
                                        <i class="fas fa-file-code"></i> XML Hiérarchique
                                        <small class="d-block text-muted">Structure XML avec métadonnées</small>
                                    </a>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rapports et Analyses -->
                <div class="card mt-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line"></i> Rapports et Analyses
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            
                            <div class="col-md-4">
                                <div class="d-grid">
                                    <a href="{{ url_for('generate_comparison_report', file_id=file_id) }}" 
                                       class="btn btn-warning">
                                        <i class="fas fa-exchange-alt"></i> Rapport de Comparaison
                                        <small class="d-block text-muted">Analyse avant/après enrichissement</small>
                                    </a>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="d-grid">
                                    <a href="{{ url_for('compare_results', file_id=file_id) }}" 
                                       class="btn btn-outline-warning">
                                        <i class="fas fa-eye"></i> Comparaison Visuelle
                                        <small class="d-block text-muted">Interface de comparaison interactive</small>
                                    </a>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="d-grid">
                                    <button onclick="generateCustomReport()" class="btn btn-outline-warning">
                                        <i class="fas fa-file-pdf"></i> Rapport Personnalisé
                                        <small class="d-block text-muted">Rapport PDF avec graphiques</small>
                                    </button>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- Options de personnalisation -->
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-sliders-h"></i> Options de Personnalisation
                        </h5>
                    </div>
                    <div class="card-body">
                        
                        <form id="custom-export-form">
                            <div class="row">
                                
                                <div class="col-md-6">
                                    <h6>Filtres</h6>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Composants à inclure :</label>
                                        <div id="component-filters">
                                            <!-- Sera rempli dynamiquement -->
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Type de tests :</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="include-functional" checked>
                                            <label class="form-check-label" for="include-functional">
                                                Tests Fonctionnels
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="include-nonfunctional" checked>
                                            <label class="form-check-label" for="include-nonfunctional">
                                                Tests Non-Fonctionnels
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h6>Format de sortie</h6>
                                    
                                    <div class="mb-3">
                                        <label for="export-format" class="form-label">Format :</label>
                                        <select class="form-select" id="export-format">
                                            <option value="csv">CSV</option>
                                            <option value="json">JSON</option>
                                            <option value="xml">XML</option>
                                            <option value="tsv">TSV</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="encoding" class="form-label">Encodage :</label>
                                        <select class="form-select" id="encoding">
                                            <option value="utf-8">UTF-8</option>
                                            <option value="iso-8859-1">ISO-8859-1</option>
                                            <option value="windows-1252">Windows-1252</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="include-stats">
                                            <label class="form-check-label" for="include-stats">
                                                Inclure les statistiques
                                            </label>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="text-center mt-3">
                                <button type="button" onclick="generateCustomExport()" class="btn btn-primary btn-lg">
                                    <i class="fas fa-download"></i> Générer Export Personnalisé
                                </button>
                            </div>
                        </form>

                    </div>
                </div>

                <!-- Actions -->
                <div class="text-center mt-4">
                    <a href="{{ url_for('show_history') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-history"></i> Retour à l'Historique
                    </a>
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Nouveau Fichier
                    </a>
                </div>

            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
const fileId = '{{ file_id }}';

// Charger les informations du fichier
async function loadFileInfo() {
    try {
        const response = await fetch(`/preview/${fileId}`);
        const data = await response.json();
        
        if (data.success) {
            document.getElementById('test-count').textContent = data.stats.total_tests;
            document.getElementById('component-count').textContent = Object.keys(data.stats.components).length;
            
            // Remplir les filtres de composants
            const componentFilters = document.getElementById('component-filters');
            Object.keys(data.stats.components).forEach(component => {
                const div = document.createElement('div');
                div.className = 'form-check';
                div.innerHTML = `
                    <input class="form-check-input" type="checkbox" id="component-${component}" checked>
                    <label class="form-check-label" for="component-${component}">
                        ${component} (${data.stats.components[component]} tests)
                    </label>
                `;
                componentFilters.appendChild(div);
            });
        }
    } catch (error) {
        console.error('Erreur lors du chargement des informations:', error);
    }
}

// Générer un export personnalisé
function generateCustomExport() {
    const format = document.getElementById('export-format').value;
    const encoding = document.getElementById('encoding').value;
    const includeStats = document.getElementById('include-stats').checked;
    
    // Collecter les filtres
    const filters = {
        components: [],
        includeFunctional: document.getElementById('include-functional').checked,
        includeNonFunctional: document.getElementById('include-nonfunctional').checked
    };
    
    // Composants sélectionnés
    document.querySelectorAll('#component-filters input[type="checkbox"]:checked').forEach(checkbox => {
        const component = checkbox.id.replace('component-', '');
        filters.components.push(component);
    });
    
    // Construire l'URL avec les paramètres
    const params = new URLSearchParams({
        format: format,
        encoding: encoding,
        include_stats: includeStats,
        filters: JSON.stringify(filters)
    });
    
    // Télécharger
    window.location.href = `/export_custom/${fileId}?${params.toString()}`;
}

// Générer un rapport personnalisé
function generateCustomReport() {
    alert('Fonctionnalité en développement - Rapport PDF avec graphiques');
}

// Charger les informations au chargement de la page
document.addEventListener('DOMContentLoaded', loadFileInfo);
</script>
{% endblock %}
