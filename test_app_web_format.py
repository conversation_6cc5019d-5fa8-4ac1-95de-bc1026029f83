#!/usr/bin/env python3
"""
Test pour vérifier que l'application web utilise la version corrigée
"""

import requests
import os
import time

def test_app_web_format():
    """
    Test de l'application web pour vérifier le format
    """
    print("🧪 TEST APPLICATION WEB - FORMAT LABELS")
    print("="*60)
    
    try:
        # Créer un fichier test simple
        csv_content = '''Summary,Issue key,Component/s,Action,Data,Expected Result
S6a ULR,TEST-001,EPC-HSS,Send ULR,IMSI=123,HSS responds
VoLTE Call,TEST-002,IMS-HSS,Setup call,,Call established'''
        
        with open('test_app_web.csv', 'w', encoding='utf-8') as f:
            f.write(csv_content)
        
        print("📁 Fichier test créé")
        
        # Test de l'upload (simulation)
        url_base = 'http://127.0.0.1:5000'
        
        # Vérifier que l'app est accessible
        try:
            response = requests.get(url_base, timeout=5)
            if response.status_code == 200:
                print("✅ Application web accessible")
            else:
                print(f"⚠️ Application web répond avec code {response.status_code}")
        except requests.exceptions.ConnectionError:
            print("❌ Application web non accessible")
            print("💡 Assurez-vous que l'application tourne sur http://127.0.0.1:5000")
            return False
        except Exception as e:
            print(f"❌ Erreur de connexion: {e}")
            return False
        
        print("\n📋 INSTRUCTIONS POUR TESTER:")
        print("1. Allez sur http://127.0.0.1:5000")
        print("2. Uploadez le fichier 'test_app_web.csv'")
        print("3. Faites l'enrichissement")
        print("4. Téléchargez le fichier enrichi")
        print("5. Vérifiez l'en-tête - vous devriez avoir:")
        print("   Labels,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Labels")
        print("   (au lieu d'une seule colonne 'Labels')")
        
        print(f"\n🎯 RÉSULTAT ATTENDU:")
        print(f"   9 colonnes 'Labels' identiques")
        print(f"   Labels uniques: Functional,System,Manual,Regression,4G,Diameter,S6a,LocationUpdate,3GPP_TS_29.272")
        print(f"   Format: Labels,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Labels")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

def main():
    """
    Test principal
    """
    print("🧪 VÉRIFICATION APPLICATION WEB")
    
    success = test_app_web_format()
    
    if success:
        print(f"\n✅ Test préparé avec succès")
        print(f"🌐 Testez maintenant dans votre navigateur")
        print(f"📱 URL: http://127.0.0.1:5000")
    else:
        print(f"\n❌ Problème lors de la préparation du test")

if __name__ == "__main__":
    main()
