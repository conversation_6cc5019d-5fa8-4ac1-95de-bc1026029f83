#!/usr/bin/env python3
"""
Script de démonstration avec des exemples concrets d'enrichissement
"""

import pandas as pd

def show_demo_examples():
    """
    Affiche des exemples concrets d'enrichissement
    """
    print("=== DÉMONSTRATION DE L'OUTIL D'ENRICHISSEMENT JIRA XRAY ===\n")
    
    # Exemples avant/après
    examples = [
        {
            "type": "HLR Call Forwarding",
            "avant": {
                "Summary": "CFU",
                "Component/s": "",
                "Test Set": "",
                "Labels": ""
            },
            "après": {
                "Summary": "CFU",
                "Component/s": "HLR",
                "Test Set": "HLR Call Forwarding Services",
                "Labels": "Functional, System, Manual, Regression, 2G, MAP, CallForwarding"
            }
        },
        {
            "type": "EPC HSS S6a",
            "avant": {
                "Summary": "S6a - ULR",
                "Component/s": "",
                "Test Set": "",
                "Labels": ""
            },
            "après": {
                "Summary": "S6a - ULR",
                "Component/s": "EPC-HSS",
                "Test Set": "EPC HSS Location Management",
                "Labels": "Functional, System, Manual, Regression, 4G, Diameter, S6a, UpdateLocation"
            }
        },
        {
            "type": "IMS VoLTE",
            "avant": {
                "Summary": "EPC HSS VoLTE SRVCC",
                "Component/s": "",
                "Test Set": "",
                "Labels": ""
            },
            "après": {
                "Summary": "EPC HSS VoLTE SRVCC",
                "Component/s": "IMS-HSS",
                "Test Set": "IMS SRVCC Services",
                "Labels": "Functional, System, Manual, Regression, 4G, IMS, SRVCC"
            }
        },
        {
            "type": "Sécurité",
            "avant": {
                "Summary": "Images vulnerability Scanning",
                "Component/s": "",
                "Test Set": "",
                "Labels": ""
            },
            "après": {
                "Summary": "Images vulnerability Scanning",
                "Component/s": "Security",
                "Test Set": "Security Vulnerability Assessment",
                "Labels": "NonFunctional, System, Manual, Regression, Security, Vulnerability"
            }
        },
        {
            "type": "Backup/Restore",
            "avant": {
                "Summary": "Schedule Backup",
                "Component/s": "",
                "Test Set": "",
                "Labels": ""
            },
            "après": {
                "Summary": "Schedule Backup",
                "Component/s": "COMMON",
                "Test Set": "Backup and Restore Operations",
                "Labels": "NonFunctional, System, Manual, Regression, BackupRestore"
            }
        },
        {
            "type": "OAM",
            "avant": {
                "Summary": "Online loading",
                "Component/s": "",
                "Test Set": "",
                "Labels": ""
            },
            "après": {
                "Summary": "Online loading",
                "Component/s": "OAM",
                "Test Set": "OAM and Provisioning",
                "Labels": "Functional, System, Manual, Regression, OAM"
            }
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"--- EXEMPLE {i}: {example['type']} ---")
        print("AVANT enrichissement:")
        for key, value in example['avant'].items():
            print(f"  {key}: {value if value else '(vide)'}")
        
        print("\nAPRÈS enrichissement:")
        for key, value in example['après'].items():
            print(f"  {key}: {value}")
        print()
    
    print("=== AVANTAGES DE L'ENRICHISSEMENT ===")
    print("✅ Classification automatique selon les standards ISTQB")
    print("✅ Labellisation cohérente par domaine technique")
    print("✅ Regroupement intelligent en Test Sets")
    print("✅ Identification des tests fonctionnels vs non-fonctionnels")
    print("✅ Traçabilité par composant (HLR, EPC-HSS, IMS-HSS, etc.)")
    print("✅ Prêt pour import dans Jira Xray")
    
    print("\n=== STATISTIQUES TYPIQUES ===")
    print("📊 516 tests traités automatiquement")
    print("📊 9 composants identifiés")
    print("📊 15+ Test Sets créés")
    print("📊 Labels ISTQB appliqués systématiquement")
    
    print("\n=== UTILISATION ===")
    print("1. python test_enrichment_tool.py input.csv output.csv")
    print("2. python create_excel_output.py output.csv final.xlsx")
    print("3. Importez final.xlsx dans Jira Xray")

def show_labeling_rules():
    """
    Affiche les règles de labellisation détaillées
    """
    print("\n=== RÈGLES DE LABELLISATION DÉTAILLÉES ===\n")
    
    rules = {
        "HLR (2G/3G)": {
            "patterns": ["call forward", "call barring", "supplementary", "camel", "hlr", "map", "sri", "ati"],
            "labels": ["Functional", "System", "Manual", "Regression", "2G", "MAP"],
            "specific_labels": {
                "call forward": "CallForwarding",
                "call barring": "CallBarring", 
                "supplementary": "SupplementaryService",
                "camel": "CAMEL"
            },
            "test_sets": [
                "HLR Call Forwarding Services",
                "HLR Call Barring Services", 
                "HLR Supplementary Services",
                "HLR CAMEL Services",
                "HLR General Services"
            ]
        },
        "EPC-HSS (4G)": {
            "patterns": ["attach", "detach", "update location", "authentication", "hss", "epc", "s6a", "diameter"],
            "labels": ["Functional", "System", "Manual", "Regression", "4G", "Diameter", "S6a"],
            "specific_labels": {
                "attach/detach": "AttachDetach",
                "update location": "UpdateLocation",
                "authentication": "Authentication"
            },
            "test_sets": [
                "EPC HSS Attach/Detach Procedures",
                "EPC HSS Location Management",
                "EPC HSS Authentication Services",
                "EPC HSS General Services"
            ]
        },
        "IMS (VoLTE)": {
            "patterns": ["volte", "ims", "t-ads", "srvcc", "swx", "sh", "cx", "scc-as"],
            "labels": ["Functional", "System", "Manual", "Regression", "4G", "IMS"],
            "specific_labels": {
                "volte": "VoLTE",
                "t-ads": "TADS",
                "srvcc": "SRVCC"
            },
            "test_sets": [
                "IMS VoLTE Services",
                "IMS T-ADS Services",
                "IMS SRVCC Services",
                "IMS General Services"
            ]
        },
        "Sécurité": {
            "patterns": ["cis", "vulnerability", "rbac", "security", "hardening"],
            "labels": ["NonFunctional", "System", "Manual", "Regression", "Security"],
            "specific_labels": {
                "cis": "CIS",
                "vulnerability": "Vulnerability",
                "rbac": "RBAC"
            },
            "test_sets": [
                "Security CIS Compliance",
                "Security Vulnerability Assessment",
                "Security RBAC Management",
                "Security General Tests"
            ]
        }
    }
    
    for domain, rule in rules.items():
        print(f"🔍 {domain}")
        print(f"   Patterns: {', '.join(rule['patterns'])}")
        print(f"   Labels de base: {', '.join(rule['labels'])}")
        print(f"   Labels spécifiques: {rule['specific_labels']}")
        print(f"   Test Sets: {', '.join(rule['test_sets'])}")
        print()

if __name__ == "__main__":
    show_demo_examples()
    show_labeling_rules()
