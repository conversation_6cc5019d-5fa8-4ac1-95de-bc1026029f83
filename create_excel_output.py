#!/usr/bin/env python3
"""
Script pour créer un fichier Excel avec les résultats d'enrichissement
"""

import pandas as pd
import sys

def create_excel_output(csv_file, excel_file):
    """
    Crée un fichier Excel avec les résultats d'enrichissement
    """
    try:
        # Lecture du CSV enrichi
        df = pd.read_csv(csv_file)
        
        # Création du fichier Excel avec plusieurs feuilles
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            
            # Feuille principale avec tous les tests
            df_main = df[['Summary', 'Component/s', 'Test Set', 'Labels', 'Description', 'Action', 'Expected Result']].copy()
            df_main.to_excel(writer, sheet_name='Tests Enrichis', index=False)
            
            # Feuille avec exemples par composant
            examples_data = []
            
            # Exemples HLR
            hlr_examples = df[df['Component/s'] == 'HLR'].head(5)
            for _, row in hlr_examples.iterrows():
                examples_data.append({
                    'Composant': 'HLR',
                    'Summary': row['Summary'],
                    'Labels': row['Labels'],
                    'Test Set': row['Test Set'],
                    'Explication': 'Tests HLR avec labels 2G, MAP pour les services de base'
                })
            
            # Exemples EPC-HSS
            epc_examples = df[df['Component/s'] == 'EPC-HSS'].head(3)
            for _, row in epc_examples.iterrows():
                examples_data.append({
                    'Composant': 'EPC-HSS',
                    'Summary': row['Summary'],
                    'Labels': row['Labels'],
                    'Test Set': row['Test Set'],
                    'Explication': 'Tests EPC-HSS avec labels 4G, Diameter, S6a'
                })
            
            # Exemples IMS-HSS
            ims_examples = df[df['Component/s'] == 'IMS-HSS'].head(3)
            for _, row in ims_examples.iterrows():
                examples_data.append({
                    'Composant': 'IMS-HSS',
                    'Summary': row['Summary'],
                    'Labels': row['Labels'],
                    'Test Set': row['Test Set'],
                    'Explication': 'Tests IMS avec labels 4G, IMS pour VoLTE et services'
                })
            
            # Exemples Sécurité
            security_examples = df[df['Component/s'] == 'Security'].head(2)
            for _, row in security_examples.iterrows():
                examples_data.append({
                    'Composant': 'Security',
                    'Summary': row['Summary'],
                    'Labels': row['Labels'],
                    'Test Set': row['Test Set'],
                    'Explication': 'Tests sécurité NonFunctional avec labels Security'
                })
            
            # Exemples OAM
            oam_examples = df[df['Component/s'] == 'OAM'].head(2)
            for _, row in oam_examples.iterrows():
                examples_data.append({
                    'Composant': 'OAM',
                    'Summary': row['Summary'],
                    'Labels': row['Labels'],
                    'Test Set': row['Test Set'],
                    'Explication': 'Tests OAM et provisioning'
                })
            
            # Exemples Backup
            backup_examples = df[df['Labels'].str.contains('BackupRestore', na=False)].head(2)
            for _, row in backup_examples.iterrows():
                examples_data.append({
                    'Composant': row['Component/s'],
                    'Summary': row['Summary'],
                    'Labels': row['Labels'],
                    'Test Set': row['Test Set'],
                    'Explication': 'Tests Backup/Restore NonFunctional'
                })
            
            df_examples = pd.DataFrame(examples_data)
            df_examples.to_excel(writer, sheet_name='Exemples par Composant', index=False)
            
            # Feuille avec statistiques
            stats_data = []
            
            # Statistiques par composant
            component_stats = df['Component/s'].value_counts()
            for component, count in component_stats.items():
                stats_data.append({
                    'Type': 'Composant',
                    'Nom': component,
                    'Nombre de tests': count,
                    'Pourcentage': f"{(count/len(df)*100):.1f}%"
                })
            
            # Statistiques par type
            functional_count = df['Labels'].str.contains('Functional', na=False).sum()
            non_functional_count = df['Labels'].str.contains('NonFunctional', na=False).sum()
            
            stats_data.append({
                'Type': 'Type de test',
                'Nom': 'Functional',
                'Nombre de tests': functional_count,
                'Pourcentage': f"{(functional_count/len(df)*100):.1f}%"
            })
            
            stats_data.append({
                'Type': 'Type de test',
                'Nom': 'NonFunctional',
                'Nombre de tests': non_functional_count,
                'Pourcentage': f"{(non_functional_count/len(df)*100):.1f}%"
            })
            
            # Statistiques par Test Set
            test_set_stats = df['Test Set'].value_counts().head(10)
            for test_set, count in test_set_stats.items():
                stats_data.append({
                    'Type': 'Test Set',
                    'Nom': test_set,
                    'Nombre de tests': count,
                    'Pourcentage': f"{(count/len(df)*100):.1f}%"
                })
            
            df_stats = pd.DataFrame(stats_data)
            df_stats.to_excel(writer, sheet_name='Statistiques', index=False)
            
            # Feuille avec règles de labellisation
            rules_data = [
                {
                    'Domaine': 'HLR',
                    'Patterns détectés': 'call forward, call barring, supplementary, camel, hlr, map, sri, ati',
                    'Labels appliqués': 'Functional, System, Manual, Regression, 2G, MAP',
                    'Composant': 'HLR',
                    'Test Sets': 'HLR Call Forwarding Services, HLR Call Barring Services, HLR Supplementary Services, HLR CAMEL Services'
                },
                {
                    'Domaine': 'EPC-HSS',
                    'Patterns détectés': 'attach, detach, update location, authentication, hss, epc, s6a, diameter, ulr, air',
                    'Labels appliqués': 'Functional, System, Manual, Regression, 4G, Diameter, S6a',
                    'Composant': 'EPC-HSS',
                    'Test Sets': 'EPC HSS Attach/Detach Procedures, EPC HSS Location Management, EPC HSS Authentication Services'
                },
                {
                    'Domaine': 'IMS',
                    'Patterns détectés': 'volte, ims, t-ads, srvcc, swx, sh, cx, scc-as',
                    'Labels appliqués': 'Functional, System, Manual, Regression, 4G, IMS',
                    'Composant': 'IMS-HSS',
                    'Test Sets': 'IMS VoLTE Services, IMS T-ADS Services, IMS SRVCC Services'
                },
                {
                    'Domaine': 'OAM',
                    'Patterns détectés': 'provisioning, oam, soap, mml, configuration, management, web agent',
                    'Labels appliqués': 'Functional, System, Manual, Regression, OAM',
                    'Composant': 'OAM',
                    'Test Sets': 'OAM and Provisioning'
                },
                {
                    'Domaine': 'Sécurité',
                    'Patterns détectés': 'cis, vulnerability, rbac, security, hardening, authentication',
                    'Labels appliqués': 'NonFunctional, System, Manual, Regression, Security',
                    'Composant': 'Security',
                    'Test Sets': 'Security CIS Compliance, Security Vulnerability Assessment, Security RBAC Management'
                },
                {
                    'Domaine': 'Backup/Restore',
                    'Patterns détectés': 'backup, restore, recovery, schedule.*backup',
                    'Labels appliqués': 'NonFunctional, System, Manual, Regression, BackupRestore',
                    'Composant': 'COMMON',
                    'Test Sets': 'Backup and Restore Operations'
                },
                {
                    'Domaine': 'Performance',
                    'Patterns détectés': 'performance, resiliency, redundancy, vnf, lcm, scaling, autoscaling',
                    'Labels appliqués': 'NonFunctional, System, Manual, Regression, Performance/Resiliency/VNF',
                    'Composant': 'COMMON',
                    'Test Sets': 'Performance Testing, Resiliency and Redundancy, VNF Lifecycle Management'
                }
            ]
            
            df_rules = pd.DataFrame(rules_data)
            df_rules.to_excel(writer, sheet_name='Règles de Labellisation', index=False)
        
        print(f"Fichier Excel créé avec succès : {excel_file}")
        print(f"Contient {len(df)} tests enrichis répartis sur 4 feuilles")
        
    except Exception as e:
        print(f"Erreur lors de la création du fichier Excel : {e}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python create_excel_output.py <input_csv> <output_excel>")
        sys.exit(1)
    
    create_excel_output(sys.argv[1], sys.argv[2])
