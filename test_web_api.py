#!/usr/bin/env python3
"""
Test simple de l'API web d'enrichissement
"""

import urllib.request
import urllib.parse
import json
import os

def test_enrichment_api():
    """Test de l'API d'enrichissement avec un fichier existant"""
    
    # Utiliser un file_id existant
    file_id = "cb08b81d-b6dd-4971-ace6-4f4f2a65ee14"
    
    # URL de l'API
    url = f"http://127.0.0.1:5000/process/{file_id}"
    
    # Données à envoyer
    data = {
        "format": "separate"
    }
    
    # Convertir en JSON
    json_data = json.dumps(data).encode('utf-8')
    
    # Créer la requête
    req = urllib.request.Request(
        url, 
        data=json_data,
        headers={
            'Content-Type': 'application/json',
            'Content-Length': len(json_data)
        },
        method='POST'
    )
    
    try:
        print(f"🔄 Test de l'API: {url}")
        print(f"📤 Données envoyées: {data}")
        
        # Envoyer la requête
        with urllib.request.urlopen(req, timeout=30) as response:
            response_data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            print(f"📥 Status: {status_code}")
            print(f"📥 Réponse: {response_data}")
            
            # Parser la réponse JSON
            try:
                result = json.loads(response_data)
                if result.get('success'):
                    print("✅ API fonctionne correctement!")
                    return True
                else:
                    print(f"❌ Erreur API: {result.get('message')}")
                    return False
            except json.JSONDecodeError:
                print(f"❌ Réponse non-JSON: {response_data}")
                return False
                
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Test de l'API Web d'Enrichissement")
    print("=" * 50)
    
    # Vérifier que le serveur est en cours d'exécution
    try:
        with urllib.request.urlopen("http://127.0.0.1:5000", timeout=5) as response:
            print("✅ Serveur Flask accessible")
    except:
        print("❌ Serveur Flask non accessible")
        print("   Lancez d'abord: python app.py")
        exit(1)
    
    # Tester l'API
    success = test_enrichment_api()
    
    if success:
        print("\n🎉 Test réussi - L'API fonctionne!")
    else:
        print("\n💥 Test échoué - Problème avec l'API")
