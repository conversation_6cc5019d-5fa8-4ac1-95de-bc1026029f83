"Summary";"Description";"Component/s";"Custom field (Manual Test Steps)";"Action";"Data";"Expected Result"
"Subscriber Query/Export";"Test subscriber export functionality";"";"[{""id"":3665376,""index"":1,""fields"":{""Action"":""Export/Query Subscribers"",""Data"":"""",""Expected Result"":""Verify if subscribers are recorded correctly""}},{""id"":3665377,""index"":2,""fields"":{""Action"":""Verify if can be exported automatically to an external SFTP server"",""Data"":"""",""Expected Result"":""The subscribers are on the external server""}}]";"Export/Query Subscribers";"";"Verify if subscribers are recorded correctly"
"Subscriber Query/Export";"Test subscriber export functionality";"";"[{""id"":3665376,""index"":1,""fields"":{""Action"":""Export/Query Subscribers"",""Data"":"""",""Expected Result"":""Verify if subscribers are recorded correctly""}},{""id"":3665377,""index"":2,""fields"":{""Action"":""Verify if can be exported automatically to an external SFTP server"",""Data"":"""",""Expected Result"":""The subscribers are on the external server""}}]";"Verify if can be exported automatically to an external SFTP server";"";"The subscribers are on the external server"
"Schedule Backup";"Test backup scheduling";"";"[{""id"":3665370,""index"":1,""fields"":{""Action"":""Schedule an automatic backup and configure to export it to an external server"",""Data"":"""",""Expected Result"":""Verify if backup are on the external server""}}]";"Schedule an automatic backup and configure to export it to an external server";"";"Verify if backup are on the external server"
"Trace function";"Test trace functionality";"";"[{""id"":3665362,""index"":1,""fields"":{""Action"":""Verify that a trace task can be created, stopped and deleted for protocol"",""Data"":"""",""Expected Result"":""Trace task are created, can be stopped and deleted""}},{""id"":3665363,""index"":2,""fields"":{""Action"":""Verify that a trace task can be created, stopped and deleted for mobile user"",""Data"":"""",""Expected Result"":""Trace task are created, can be stopped and deleted""}}]";"Verify that a trace task can be created, stopped and deleted for protocol";"";"Trace task are created, can be stopped and deleted"
"Trace function";"Test trace functionality";"";"[{""id"":3665362,""index"":1,""fields"":{""Action"":""Verify that a trace task can be created, stopped and deleted for protocol"",""Data"":"""",""Expected Result"":""Trace task are created, can be stopped and deleted""}},{""id"":3665363,""index"":2,""fields"":{""Action"":""Verify that a trace task can be created, stopped and deleted for mobile user"",""Data"":"""",""Expected Result"":""Trace task are created, can be stopped and deleted""}}]";"Verify that a trace task can be created, stopped and deleted for mobile user";"";"Trace task are created, can be stopped and deleted"
"Online loading";"Test online loading";"";"[{""id"":3665359,""index"":1,""fields"":{""Action"":""Verify that the upgrade package can be loaded into the SDM without SDM system interruption"",""Data"":"""",""Expected Result"":""The upgrade package is loaded with success""}}]";"Verify that the upgrade package can be loaded into the SDM without SDM system interruption";"";"The upgrade package is loaded with success"
