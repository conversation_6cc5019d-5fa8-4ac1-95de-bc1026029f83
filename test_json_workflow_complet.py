#!/usr/bin/env python3
"""
Test complet du workflow avec extraction JSON
"""

import pandas as pd
import os
from json_steps_processor import JSONStepsProcessor
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def print_header(title):
    print("\n" + "="*70)
    print(f"  {title}")
    print("="*70)

def test_workflow_complet():
    """
    Test du workflow complet : JSON → Extraction → Enrichissement
    """
    print_header("🚀 TEST WORKFLOW COMPLET JSON + ENRICHISSEMENT")
    
    # Étape 1 : Fichier avec données JSON
    print("📁 Étape 1 : Fichier avec données JSON")
    input_file = 'example_with_json_steps.csv'
    
    if not os.path.exists(input_file):
        print(f"❌ Fichier {input_file} non trouvé")
        return False
    
    df_original = pd.read_csv(input_file)
    print(f"   Fichier original : {len(df_original)} tests")
    print(f"   Colonnes : {list(df_original.columns)}")
    
    # Vérifier la présence du champ JSON
    json_column = 'Custom field (Manual Test Steps)'
    if json_column in df_original.columns:
        json_count = df_original[json_column].notna().sum()
        print(f"   Tests avec JSON : {json_count}")
    else:
        print(f"   ⚠️  Colonne JSON non trouvée")
    
    # Étape 2 : Extraction des étapes JSON
    print(f"\n🔧 Étape 2 : Extraction des étapes JSON")
    
    processor = JSONStepsProcessor()
    steps_file = 'test_workflow_with_steps.csv'
    
    stats = processor.process_csv_file(input_file, steps_file)
    print(f"   Statistiques extraction :")
    print(f"     Traités : {stats['processed']}")
    print(f"     Extraits : {stats['extracted']}")
    print(f"     Erreurs : {stats['errors']}")
    
    # Vérifier le fichier avec étapes
    df_with_steps = pd.read_csv(steps_file)
    print(f"   Fichier avec étapes : {len(df_with_steps)} tests")
    print(f"   Nouvelles colonnes : Action, Data, Expected Result")
    
    # Afficher un exemple
    print(f"\n📋 Exemple d'extraction :")
    first_test = df_with_steps.iloc[0]
    print(f"   Summary : {first_test['Summary']}")
    print(f"   Action : {first_test['Action']}")
    print(f"   Data : {first_test['Data']}")
    print(f"   Expected Result : {first_test['Expected Result']}")
    
    # Étape 3 : Enrichissement avec les étapes extraites
    print(f"\n🎯 Étape 3 : Enrichissement avec étapes extraites")
    
    tool = TestEnrichmentToolEnhanced()
    enriched_file = 'test_workflow_enriched.csv'
    
    tool.enrich_csv_enhanced(steps_file, enriched_file, 'separate')
    
    # Vérifier le fichier enrichi
    df_enriched = pd.read_csv(enriched_file)
    print(f"   Fichier enrichi : {len(df_enriched)} tests")
    print(f"   Colonnes totales : {len(df_enriched.columns)}")
    
    # Vérifier que les colonnes Action, Data, Expected Result sont préservées
    required_columns = ['Action', 'Data', 'Expected Result', 'Component/s', 'Test Set']
    missing_columns = [col for col in required_columns if col not in df_enriched.columns]
    
    if missing_columns:
        print(f"   ⚠️  Colonnes manquantes : {missing_columns}")
    else:
        print(f"   ✅ Toutes les colonnes requises présentes")
    
    # Compter les colonnes Labels
    labels_columns = [col for col in df_enriched.columns if 'Labels' in col]
    print(f"   Colonnes Labels : {len(labels_columns)}")
    
    # Afficher un exemple enrichi
    print(f"\n🎊 Exemple de test enrichi complet :")
    first_enriched = df_enriched.iloc[0]
    print(f"   Summary : {first_enriched['Summary']}")
    print(f"   Component : {first_enriched['Component/s']}")
    print(f"   Test Set : {first_enriched['Test Set']}")
    print(f"   Action : {first_enriched['Action']}")
    print(f"   Data : {first_enriched['Data']}")
    print(f"   Expected Result : {first_enriched['Expected Result']}")
    
    # Collecter les labels
    labels = []
    for col in df_enriched.columns:
        if 'Labels' in col:
            value = first_enriched[col]
            if pd.notna(value) and str(value) != '' and str(value).lower() not in ['nan', 'none', 'null']:
                labels.append(str(value))
    
    print(f"   Labels : {labels}")
    
    # Vérifier le format final
    print(f"\n📊 Vérification du format final :")
    
    # Lire l'en-tête brut
    with open(enriched_file, 'r', encoding='utf-8') as f:
        header_line = f.readline().strip()
    
    labels_in_header = header_line.count('Labels')
    print(f"   Colonnes 'Labels' dans l'en-tête : {labels_in_header}")
    
    # Vérifier qu'il n'y a pas de valeurs "nan"
    with open(enriched_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    nan_count = content.lower().count('nan')
    print(f"   Valeurs 'nan' trouvées : {nan_count}")
    
    # Nettoyer les fichiers de test
    os.remove(steps_file)
    os.remove(enriched_file)
    
    # Résultat final
    success = (
        stats['extracted'] > 0 and
        stats['errors'] == 0 and
        len(missing_columns) == 0 and
        labels_in_header >= 8 and
        nan_count == 0
    )
    
    return success

def test_format_final():
    """
    Test du format final avec colonnes Action, Data, Expected Result
    """
    print_header("📋 TEST FORMAT FINAL AVEC ÉTAPES JSON")
    
    # Créer un fichier de test simple
    data = {
        'Summary': ['Test 1', 'Test 2'],
        'Description': ['Desc 1', 'Desc 2'],
        'Component/s': ['', ''],
        'Custom field (Manual Test Steps)': [
            '[{"fields": {"Action": "Execute test 1", "Data": "Test data 1", "Expected Result": "Result 1"}}]',
            '[{"fields": {"Action": "Execute test 2", "Data": "", "Expected Result": "Result 2"}}]'
        ]
    }
    
    df = pd.DataFrame(data)
    test_file = 'test_format_simple.csv'
    df.to_csv(test_file, index=False)
    
    # Traitement complet
    processor = JSONStepsProcessor()
    steps_file = 'test_format_with_steps.csv'
    processor.process_csv_file(test_file, steps_file)
    
    tool = TestEnrichmentToolEnhanced()
    final_file = 'test_format_final.csv'
    tool.enrich_csv_enhanced(steps_file, final_file, 'separate')
    
    # Vérifier le format final
    df_final = pd.read_csv(final_file)
    
    print(f"📊 Format final :")
    print(f"   Tests : {len(df_final)}")
    print(f"   Colonnes : {len(df_final.columns)}")
    
    # Vérifier les colonnes essentielles
    essential_columns = ['Summary', 'Component/s', 'Test Set', 'Action', 'Data', 'Expected Result']
    for col in essential_columns:
        if col in df_final.columns:
            print(f"   ✅ {col} : présente")
        else:
            print(f"   ❌ {col} : manquante")
    
    # Compter les Labels
    labels_columns = [col for col in df_final.columns if 'Labels' in col]
    print(f"   Colonnes Labels : {len(labels_columns)}")
    
    # Afficher l'exemple
    print(f"\n🎯 Exemple de ligne finale :")
    first_row = df_final.iloc[0]
    print(f"   Summary : {first_row['Summary']}")
    print(f"   Component : {first_row['Component/s']}")
    print(f"   Test Set : {first_row['Test Set']}")
    print(f"   Action : {first_row['Action']}")
    print(f"   Data : {first_row['Data']}")
    print(f"   Expected Result : {first_row['Expected Result']}")
    
    # Nettoyer
    os.remove(test_file)
    os.remove(steps_file)
    os.remove(final_file)
    
    return len(essential_columns) == sum(1 for col in essential_columns if col in df_final.columns)

def main():
    """
    Test complet du workflow JSON
    """
    print("🧪 TEST COMPLET DU WORKFLOW JSON + ENRICHISSEMENT")
    
    # Test 1 : Workflow complet
    test1 = test_workflow_complet()
    
    # Test 2 : Format final
    test2 = test_format_final()
    
    print_header("📊 RÉSULTATS FINAUX")
    
    print(f"✅ Workflow complet : {'OK' if test1 else 'ÉCHEC'}")
    print(f"✅ Format final : {'OK' if test2 else 'ÉCHEC'}")
    
    if test1 and test2:
        print(f"\n🎉 WORKFLOW JSON COMPLET FONCTIONNEL !")
        print(f"✅ Extraction JSON → Colonnes Action, Data, Expected Result")
        print(f"✅ Enrichissement → Labels techniques + Test Sets")
        print(f"✅ Format final → Toutes colonnes présentes")
        print(f"✅ Pas de valeurs 'nan' indésirables")
        print(f"🎯 Prêt pour utilisation en production")
    else:
        print(f"\n⚠️  CERTAINS TESTS ÉCHOUENT")
        print(f"💡 Vérifiez les erreurs ci-dessus")

if __name__ == "__main__":
    main()
