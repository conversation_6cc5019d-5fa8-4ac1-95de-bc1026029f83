{% extends "base.html" %}

{% block title %}Règles Core Network - Enrichissement Tests Jira Xray{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">
                    <i class="fas fa-cogs"></i> Règles Complètes Core Network - Standards 3GPP
                </h3>
            </div>
            <div class="card-body">
                <p class="lead">
                    Système complet de règles d'enrichissement basé sur l'expertise Core Network,
                    les standards 3GPP et les bonnes pratiques ISTQB.
                </p>

                <!-- Navigation des sections -->
                <nav class="mb-4">
                    <div class="nav nav-tabs" id="nav-tab" role="tablist">
                        <button class="nav-link active" id="nav-testsets-tab" data-bs-toggle="tab" data-bs-target="#nav-testsets" type="button">
                            <i class="fas fa-folder"></i> Test Sets
                        </button>
                        <button class="nav-link" id="nav-labels-tab" data-bs-toggle="tab" data-bs-target="#nav-labels" type="button">
                            <i class="fas fa-tags"></i> Labels Techniques
                        </button>
                        <button class="nav-link" id="nav-3gpp-tab" data-bs-toggle="tab" data-bs-target="#nav-3gpp" type="button">
                            <i class="fas fa-book"></i> Références 3GPP
                        </button>
                        <button class="nav-link" id="nav-patterns-tab" data-bs-toggle="tab" data-bs-target="#nav-patterns" type="button">
                            <i class="fas fa-search"></i> Patterns de Détection
                        </button>
                    </div>
                </nav>

                <div class="tab-content" id="nav-tabContent">

                    <!-- Test Sets -->
                    <div class="tab-pane fade show active" id="nav-testsets" role="tabpanel">
                        <h4><i class="fas fa-folder-open"></i> Test Sets Organisés par Domaines</h4>
                        <p class="text-muted">Classification complète des Test Sets selon l'expertise Core Network</p>

                        <!-- HLR Domain -->
                        <div class="card mb-3">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-server"></i> Domaine HLR (2G/3G)</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="test-set-item">
                                            <h6><strong>HLR Call Forwarding Services</strong></h6>
                                            <p class="small text-muted">Tests des services de renvoi d'appel selon 3GPP TS 23.082</p>
                                            <div class="labels-preview">
                                                <span class="badge bg-primary">Functional</span>
                                                <span class="badge bg-secondary">2G</span>
                                                <span class="badge bg-secondary">MAP</span>
                                                <span class="badge bg-warning">CallForwarding</span>
                                                <span class="badge bg-info">3GPP_TS_23.082</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="test-set-item">
                                            <h6><strong>HLR Call Barring Services</strong></h6>
                                            <p class="small text-muted">Tests des services d'interdiction d'appel selon 3GPP TS 23.088</p>
                                            <div class="labels-preview">
                                                <span class="badge bg-primary">Functional</span>
                                                <span class="badge bg-secondary">2G</span>
                                                <span class="badge bg-secondary">MAP</span>
                                                <span class="badge bg-warning">CallBarring</span>
                                                <span class="badge bg-info">3GPP_TS_23.088</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="test-set-item">
                                            <h6><strong>HLR CAMEL Services</strong></h6>
                                            <p class="small text-muted">Tests des services CAMEL selon 3GPP TS 23.078</p>
                                            <div class="labels-preview">
                                                <span class="badge bg-primary">Functional</span>
                                                <span class="badge bg-secondary">2G</span>
                                                <span class="badge bg-secondary">MAP</span>
                                                <span class="badge bg-warning">CAMEL</span>
                                                <span class="badge bg-info">3GPP_TS_23.078</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="test-set-item">
                                            <h6><strong>HLR Location Management</strong></h6>
                                            <p class="small text-muted">Tests de gestion de localisation HLR selon 3GPP TS 23.012</p>
                                            <div class="labels-preview">
                                                <span class="badge bg-primary">Functional</span>
                                                <span class="badge bg-secondary">2G</span>
                                                <span class="badge bg-secondary">MAP</span>
                                                <span class="badge bg-warning">LocationUpdate</span>
                                                <span class="badge bg-info">3GPP_TS_23.012</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- EPC-HSS Domain -->
                        <div class="card mb-3">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="fas fa-network-wired"></i> Domaine EPC-HSS (4G)</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="test-set-item">
                                            <h6><strong>EPC HSS Authentication</strong></h6>
                                            <p class="small text-muted">Tests d'authentification EPC-HSS via S6a selon 3GPP TS 29.272</p>
                                            <div class="labels-preview">
                                                <span class="badge bg-primary">Functional</span>
                                                <span class="badge bg-secondary">4G</span>
                                                <span class="badge bg-secondary">Diameter</span>
                                                <span class="badge bg-secondary">S6a</span>
                                                <span class="badge bg-warning">Authentication</span>
                                                <span class="badge bg-info">3GPP_TS_29.272</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="test-set-item">
                                            <h6><strong>EPC HSS Location Management</strong></h6>
                                            <p class="small text-muted">Tests de gestion de localisation EPC-HSS selon 3GPP TS 29.272</p>
                                            <div class="labels-preview">
                                                <span class="badge bg-primary">Functional</span>
                                                <span class="badge bg-secondary">4G</span>
                                                <span class="badge bg-secondary">Diameter</span>
                                                <span class="badge bg-secondary">S6a</span>
                                                <span class="badge bg-warning">LocationUpdate</span>
                                                <span class="badge bg-info">3GPP_TS_29.272</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- IMS Domain -->
                        <div class="card mb-3">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0"><i class="fas fa-phone"></i> Domaine IMS</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="test-set-item">
                                            <h6><strong>IMS VoLTE Services</strong></h6>
                                            <p class="small text-muted">Tests des services VoLTE IMS selon 3GPP TS 23.216</p>
                                            <div class="labels-preview">
                                                <span class="badge bg-primary">Functional</span>
                                                <span class="badge bg-secondary">4G</span>
                                                <span class="badge bg-secondary">IMS</span>
                                                <span class="badge bg-warning">VoLTE</span>
                                                <span class="badge bg-info">3GPP_TS_23.216</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="test-set-item">
                                            <h6><strong>IMS Cx/Dx Interface</strong></h6>
                                            <p class="small text-muted">Tests de l'interface Cx/Dx IMS selon 3GPP TS 29.229</p>
                                            <div class="labels-preview">
                                                <span class="badge bg-primary">Functional</span>
                                                <span class="badge bg-secondary">4G</span>
                                                <span class="badge bg-secondary">IMS</span>
                                                <span class="badge bg-secondary">Diameter</span>
                                                <span class="badge bg-secondary">Cx</span>
                                                <span class="badge bg-info">3GPP_TS_29.229</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Domain -->
                        <div class="card mb-3">
                            <div class="card-header bg-danger text-white">
                                <h5 class="mb-0"><i class="fas fa-tachometer-alt"></i> Domaine Performance</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="test-set-item">
                                            <h6><strong>Performance Load Testing</strong></h6>
                                            <p class="small text-muted">Tests de charge et performance selon 3GPP TS 32.401</p>
                                            <div class="labels-preview">
                                                <span class="badge bg-danger">NonFunctional</span>
                                                <span class="badge bg-warning">Performance</span>
                                                <span class="badge bg-warning">Load</span>
                                                <span class="badge bg-info">3GPP_TS_32.401</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="test-set-item">
                                            <h6><strong>Performance Diameter Routing</strong></h6>
                                            <p class="small text-muted">Tests de performance du routage Diameter selon 3GPP TS 29.212</p>
                                            <div class="labels-preview">
                                                <span class="badge bg-danger">NonFunctional</span>
                                                <span class="badge bg-warning">Performance</span>
                                                <span class="badge bg-secondary">Diameter</span>
                                                <span class="badge bg-warning">Routing</span>
                                                <span class="badge bg-info">3GPP_TS_29.212</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Security Domain -->
                        <div class="card mb-3">
                            <div class="card-header bg-dark text-white">
                                <h5 class="mb-0"><i class="fas fa-shield-alt"></i> Domaine Sécurité</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="test-set-item">
                                            <h6><strong>Security Vulnerability Assessment</strong></h6>
                                            <p class="small text-muted">Tests d'évaluation des vulnérabilités selon 3GPP TS 33.102</p>
                                            <div class="labels-preview">
                                                <span class="badge bg-danger">NonFunctional</span>
                                                <span class="badge bg-dark">Security</span>
                                                <span class="badge bg-warning">Vulnerability</span>
                                                <span class="badge bg-info">3GPP_TS_33.102</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="test-set-item">
                                            <h6><strong>Security CIS Compliance</strong></h6>
                                            <p class="small text-muted">Tests de conformité CIS (Center for Internet Security)</p>
                                            <div class="labels-preview">
                                                <span class="badge bg-danger">NonFunctional</span>
                                                <span class="badge bg-dark">Security</span>
                                                <span class="badge bg-warning">CIS</span>
                                                <span class="badge bg-info">3GPP_TS_33.102</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Labels Techniques -->
                    <div class="tab-pane fade" id="nav-labels" role="tabpanel">
                        <h4><i class="fas fa-tags"></i> Labels Techniques Automatiques</h4>
                        <p class="text-muted">Classification complète des labels selon les standards ISTQB et 3GPP</p>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">Domaines Techniques</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="label-group">
                                            <span class="badge bg-secondary me-1">2G</span>
                                            <span class="badge bg-secondary me-1">3G</span>
                                            <span class="badge bg-secondary me-1">4G</span>
                                            <span class="badge bg-secondary me-1">5G</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-3">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">Protocoles</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="label-group">
                                            <span class="badge bg-info me-1">MAP</span>
                                            <span class="badge bg-info me-1">Diameter</span>
                                            <span class="badge bg-info me-1">SOAP</span>
                                            <span class="badge bg-info me-1">HTTP</span>
                                            <span class="badge bg-info me-1">SNMP</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-3">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">Interfaces</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="label-group">
                                            <span class="badge bg-warning me-1">S6a</span>
                                            <span class="badge bg-warning me-1">S6d</span>
                                            <span class="badge bg-warning me-1">Cx</span>
                                            <span class="badge bg-warning me-1">Dx</span>
                                            <span class="badge bg-warning me-1">Sh</span>
                                            <span class="badge bg-warning me-1">Rx</span>
                                            <span class="badge bg-warning me-1">Gx</span>
                                            <span class="badge bg-warning me-1">Swx</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">Types de Tests ISTQB</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="label-group mb-2">
                                            <strong>Types :</strong>
                                            <span class="badge bg-primary me-1">Functional</span>
                                            <span class="badge bg-danger me-1">NonFunctional</span>
                                        </div>
                                        <div class="label-group mb-2">
                                            <strong>Niveaux :</strong>
                                            <span class="badge bg-secondary me-1">Unit</span>
                                            <span class="badge bg-secondary me-1">Integration</span>
                                            <span class="badge bg-secondary me-1">System</span>
                                            <span class="badge bg-secondary me-1">Acceptance</span>
                                        </div>
                                        <div class="label-group mb-2">
                                            <strong>Méthodes :</strong>
                                            <span class="badge bg-success me-1">Manual</span>
                                            <span class="badge bg-info me-1">Automated</span>
                                        </div>
                                        <div class="label-group">
                                            <strong>Objectifs :</strong>
                                            <span class="badge bg-warning me-1">Regression</span>
                                            <span class="badge bg-warning me-1">Smoke</span>
                                            <span class="badge bg-warning me-1">Sanity</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-3">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">Domaines Fonctionnels</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="label-group">
                                            <span class="badge bg-light text-dark me-1">Authentication</span>
                                            <span class="badge bg-light text-dark me-1">Authorization</span>
                                            <span class="badge bg-light text-dark me-1">CallForwarding</span>
                                            <span class="badge bg-light text-dark me-1">CallBarring</span>
                                            <span class="badge bg-light text-dark me-1">LocationUpdate</span>
                                            <span class="badge bg-light text-dark me-1">Routing</span>
                                            <span class="badge bg-light text-dark me-1">Provisioning</span>
                                            <span class="badge bg-light text-dark me-1">Configuration</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-3">
                                    <div class="card-header bg-danger text-white">
                                        <h6 class="mb-0">Domaines Non-Fonctionnels</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="label-group">
                                            <span class="badge bg-danger me-1">Performance</span>
                                            <span class="badge bg-danger me-1">Security</span>
                                            <span class="badge bg-danger me-1">Reliability</span>
                                            <span class="badge bg-danger me-1">Scalability</span>
                                            <span class="badge bg-danger me-1">Availability</span>
                                            <span class="badge bg-danger me-1">Maintainability</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Références 3GPP -->
                    <div class="tab-pane fade" id="nav-3gpp" role="tabpanel">
                        <h4><i class="fas fa-book"></i> Références 3GPP Automatiques</h4>
                        <p class="text-muted">Références aux spécifications 3GPP ajoutées automatiquement selon le contenu</p>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">HLR/2G/3G</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="gpp-ref-item mb-2">
                                            <strong>3GPP TS 23.082</strong> - Call Forwarding
                                            <br><small class="text-muted">Services de renvoi d'appel</small>
                                        </div>
                                        <div class="gpp-ref-item mb-2">
                                            <strong>3GPP TS 23.088</strong> - Call Barring
                                            <br><small class="text-muted">Services d'interdiction d'appel</small>
                                        </div>
                                        <div class="gpp-ref-item mb-2">
                                            <strong>3GPP TS 23.078</strong> - CAMEL
                                            <br><small class="text-muted">Services intelligents</small>
                                        </div>
                                        <div class="gpp-ref-item mb-2">
                                            <strong>3GPP TS 29.002</strong> - MAP
                                            <br><small class="text-muted">Protocole MAP</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">EPC/HSS/4G</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="gpp-ref-item mb-2">
                                            <strong>3GPP TS 29.272</strong> - S6a Interface
                                            <br><small class="text-muted">Interface S6a MME-HSS</small>
                                        </div>
                                        <div class="gpp-ref-item mb-2">
                                            <strong>3GPP TS 29.229</strong> - Diameter
                                            <br><small class="text-muted">Protocole Diameter</small>
                                        </div>
                                        <div class="gpp-ref-item mb-2">
                                            <strong>3GPP TS 23.401</strong> - EPC
                                            <br><small class="text-muted">Architecture EPC</small>
                                        </div>
                                        <div class="gpp-ref-item mb-2">
                                            <strong>3GPP TS 29.212</strong> - Diameter Routing
                                            <br><small class="text-muted">Routage Diameter</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">IMS/VoLTE</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="gpp-ref-item mb-2">
                                            <strong>3GPP TS 23.228</strong> - IMS
                                            <br><small class="text-muted">Architecture IMS</small>
                                        </div>
                                        <div class="gpp-ref-item mb-2">
                                            <strong>3GPP TS 23.216</strong> - VoLTE
                                            <br><small class="text-muted">Voice over LTE</small>
                                        </div>
                                        <div class="gpp-ref-item mb-2">
                                            <strong>3GPP TS 29.329</strong> - Sh Interface
                                            <br><small class="text-muted">Interface Sh</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-danger text-white">
                                        <h6 class="mb-0">Performance & Sécurité</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="gpp-ref-item mb-2">
                                            <strong>3GPP TS 32.401</strong> - Performance
                                            <br><small class="text-muted">Gestion de performance</small>
                                        </div>
                                        <div class="gpp-ref-item mb-2">
                                            <strong>3GPP TS 33.102</strong> - Security
                                            <br><small class="text-muted">Aspects sécurité</small>
                                        </div>
                                        <div class="gpp-ref-item mb-2">
                                            <strong>3GPP TS 33.401</strong> - EPS Security
                                            <br><small class="text-muted">Sécurité EPS</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Patterns de Détection -->
                    <div class="tab-pane fade" id="nav-patterns" role="tabpanel">
                        <h4><i class="fas fa-search"></i> Patterns de Détection Automatique</h4>
                        <p class="text-muted">Expressions régulières utilisées pour détecter automatiquement les domaines et appliquer les règles</p>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Fonctionnement :</strong> L'outil analyse le contenu des tests (Summary, Description, Action, Expected Result)
                            avec ces patterns pour déterminer automatiquement le Test Set approprié et les labels techniques.
                        </div>

                        <div class="accordion" id="patternsAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="hlrPatterns">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseHLR">
                                        <i class="fas fa-server me-2"></i> Patterns HLR (2G/3G)
                                    </button>
                                </h2>
                                <div id="collapseHLR" class="accordion-collapse collapse show" data-bs-parent="#patternsAccordion">
                                    <div class="accordion-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>Call Forwarding :</h6>
                                                <code>call.*forward, cfu, cfb, cfnr, renvoi</code>
                                                <h6 class="mt-3">Call Barring :</h6>
                                                <code>call.*barr, interdiction, restriction</code>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>CAMEL :</h6>
                                                <code>camel, intelligent.*network</code>
                                                <h6 class="mt-3">Location :</h6>
                                                <code>location.*update, sri, ati, localisation</code>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="accordion-item">
                                <h2 class="accordion-header" id="epcPatterns">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseEPC">
                                        <i class="fas fa-network-wired me-2"></i> Patterns EPC-HSS (4G)
                                    </button>
                                </h2>
                                <div id="collapseEPC" class="accordion-collapse collapse" data-bs-parent="#patternsAccordion">
                                    <div class="accordion-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>Authentication :</h6>
                                                <code>authentication, air, aia, auth.*vector</code>
                                                <h6 class="mt-3">Location Update :</h6>
                                                <code>update.*location, ulr, ula</code>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>S6a Interface :</h6>
                                                <code>s6a, diameter, hss, epc</code>
                                                <h6 class="mt-3">Notification :</h6>
                                                <code>notification, nor, noa, notify</code>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="accordion-item">
                                <h2 class="accordion-header" id="performancePatterns">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePerformance">
                                        <i class="fas fa-tachometer-alt me-2"></i> Patterns Performance
                                    </button>
                                </h2>
                                <div id="collapsePerformance" class="accordion-collapse collapse" data-bs-parent="#patternsAccordion">
                                    <div class="accordion-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>Load Testing :</h6>
                                                <code>performance, load, stress, capacity, throughput</code>
                                                <h6 class="mt-3">Scalability :</h6>
                                                <code>scalability, scaling, montee.*charge</code>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Diameter Routing :</h6>
                                                <code>diameter.*routing, routing.*performance, dra</code>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- Footer avec statistiques -->
                <div class="mt-4 p-3 bg-light rounded">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h5 class="text-primary">27</h5>
                            <small>Test Sets Spécialisés</small>
                        </div>
                        <div class="col-md-3">
                            <h5 class="text-success">63</h5>
                            <small>Labels Techniques</small>
                        </div>
                        <div class="col-md-3">
                            <h5 class="text-info">23</h5>
                            <small>Références 3GPP</small>
                        </div>
                        <div class="col-md-3">
                            <h5 class="text-warning">8</h5>
                            <small>Domaines Couverts</small>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="mt-4 text-center">
                    <button id="btn-generate-csv" class="btn btn-success btn-lg me-2" onclick="generateTestSetsCSV()">
                        <i class="fas fa-file-csv"></i> Générer CSV Test Sets
                    </button>
                    <button class="btn btn-outline-info btn-lg" onclick="showCSVFormatHelp()">
                        <i class="fas fa-info-circle"></i> Format CSV
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Générer le fichier CSV des Test Sets
async function generateTestSetsCSV() {
    const button = document.getElementById('btn-generate-csv');
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Génération...';
    button.disabled = true;

    try {
        const response = await fetch('/generate_test_sets_csv', {
            method: 'GET'
        });

        const result = await response.json();

        if (result.success) {
            // Afficher le succès
            showAlert('success', `Fichier CSV généré avec succès ! ${result.stats.total_test_sets} Test Sets inclus.`);

            // Créer un lien de téléchargement
            const downloadLink = document.createElement('a');
            downloadLink.href = `/download/${result.filename}`;
            downloadLink.download = result.filename;
            downloadLink.style.display = 'none';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);

        } else {
            showAlert('danger', result.message);
        }
    } catch (error) {
        showAlert('danger', 'Erreur de communication avec le serveur');
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Afficher l'aide sur le format CSV
function showCSVFormatHelp() {
    const helpContent = `
    <div class="modal fade" id="csvFormatModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Format CSV pour Import Test Sets</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>📋 Structure du Fichier CSV :</h6>
                    <code>Summary;Status;Labels;Labels_1;Labels_2;Labels_3;Labels_4;Labels_5;Labels_6;Labels_7;Labels_8;Labels_9;;Description;Comment</code>

                    <h6 class="mt-3">📊 Contenu des Colonnes :</h6>
                    <ul>
                        <li><strong>Summary</strong> : Nom du Test Set</li>
                        <li><strong>Status</strong> : Statut (Draft par défaut)</li>
                        <li><strong>Labels à Labels_9</strong> : Labels techniques séparés (jusqu'à 10 colonnes)</li>
                        <li><strong>Description</strong> : Description technique en anglais</li>
                        <li><strong>Comment</strong> : Commentaire vulgarisé en français</li>
                    </ul>

                    <h6 class="mt-3">🎯 Exemple d'Entrée :</h6>
                    <div class="bg-light p-2 rounded">
                        <small>
                        <strong>Summary:</strong> HLR Call Forwarding Services<br>
                        <strong>Labels:</strong> Functional, System, Manual, Regression, 2G, MAP, CallForwarding, 3GPP_TS_23.082<br>
                        <strong>Description:</strong> Comprehensive testing of call forwarding services...<br>
                        <strong>Comment:</strong> Tests des services de renvoi d'appel (CFU, CFB, CFNR)...
                        </small>
                    </div>

                    <h6 class="mt-3">📁 Utilisation :</h6>
                    <ol>
                        <li>Cliquer sur "Générer CSV Test Sets"</li>
                        <li>Télécharger le fichier généré</li>
                        <li>Importer dans votre outil de gestion de tests</li>
                        <li>Les 27 Test Sets seront créés automatiquement</li>
                    </ol>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>
    `;

    document.body.insertAdjacentHTML('beforeend', helpContent);
    new bootstrap.Modal(document.getElementById('csvFormatModal')).show();
}

// Fonction pour afficher des alertes
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
