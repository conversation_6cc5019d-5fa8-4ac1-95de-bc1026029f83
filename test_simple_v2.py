#!/usr/bin/env python3
"""
Test simple de la version V2 avec colonnes séparées
"""

import pandas as pd
import os
from test_enrichment_tool_v2 import TestEnrichmentToolV2

def create_simple_test():
    """
    Crée un fichier de test simple
    """
    data = {
        'Summary': [
            'CFU Test',
            'S6a ULR', 
            'Security Scan'
        ],
        'Description': [
            'Test Call Forwarding Unconditional in HLR',
            'Test Update Location Request on S6a interface',
            'Security vulnerability scanning'
        ],
        'Component/s': ['', '', ''],
        'Action': [
            'Activate CFU service',
            'Send ULR from MME to HSS',
            'Run vulnerability scan'
        ],
        'Expected Result': [
            'CFU activated successfully',
            'HSS responds with ULA',
            'No vulnerabilities found'
        ]
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_simple_input.csv', index=False)
    print("✅ Fichier de test créé : test_simple_input.csv")

def test_v2():
    """
    Test la version V2
    """
    print("=== TEST VERSION V2 - COLONNES LABELS SÉPARÉES ===\n")
    
    # C<PERSON>er le fichier de test
    create_simple_test()
    
    # Enrichir
    tool = TestEnrichmentToolV2()
    tool.enrich_csv_with_separate_labels('test_simple_input.csv', 'test_simple_output.csv')
    
    # Analyser le résultat
    print("\n=== ANALYSE DU RÉSULTAT ===")
    df = pd.read_csv('test_simple_output.csv')
    
    print(f"Colonnes : {list(df.columns)}")
    
    # Compter les colonnes Labels
    label_columns = [col for col in df.columns if col == 'Labels']
    print(f"Nombre de colonnes 'Labels' : {len(label_columns)}")
    
    # Afficher le contenu
    print("\n=== CONTENU ===")
    for index, row in df.iterrows():
        print(f"\nTest {index+1}: {row['Summary']}")
        print(f"  Composant: {row['Component/s']}")
        print(f"  Test Set: {row['Test Set']}")
        print("  Labels:")
        for i, col in enumerate(label_columns):
            if col == 'Labels':
                value = row.iloc[df.columns.get_loc(col) + i]
                if pd.notna(value) and value != '':
                    print(f"    Colonne {i+1}: {value}")
    
    # Nettoyer
    os.remove('test_simple_input.csv')
    print(f"\n✅ Test terminé. Fichier de sortie : test_simple_output.csv")

if __name__ == "__main__":
    test_v2()
