#!/usr/bin/env python3
"""
Test du workflow complet : extraction JSON + enrichissement
pour reproduire exactement votre problème
"""

import pandas as pd
import os
import uuid
from json_steps_processor import JSONStepsProcessor
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def test_workflow_extraction_puis_enrichissement():
    """
    Test qui reproduit exactement votre workflow:
    1. Fichier avec JSON steps
    2. Extraction JSON -> fichier with_steps_
    3. Enrichissement du fichier with_steps_
    """
    print("🧪 TEST WORKFLOW COMPLET EXTRACTION + ENRICHISSEMENT")
    print("="*70)
    
    # Générer un ID unique comme dans l'application
    file_id = str(uuid.uuid4())[:8]
    
    # 1. Créer un fichier original avec JSON steps (comme votre export Jira)
    data_original = {
        'Summary': ['S6a ULR Test', 'VoLTE Call Test'],
        'Issue key': ['TEST-001', 'TEST-002'],
        'Component/s': ['EPC-HSS', 'IMS-HSS'],
        'Priority': ['High', 'Medium'],
        'Description': ['Test Update Location Request', 'Test VoLTE call setup'],
        'Custom field (Manual Test Steps)': [
            '[{"id":1,"fields":{"Action":"Send ULR","Data":"IMSI=123456789012345","Expected Result":"HSS responds with ULA"}}]',
            '[{"id":1,"fields":{"Action":"Setup call","Data":"","Expected Result":"Call established"}}]'
        ]
    }
    
    df_original = pd.DataFrame(data_original)
    original_file = f"{file_id}.csv"
    df_original.to_csv(original_file, index=False)
    
    print(f"📁 1. Fichier original créé: {original_file}")
    print(f"   Contient JSON steps dans 'Custom field (Manual Test Steps)'")
    
    try:
        # 2. Extraction JSON (comme dans l'application)
        print(f"\n🔧 2. Extraction JSON...")
        processor = JSONStepsProcessor()
        with_steps_file = f"with_steps_{file_id}.csv"
        stats = processor.process_csv_file(original_file, with_steps_file)
        
        print(f"✅ Extraction terminée: {with_steps_file}")
        print(f"   Stats: {stats}")
        
        # Vérifier le fichier extrait
        if os.path.exists(with_steps_file):
            with open(with_steps_file, 'r', encoding='utf-8') as f:
                header_extracted = f.readline().strip()
            
            print(f"📋 En-tête fichier extrait:")
            columns_extracted = header_extracted.split(',')
            for i, col in enumerate(columns_extracted):
                if col in ['Action', 'Data', 'Expected Result']:
                    print(f"   {i+1:2d}. {col} ← EXTRAIT")
                elif i < 10:
                    print(f"   {i+1:2d}. {col}")
            
            has_extracted_cols = all(col in header_extracted for col in ['Action', 'Data', 'Expected Result'])
            print(f"   Colonnes Action/Data/Expected Result présentes: {has_extracted_cols}")
            
            # 3. Enrichissement du fichier with_steps_ (comme dans l'application)
            print(f"\n🎯 3. Enrichissement du fichier extrait...")
            
            # Simuler exactement ce que fait l'application
            UPLOAD_FOLDER = "."  # Dossier courant pour le test
            PROCESSED_FOLDER = "."
            
            # Logique exacte de l'application
            with_steps_path = os.path.join(UPLOAD_FOLDER, with_steps_file)
            
            print(f"🏷️ [DEBUG] Recherche fichier with_steps: {with_steps_path}")
            print(f"🏷️ [DEBUG] Fichier with_steps existe: {os.path.exists(with_steps_path)}")
            
            if os.path.exists(with_steps_path):
                input_filepath = with_steps_path
                print(f"📋 [DEBUG] Fichier avec étapes JSON détecté : {with_steps_file}")
                print(f"📋 [DEBUG] Chemin complet: {input_filepath}")
                
                # Format forcé comme dans l'application
                format_type = 'separate'
                print(f"🏷️ [DEBUG] Format 'separate' forcé pour colonnes Labels séparées identiques")
                
                # Enrichissement
                output_filename = f"enriched_{file_id}.csv"
                output_filepath = os.path.join(PROCESSED_FOLDER, output_filename)
                
                print(f"📋 [DEBUG] Enrichissement avec:")
                print(f"   Input: {input_filepath}")
                print(f"   Output: {output_filepath}")
                print(f"   Format: {format_type}")
                
                tool = TestEnrichmentToolEnhanced()
                tool.enrich_csv_enhanced(input_filepath, output_filepath, format_type)
                
                # Vérifier le résultat
                if os.path.exists(output_filepath):
                    with open(output_filepath, 'r', encoding='utf-8') as f:
                        header_enriched = f.readline().strip()
                    
                    labels_count = header_enriched.count('Labels')
                    print(f"📋 [DEBUG] Fichier enrichi créé avec {labels_count} colonnes Labels")
                    
                    if labels_count >= 6:
                        print(f"🎉 SUCCÈS ! Format correct avec {labels_count} colonnes Labels")
                        print(f"📋 Format Labels: {','.join(['Labels'] * labels_count)}")
                        success = True
                    else:
                        print(f"❌ PROBLÈME: Seulement {labels_count} colonne(s) Labels au lieu de plusieurs!")
                        print(f"❌ En-tête problématique: {header_enriched[:200]}...")
                        success = False
                        
                        # Analyser pourquoi ça ne marche pas
                        print(f"\n🔍 ANALYSE DU PROBLÈME:")
                        print(f"   Fichier d'entrée: {input_filepath}")
                        print(f"   Format demandé: {format_type}")
                        
                        # Vérifier le contenu du fichier d'entrée
                        df_check = pd.read_csv(input_filepath, nrows=0)
                        cols_input = df_check.columns.tolist()
                        print(f"   Colonnes d'entrée: {len(cols_input)}")
                        print(f"   Action/Data/Expected Result présents: {all(col in cols_input for col in ['Action', 'Data', 'Expected Result'])}")
                    
                    # Nettoyer
                    os.remove(output_filepath)
                else:
                    print(f"❌ [DEBUG] Fichier enrichi non créé!")
                    success = False
            else:
                print(f"❌ Fichier with_steps non trouvé!")
                success = False
            
            # Nettoyer
            os.remove(with_steps_file)
        else:
            print(f"❌ Fichier extrait non créé!")
            success = False
        
        # Nettoyer
        os.remove(original_file)
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        print(traceback.format_exc())
        success = False
    
    return success

def main():
    """
    Test principal
    """
    print("🧪 REPRODUCTION DU PROBLÈME WORKFLOW EXTRACTION + ENRICHISSEMENT")
    
    success = test_workflow_extraction_puis_enrichissement()
    
    print("\n" + "="*70)
    print("📊 RÉSULTAT FINAL")
    print("="*70)
    
    if success:
        print(f"✅ Workflow extraction + enrichissement : OK")
        print(f"🎉 Le problème est résolu !")
        print(f"✅ Format correct avec colonnes Labels multiples")
    else:
        print(f"❌ Workflow extraction + enrichissement : ÉCHEC")
        print(f"⚠️ Le problème persiste")
        print(f"❌ Format incorrect avec une seule colonne Labels")
        
        print(f"\n💡 SOLUTIONS POSSIBLES:")
        print(f"   1. Vérifier que l'application utilise bien la version corrigée")
        print(f"   2. Redémarrer complètement l'application")
        print(f"   3. Vider le cache du navigateur")
        print(f"   4. Vérifier les logs de l'application")

if __name__ == "__main__":
    main()
