#!/usr/bin/env python3
"""
Analyseur de cohérence des Test Sets pour validation Core Network
Basé sur les standards 3GPP et les bonnes pratiques ISTQB
"""

import pandas as pd
import re
from typing import Dict, List, Tuple, Set
from collections import defaultdict, Counter

class TestSetAnalyzer:
    """
    Analyseur de cohérence pour les Test Sets Jira/Xray
    """
    
    def __init__(self):
        # Définition des règles de mapping 3GPP
        self.gpp_references = {
            'Call Forwarding': 'TS 23.082',
            'Call Barring': 'TS 23.088', 
            'CAMEL': 'TS 23.078',
            'EPC': 'TS 23.401',
            'IMS': 'TS 23.228',
            'HSS': 'TS 29.328',
            'S6a': 'TS 29.272',
            'MAP': 'TS 29.002',
            'Diameter': 'TS 29.229',
            'VoLTE': 'TS 23.216',
            'SRVCC': 'TS 23.216'
        }
        
        # Labels techniques obligatoires par domaine
        self.mandatory_labels = {
            'HLR': ['2G', 'MAP', 'Functional', 'System', 'Manual', 'Regression'],
            'EPC-HSS': ['4G', 'Diameter', 'S6a', 'Functional', 'System', 'Manual', 'Regression'],
            'IMS-HSS': ['4G', 'IMS', 'Functional', 'System', 'Manual', 'Regression'],
            'Security': ['NonFunctional', 'Security', 'System', 'Manual', 'Regression'],
            'OAM': ['Functional', 'OAM', 'System', 'Manual', 'Regression'],
            'Performance': ['NonFunctional', 'Performance', 'System', 'Manual', 'Regression']
        }
        
        # Test Sets recommandés
        self.recommended_test_sets = {
            'HLR Call Forwarding Services': ['CFU', 'CFB', 'CFNR', 'Call Forwarding'],
            'HLR Call Barring Services': ['Call Barring', 'Barring'],
            'HLR CAMEL Services': ['CAMEL'],
            'EPC HSS Location Management': ['ULR', 'Update Location', 'S6a ULR'],
            'EPC HSS General Services': ['S6a', 'HSS', 'EPC', 'Reset', 'NOR'],
            'IMS General Services': ['IMS Registration', 'IMS'],
            'IMS T-ADS Services': ['T-ADS', 'TADS'],
            'Security Vulnerability Assessment': ['Vulnerability', 'Scan'],
            'Security CIS Compliance': ['CIS'],
            'Security RBAC Management': ['RBAC'],
            'Security General Tests': ['Security', 'Authentication', 'Hardening'],
            'Backup and Restore Operations': ['Backup', 'Restore', 'BackupRestore'],
            'OAM and Provisioning': ['OAM', 'Provisioning', 'Configuration'],
            'Performance Testing': ['Performance', 'Load'],
            'Resiliency and Redundancy': ['Resiliency', 'Redundancy', 'Geo'],
            'Vendor Features': ['ZTE', 'Huawei', 'Nokia', 'Ericsson']
        }
        
        # Patterns pour détecter les incohérences
        self.inconsistency_patterns = {
            'backup_in_wrong_set': r'(backup|restore|recovery)',
            'security_dispersed': r'(security|vulnerability|cis|rbac|hardening)',
            'oam_misplaced': r'(oam|provisioning|configuration|management)',
            'performance_generic': r'(performance|load|stress|capacity)'
        }
    
    def analyze_test_file(self, csv_file: str) -> Dict:
        """
        Analyse complète d'un fichier de tests
        """
        df = pd.read_csv(csv_file)
        
        results = {
            'total_tests': len(df),
            'misclassified_tests': [],
            'missing_labels': [],
            'orphan_tests': [],
            'duplicate_assignments': [],
            'missing_3gpp_refs': [],
            'recommended_new_test_sets': [],
            'label_inconsistencies': [],
            'summary': {}
        }
        
        # Analyser chaque test
        for index, row in df.iterrows():
            test_analysis = self._analyze_single_test(row, index)
            
            # Collecter les problèmes
            if test_analysis['misclassified']:
                results['misclassified_tests'].append(test_analysis)
            
            if test_analysis['missing_labels']:
                results['missing_labels'].append(test_analysis)
            
            if test_analysis['missing_3gpp']:
                results['missing_3gpp_refs'].append(test_analysis)
            
            if test_analysis['label_issues']:
                results['label_inconsistencies'].append(test_analysis)
        
        # Générer les recommandations
        results['recommended_actions'] = self._generate_recommendations(results)
        results['summary'] = self._generate_summary(results)
        
        return results
    
    def _analyze_single_test(self, row: pd.Series, index: int) -> Dict:
        """
        Analyse un test individuel
        """
        summary = str(row.get('Summary', ''))
        description = str(row.get('Description', ''))
        current_test_set = str(row.get('Test Set', ''))
        component = str(row.get('Component/s', ''))
        
        # Collecter tous les labels
        labels = []
        for col in row.index:
            if col == 'Labels' and pd.notna(row[col]) and row[col] != '':
                labels.append(str(row[col]))
        
        analysis = {
            'index': index,
            'summary': summary,
            'current_test_set': current_test_set,
            'component': component,
            'labels': labels,
            'misclassified': False,
            'missing_labels': [],
            'missing_3gpp': False,
            'label_issues': [],
            'recommended_test_set': '',
            'recommended_labels': [],
            'gpp_reference': ''
        }
        
        # Vérifier la classification
        recommended_set = self._get_recommended_test_set(summary, description)
        if recommended_set and recommended_set != current_test_set:
            analysis['misclassified'] = True
            analysis['recommended_test_set'] = recommended_set
        
        # Vérifier les labels obligatoires
        missing_labels = self._check_mandatory_labels(component, labels)
        if missing_labels:
            analysis['missing_labels'] = missing_labels
        
        # Vérifier la référence 3GPP
        gpp_ref = self._get_3gpp_reference(summary, description)
        if gpp_ref:
            analysis['gpp_reference'] = gpp_ref
            if '3GPP' not in ' '.join(labels) and gpp_ref not in ' '.join(labels):
                analysis['missing_3gpp'] = True
        
        # Vérifier la cohérence des labels
        label_issues = self._check_label_consistency(labels, summary, description)
        if label_issues:
            analysis['label_issues'] = label_issues
        
        return analysis
    
    def _get_recommended_test_set(self, summary: str, description: str) -> str:
        """
        Détermine le Test Set recommandé basé sur le contenu
        """
        text = f"{summary} {description}".lower()
        
        # Vérifier chaque Test Set recommandé
        for test_set, keywords in self.recommended_test_sets.items():
            for keyword in keywords:
                if keyword.lower() in text:
                    return test_set
        
        return ''
    
    def _check_mandatory_labels(self, component: str, labels: List[str]) -> List[str]:
        """
        Vérifie les labels obligatoires selon le composant
        """
        if component not in self.mandatory_labels:
            return []
        
        labels_str = ' '.join(labels).lower()
        missing = []
        
        for mandatory_label in self.mandatory_labels[component]:
            if mandatory_label.lower() not in labels_str:
                missing.append(mandatory_label)
        
        return missing
    
    def _get_3gpp_reference(self, summary: str, description: str) -> str:
        """
        Détermine la référence 3GPP appropriée
        """
        text = f"{summary} {description}".lower()
        
        for feature, ts_ref in self.gpp_references.items():
            if feature.lower() in text:
                return ts_ref
        
        return ''
    
    def _check_label_consistency(self, labels: List[str], summary: str, description: str) -> List[str]:
        """
        Vérifie la cohérence des labels avec le contenu
        """
        issues = []
        text = f"{summary} {description}".lower()
        
        # Vérifier les contradictions
        if 'functional' in ' '.join(labels).lower() and 'nonfunctional' in ' '.join(labels).lower():
            issues.append("Contradiction: Functional ET NonFunctional")
        
        # Vérifier les labels techniques manquants
        if 's6a' in text and 'S6a' not in ' '.join(labels):
            issues.append("Label technique manquant: S6a")
        
        if 'map' in text and 'MAP' not in ' '.join(labels):
            issues.append("Label technique manquant: MAP")
        
        if 'diameter' in text and 'Diameter' not in ' '.join(labels):
            issues.append("Label technique manquant: Diameter")
        
        # Vérifier les doublons
        label_counts = Counter(labels)
        for label, count in label_counts.items():
            if count > 1:
                issues.append(f"Label dupliqué: {label} ({count} fois)")
        
        return issues
    
    def _generate_recommendations(self, results: Dict) -> List[Dict]:
        """
        Génère les recommandations d'amélioration
        """
        recommendations = []
        
        # Recommandations pour les tests mal classés
        if results['misclassified_tests']:
            recommendations.append({
                'type': 'Réaffectation Test Sets',
                'priority': 'HIGH',
                'count': len(results['misclassified_tests']),
                'description': 'Tests à réaffecter dans les Test Sets appropriés',
                'actions': [f"Déplacer '{test['summary']}' vers '{test['recommended_test_set']}'" 
                           for test in results['misclassified_tests'][:5]]
            })
        
        # Recommandations pour les labels manquants
        if results['missing_labels']:
            recommendations.append({
                'type': 'Labels Manquants',
                'priority': 'MEDIUM',
                'count': len(results['missing_labels']),
                'description': 'Tests avec labels obligatoires manquants',
                'actions': [f"Ajouter labels {test['missing_labels']} à '{test['summary']}'" 
                           for test in results['missing_labels'][:5]]
            })
        
        # Recommandations pour les références 3GPP
        if results['missing_3gpp_refs']:
            recommendations.append({
                'type': 'Références 3GPP',
                'priority': 'LOW',
                'count': len(results['missing_3gpp_refs']),
                'description': 'Tests nécessitant des références 3GPP',
                'actions': [f"Ajouter référence {test['gpp_reference']} à '{test['summary']}'" 
                           for test in results['missing_3gpp_refs'][:5]]
            })
        
        return recommendations
    
    def _generate_summary(self, results: Dict) -> Dict:
        """
        Génère un résumé de l'analyse
        """
        total = results['total_tests']
        
        return {
            'total_tests': total,
            'tests_with_issues': len(results['misclassified_tests']) + len(results['missing_labels']),
            'compliance_rate': round((total - len(results['misclassified_tests'])) / total * 100, 1) if total > 0 else 0,
            'label_completeness': round((total - len(results['missing_labels'])) / total * 100, 1) if total > 0 else 0,
            'gpp_coverage': round((total - len(results['missing_3gpp_refs'])) / total * 100, 1) if total > 0 else 0,
            'priority_actions': len([r for r in results.get('recommended_actions', []) if r.get('priority') == 'HIGH'])
        }
    
    def generate_report(self, results: Dict, output_file: str = None) -> str:
        """
        Génère un rapport détaillé
        """
        report = []
        
        # En-tête
        report.append("="*80)
        report.append("RAPPORT D'ANALYSE DES TEST SETS - VALIDATION CORE NETWORK")
        report.append("="*80)
        
        # Résumé exécutif
        summary = results['summary']
        report.append(f"\n📊 RÉSUMÉ EXÉCUTIF")
        report.append(f"   • Total tests analysés: {summary['total_tests']}")
        report.append(f"   • Taux de conformité Test Sets: {summary['compliance_rate']}%")
        report.append(f"   • Complétude des labels: {summary['label_completeness']}%")
        report.append(f"   • Couverture 3GPP: {summary['gpp_coverage']}%")
        report.append(f"   • Actions prioritaires: {summary['priority_actions']}")
        
        # Recommandations
        report.append(f"\n🎯 RECOMMANDATIONS PRIORITAIRES")
        for rec in results.get('recommended_actions', []):
            report.append(f"\n{rec['type']} (Priorité: {rec['priority']})")
            report.append(f"   Nombre de tests concernés: {rec['count']}")
            report.append(f"   Description: {rec['description']}")
            report.append("   Actions recommandées:")
            for action in rec['actions']:
                report.append(f"     - {action}")
        
        # Détails des problèmes
        if results['misclassified_tests']:
            report.append(f"\n❌ TESTS MAL CLASSÉS ({len(results['misclassified_tests'])})")
            for test in results['misclassified_tests'][:10]:  # Top 10
                report.append(f"   • {test['summary']}")
                report.append(f"     Actuel: {test['current_test_set']}")
                report.append(f"     Recommandé: {test['recommended_test_set']}")
        
        if results['missing_labels']:
            report.append(f"\n⚠️  LABELS MANQUANTS ({len(results['missing_labels'])})")
            for test in results['missing_labels'][:10]:  # Top 10
                report.append(f"   • {test['summary']}")
                report.append(f"     Labels manquants: {', '.join(test['missing_labels'])}")
        
        # Nouveaux Test Sets recommandés
        report.append(f"\n💡 NOUVEAUX TEST SETS RECOMMANDÉS")
        new_sets = [
            "Backup & Restore Operations",
            "Security/Hardening", 
            "Security/Vulnerability",
            "Security/RBAC",
            "OAM & Provisioning",
            "Performance Testing",
            "Vendor Features/Huawei",
            "Vendor Features/ZTE"
        ]
        for test_set in new_sets:
            report.append(f"   • {test_set}")
        
        report_text = "\n".join(report)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
        
        return report_text

def main():
    """
    Fonction principale pour tester l'analyseur
    """
    analyzer = TestSetAnalyzer()
    
    # Analyser le fichier d'exemple
    if os.path.exists('example_tests_separate_labels.csv'):
        print("🔍 Analyse du fichier example_tests_separate_labels.csv...")
        results = analyzer.analyze_test_file('example_tests_separate_labels.csv')
        
        # Générer le rapport
        report = analyzer.generate_report(results, 'test_set_analysis_report.txt')
        print(report)
        
        print(f"\n✅ Rapport sauvegardé: test_set_analysis_report.txt")
    else:
        print("❌ Fichier example_tests_separate_labels.csv non trouvé")
        print("💡 Exécutez d'abord: python test_web_separate_labels.py")

if __name__ == "__main__":
    import os
    main()
