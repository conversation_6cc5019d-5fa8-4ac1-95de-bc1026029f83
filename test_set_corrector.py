#!/usr/bin/env python3
"""
Correcteur automatique des Test Sets et Labels
Applique les recommandations d'analyse pour optimiser la cohérence
"""

import pandas as pd
import re
from typing import Dict, List
from test_set_analyzer import TestSetAnalyzer

class TestSetCorrector:
    """
    Correcteur automatique pour les Test Sets et Labels
    """
    
    def __init__(self):
        self.analyzer = TestSetAnalyzer()
        
        # Règles de correction automatique
        self.correction_rules = {
            # Corrections de Test Sets
            'test_set_corrections': {
                'backup': 'Backup and Restore Operations',
                'restore': 'Backup and Restore Operations',
                'recovery': 'Backup and Restore Operations',
                'vulnerability': 'Security Vulnerability Assessment',
                'cis': 'Security CIS Compliance',
                'rbac': 'Security RBAC Management',
                'provisioning': 'OAM and Provisioning',
                'configuration': 'OAM and Provisioning',
                'performance': 'Performance Testing',
                'load': 'Performance Testing',
                'resiliency': 'Resiliency and Redundancy',
                'redundancy': 'Resiliency and Redundancy'
            },
            
            # Ajouts de labels automatiques
            'auto_labels': {
                's6a': ['4G', 'Diameter', 'S6a'],
                'map': ['2G', 'MAP'],
                'hlr': ['2G', 'MAP', 'HLR'],
                'hss': ['4G', 'Diameter'],
                'ims': ['4G', 'IMS'],
                'camel': ['2G', 'MAP', 'CAMEL'],
                'backup': ['NonFunctional', 'BackupRestore'],
                'restore': ['NonFunctional', 'BackupRestore'],
                'security': ['NonFunctional', 'Security'],
                'performance': ['NonFunctional', 'Performance'],
                'oam': ['Functional', 'OAM'],
                'provisioning': ['Functional', 'OAM']
            },
            
            # Références 3GPP automatiques
            'gpp_auto_refs': {
                'call forwarding': '3GPP_TS_23.082',
                'call barring': '3GPP_TS_23.088',
                'camel': '3GPP_TS_23.078',
                'epc': '3GPP_TS_23.401',
                'ims': '3GPP_TS_23.228',
                's6a': '3GPP_TS_29.272',
                'map': '3GPP_TS_29.002',
                'diameter': '3GPP_TS_29.229',
                'volte': '3GPP_TS_23.216',
                'srvcc': '3GPP_TS_23.216'
            }
        }
    
    def correct_test_file(self, input_file: str, output_file: str, apply_corrections: bool = True) -> Dict:
        """
        Corrige un fichier de tests selon les règles définies
        """
        df = pd.read_csv(input_file)
        corrections_applied = {
            'test_set_changes': 0,
            'labels_added': 0,
            'gpp_refs_added': 0,
            'duplicates_removed': 0,
            'details': []
        }
        
        # Analyser d'abord le fichier
        analysis = self.analyzer.analyze_test_file(input_file)
        
        if not apply_corrections:
            return self._preview_corrections(df, analysis)
        
        # Appliquer les corrections
        for index, row in df.iterrows():
            corrections = self._correct_single_test(row, index)
            
            if corrections['test_set_changed']:
                df.at[index, 'Test Set'] = corrections['new_test_set']
                corrections_applied['test_set_changes'] += 1
                corrections_applied['details'].append(
                    f"Test {index+1}: Test Set '{corrections['old_test_set']}' → '{corrections['new_test_set']}'"
                )
            
            if corrections['labels_added']:
                # Ajouter les nouveaux labels dans les colonnes Labels disponibles
                self._add_labels_to_row(df, index, corrections['new_labels'])
                corrections_applied['labels_added'] += len(corrections['new_labels'])
                corrections_applied['details'].append(
                    f"Test {index+1}: Ajout labels {corrections['new_labels']}"
                )
            
            if corrections['gpp_ref_added']:
                # Ajouter la référence 3GPP comme label
                self._add_labels_to_row(df, index, [corrections['gpp_ref']])
                corrections_applied['gpp_refs_added'] += 1
                corrections_applied['details'].append(
                    f"Test {index+1}: Ajout référence {corrections['gpp_ref']}"
                )
        
        # Sauvegarder le fichier corrigé
        df.to_csv(output_file, index=False)
        
        return corrections_applied
    
    def _correct_single_test(self, row: pd.Series, index: int) -> Dict:
        """
        Corrige un test individuel
        """
        summary = str(row.get('Summary', '')).lower()
        description = str(row.get('Description', '')).lower()
        current_test_set = str(row.get('Test Set', ''))
        
        # Collecter les labels existants
        existing_labels = []
        for col in row.index:
            if col == 'Labels' and pd.notna(row[col]) and row[col] != '':
                existing_labels.append(str(row[col]))
        
        corrections = {
            'test_set_changed': False,
            'old_test_set': current_test_set,
            'new_test_set': current_test_set,
            'labels_added': False,
            'new_labels': [],
            'gpp_ref_added': False,
            'gpp_ref': ''
        }
        
        text = f"{summary} {description}"
        
        # Corriger le Test Set
        new_test_set = self._get_corrected_test_set(text, current_test_set)
        if new_test_set != current_test_set:
            corrections['test_set_changed'] = True
            corrections['new_test_set'] = new_test_set
        
        # Ajouter les labels manquants
        new_labels = self._get_missing_labels(text, existing_labels)
        if new_labels:
            corrections['labels_added'] = True
            corrections['new_labels'] = new_labels
        
        # Ajouter la référence 3GPP
        gpp_ref = self._get_gpp_reference(text, existing_labels)
        if gpp_ref:
            corrections['gpp_ref_added'] = True
            corrections['gpp_ref'] = gpp_ref
        
        return corrections
    
    def _get_corrected_test_set(self, text: str, current_test_set: str) -> str:
        """
        Détermine le Test Set corrigé
        """
        # Vérifier les règles de correction
        for keyword, correct_test_set in self.correction_rules['test_set_corrections'].items():
            if keyword in text:
                return correct_test_set
        
        # Utiliser l'analyseur pour une recommandation plus précise
        recommended = self.analyzer._get_recommended_test_set(text, "")
        if recommended:
            return recommended
        
        return current_test_set
    
    def _get_missing_labels(self, text: str, existing_labels: List[str]) -> List[str]:
        """
        Détermine les labels manquants à ajouter
        """
        existing_str = ' '.join(existing_labels).lower()
        new_labels = []
        
        # Vérifier les règles d'ajout automatique
        for keyword, auto_labels in self.correction_rules['auto_labels'].items():
            if keyword in text:
                for label in auto_labels:
                    if label.lower() not in existing_str and label not in new_labels:
                        new_labels.append(label)
        
        # Ajouter les labels de base si manquants
        if 'functional' not in existing_str and 'nonfunctional' not in existing_str:
            if any(word in text for word in ['backup', 'restore', 'security', 'performance']):
                new_labels.append('NonFunctional')
            else:
                new_labels.append('Functional')
        
        if 'system' not in existing_str:
            new_labels.append('System')
        
        if 'manual' not in existing_str:
            new_labels.append('Manual')
        
        if 'regression' not in existing_str:
            new_labels.append('Regression')
        
        return new_labels
    
    def _get_gpp_reference(self, text: str, existing_labels: List[str]) -> str:
        """
        Détermine la référence 3GPP à ajouter
        """
        existing_str = ' '.join(existing_labels).lower()
        
        # Vérifier si une référence 3GPP est déjà présente
        if '3gpp' in existing_str or 'ts_' in existing_str:
            return ''
        
        # Chercher la référence appropriée
        for keyword, gpp_ref in self.correction_rules['gpp_auto_refs'].items():
            if keyword in text:
                return gpp_ref
        
        return ''
    
    def _add_labels_to_row(self, df: pd.DataFrame, row_index: int, new_labels: List[str]):
        """
        Ajoute des labels dans les colonnes Labels disponibles
        """
        # Trouver les colonnes Labels
        label_columns = [i for i, col in enumerate(df.columns) if col == 'Labels']
        
        # Trouver les colonnes vides
        for label in new_labels:
            for col_idx in label_columns:
                current_value = df.iloc[row_index, col_idx]
                if pd.isna(current_value) or current_value == '':
                    df.iloc[row_index, col_idx] = label
                    break
    
    def _preview_corrections(self, df: pd.DataFrame, analysis: Dict) -> Dict:
        """
        Prévisualise les corrections sans les appliquer
        """
        preview = {
            'total_tests': len(df),
            'proposed_changes': [],
            'summary': {
                'test_set_changes': len(analysis['misclassified_tests']),
                'label_additions': len(analysis['missing_labels']),
                'gpp_additions': len(analysis['missing_3gpp_refs'])
            }
        }
        
        # Détailler les changements proposés
        for test in analysis['misclassified_tests'][:10]:
            preview['proposed_changes'].append({
                'type': 'Test Set Change',
                'test': test['summary'],
                'from': test['current_test_set'],
                'to': test['recommended_test_set']
            })
        
        for test in analysis['missing_labels'][:10]:
            preview['proposed_changes'].append({
                'type': 'Label Addition',
                'test': test['summary'],
                'labels': test['missing_labels']
            })
        
        return preview
    
    def generate_correction_report(self, corrections: Dict, output_file: str = None) -> str:
        """
        Génère un rapport des corrections appliquées
        """
        report = []
        
        report.append("="*80)
        report.append("RAPPORT DE CORRECTIONS APPLIQUÉES")
        report.append("="*80)
        
        report.append(f"\n📊 RÉSUMÉ DES CORRECTIONS")
        report.append(f"   • Test Sets modifiés: {corrections['test_set_changes']}")
        report.append(f"   • Labels ajoutés: {corrections['labels_added']}")
        report.append(f"   • Références 3GPP ajoutées: {corrections['gpp_refs_added']}")
        report.append(f"   • Doublons supprimés: {corrections['duplicates_removed']}")
        
        if corrections['details']:
            report.append(f"\n📝 DÉTAIL DES MODIFICATIONS")
            for detail in corrections['details']:
                report.append(f"   • {detail}")
        
        report.append(f"\n✅ CORRECTIONS TERMINÉES")
        report.append(f"   Le fichier corrigé est prêt pour l'import dans Jira/Xray")
        
        report_text = "\n".join(report)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
        
        return report_text

def main():
    """
    Fonction principale pour tester le correcteur
    """
    corrector = TestSetCorrector()
    
    if not os.path.exists('example_tests_separate_labels.csv'):
        print("❌ Fichier example_tests_separate_labels.csv non trouvé")
        return
    
    print("🔧 Correction automatique des Test Sets et Labels...")
    
    # Prévisualiser les corrections
    preview = corrector.correct_test_file(
        'example_tests_separate_labels.csv', 
        'corrected_tests.csv', 
        apply_corrections=False
    )
    
    print(f"\n📋 PRÉVISUALISATION DES CORRECTIONS")
    print(f"   • Test Sets à modifier: {preview['summary']['test_set_changes']}")
    print(f"   • Labels à ajouter: {preview['summary']['label_additions']}")
    print(f"   • Références 3GPP à ajouter: {preview['summary']['gpp_additions']}")
    
    # Demander confirmation
    response = input("\n❓ Appliquer les corrections ? (o/n): ")
    
    if response.lower() in ['o', 'oui', 'y', 'yes']:
        # Appliquer les corrections
        corrections = corrector.correct_test_file(
            'example_tests_separate_labels.csv',
            'example_tests_corrected.csv',
            apply_corrections=True
        )
        
        # Générer le rapport
        report = corrector.generate_correction_report(corrections, 'corrections_report.txt')
        print(report)
        
        print(f"\n✅ Fichier corrigé sauvegardé: example_tests_corrected.csv")
        print(f"📄 Rapport sauvegardé: corrections_report.txt")
    else:
        print("\n❌ Corrections annulées")

if __name__ == "__main__":
    import os
    main()
