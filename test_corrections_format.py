#!/usr/bin/env python3
"""
Test des corrections de format :
1. Toutes les colonnes Labels doivent s'appeler "Labels"
2. Pas de valeurs "nan" dans le fichier final
"""

import pandas as pd
import os
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced
from test_set_csv_generator import TestSetCSVGenerator

def print_header(title):
    print("\n" + "="*70)
    print(f"  {title}")
    print("="*70)

def test_colonnes_labels_identiques():
    """
    Test que toutes les colonnes Labels ont le même nom
    """
    print_header("🔧 TEST - COLONNES LABELS IDENTIQUES")
    
    # Test 1 : Enrichissement des tests
    print("📊 Test enrichissement avec colonnes Labels identiques...")
    
    tool = TestEnrichmentToolEnhanced()
    tool.enrich_csv_enhanced('example_tests.csv', 'test_labels_identiques.csv', 'separate')
    
    # Lire le fichier brut pour vérifier l'en-tête
    with open('test_labels_identiques.csv', 'r', encoding='utf-8') as f:
        header_line = f.readline().strip()
    
    print(f"En-tête CSV : {header_line}")
    
    # Compter les colonnes "Labels"
    columns = header_line.split(',')
    labels_columns = [col for col in columns if col == 'Labels']
    
    print(f"Colonnes 'Labels' trouvées : {len(labels_columns)}")
    print(f"Toutes identiques : {'✅ OUI' if len(labels_columns) >= 8 else '❌ NON'}")
    
    # Test 2 : Génération CSV Test Sets
    print(f"\n📋 Test génération CSV Test Sets...")
    
    generator = TestSetCSVGenerator()
    generator.generate_test_sets_csv('test_testsets_identiques.csv')
    
    # Lire le fichier brut
    with open('test_testsets_identiques.csv', 'r', encoding='utf-8-sig') as f:
        header_line = f.readline().strip()
    
    print(f"En-tête CSV Test Sets : {header_line}")
    
    # Vérifier le format attendu
    expected_format = "Summary;Status;Labels;Labels;Labels;Labels;Labels;Labels;Labels;Labels;Labels;Labels;;Description;Comment"
    format_correct = header_line == expected_format
    
    print(f"Format attendu : {expected_format}")
    print(f"Format correct : {'✅ OUI' if format_correct else '❌ NON'}")
    
    # Nettoyer
    os.remove('test_labels_identiques.csv')
    os.remove('test_testsets_identiques.csv')
    
    return len(labels_columns) >= 8 and format_correct

def test_suppression_nan():
    """
    Test que les valeurs "nan" sont supprimées
    """
    print_header("🧹 TEST - SUPPRESSION DES VALEURS NAN")
    
    # Créer un fichier avec des valeurs manquantes
    data = {
        'Summary': ['Test 1', 'Test 2', 'Test 3'],
        'Description': ['Desc 1', None, 'Desc 3'],  # Une valeur None
        'Component/s': ['', 'Comp2', ''],  # Valeurs vides
        'Action': ['Action 1', 'Action 2', None],  # Une valeur None
        'Expected Result': ['Result 1', '', 'Result 3']  # Valeur vide
    }
    
    df_with_nan = pd.DataFrame(data)
    df_with_nan.to_csv('test_avec_nan.csv', index=False)
    
    print("📁 Fichier créé avec valeurs manquantes...")
    print(f"Valeurs None dans le fichier original : {df_with_nan.isnull().sum().sum()}")
    
    # Enrichir le fichier
    tool = TestEnrichmentToolEnhanced()
    tool.enrich_csv_enhanced('test_avec_nan.csv', 'test_sans_nan.csv', 'separate')
    
    # Lire le fichier enrichi et vérifier qu'il n'y a pas de "nan"
    with open('test_sans_nan.csv', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Compter les occurrences de "nan"
    nan_count = content.lower().count('nan')
    none_count = content.lower().count('none')
    null_count = content.lower().count('null')
    
    print(f"\n📊 Vérification du fichier enrichi :")
    print(f"Occurrences de 'nan' : {nan_count}")
    print(f"Occurrences de 'none' : {none_count}")
    print(f"Occurrences de 'null' : {null_count}")
    
    # Vérifier avec pandas aussi
    df_enriched = pd.read_csv('test_sans_nan.csv')
    pandas_nan_count = df_enriched.isnull().sum().sum()
    
    print(f"Valeurs NaN détectées par pandas : {pandas_nan_count}")
    
    # Vérifier quelques cellules spécifiques
    print(f"\n🔍 Vérification de cellules spécifiques :")
    for i in range(min(3, len(df_enriched))):
        for col in df_enriched.columns:
            value = df_enriched.iloc[i][col]
            if pd.isna(value):
                print(f"   Ligne {i}, Colonne {col} : ENCORE NaN !")
            elif str(value).lower() in ['nan', 'none', 'null']:
                print(f"   Ligne {i}, Colonne {col} : Valeur '{value}' détectée !")
    
    success = nan_count == 0 and none_count == 0 and null_count == 0
    print(f"\nRésultat : {'✅ AUCUNE valeur nan/none/null' if success else '❌ Valeurs indésirables trouvées'}")
    
    # Nettoyer
    os.remove('test_avec_nan.csv')
    os.remove('test_sans_nan.csv')
    
    return success

def test_format_complet():
    """
    Test du format complet avec un exemple réel
    """
    print_header("📋 TEST - FORMAT COMPLET")
    
    # Enrichir le fichier d'exemple
    tool = TestEnrichmentToolEnhanced()
    tool.enrich_csv_enhanced('example_tests.csv', 'test_format_final.csv', 'separate')
    
    # Analyser le fichier final
    print("📊 Analyse du fichier enrichi final :")
    
    # Lire l'en-tête brut
    with open('test_format_final.csv', 'r', encoding='utf-8') as f:
        header_line = f.readline().strip()
        first_data_line = f.readline().strip()
    
    print(f"En-tête : {header_line}")
    print(f"Première ligne : {first_data_line[:100]}...")
    
    # Compter les colonnes Labels
    columns = header_line.split(',')
    labels_columns = [col for col in columns if col == 'Labels']
    
    print(f"\n✅ Colonnes 'Labels' : {len(labels_columns)}")
    
    # Vérifier les valeurs de la première ligne
    values = first_data_line.split(',')
    empty_values = sum(1 for val in values if val == '')
    nan_values = sum(1 for val in values if val.lower() in ['nan', 'none', 'null'])
    
    print(f"✅ Valeurs vides (OK) : {empty_values}")
    print(f"❌ Valeurs 'nan' (à éviter) : {nan_values}")
    
    # Lire avec pandas pour analyse détaillée
    df = pd.read_csv('test_format_final.csv')
    
    print(f"\n📊 Statistiques pandas :")
    print(f"   Tests : {len(df)}")
    print(f"   Colonnes : {len(df.columns)}")
    print(f"   Colonnes Labels (pandas) : {len([col for col in df.columns if 'Labels' in col])}")
    
    # Afficher un exemple de test enrichi
    print(f"\n🎯 Exemple de test enrichi :")
    print(f"   Summary : {df.iloc[0]['Summary']}")
    print(f"   Component : {df.iloc[0]['Component/s']}")
    print(f"   Test Set : {df.iloc[0]['Test Set']}")
    
    # Collecter les labels du premier test
    first_test_labels = []
    for col in df.columns:
        if 'Labels' in col:
            value = df.iloc[0][col]
            if pd.notna(value) and str(value) != '' and str(value).lower() not in ['nan', 'none', 'null']:
                first_test_labels.append(str(value))
    
    print(f"   Labels : {first_test_labels}")
    print(f"   Nombre de labels : {len(first_test_labels)}")
    
    # Nettoyer
    os.remove('test_format_final.csv')
    
    return len(labels_columns) >= 8 and nan_values == 0

def main():
    """
    Test complet des corrections de format
    """
    print("🧪 TEST DES CORRECTIONS DE FORMAT")
    
    # Test 1 : Colonnes Labels identiques
    test1 = test_colonnes_labels_identiques()
    
    # Test 2 : Suppression des valeurs "nan"
    test2 = test_suppression_nan()
    
    # Test 3 : Format complet
    test3 = test_format_complet()
    
    print_header("📊 RÉSULTATS FINAUX")
    
    print(f"✅ Colonnes Labels identiques : {'OK' if test1 else 'ÉCHEC'}")
    print(f"✅ Suppression valeurs 'nan' : {'OK' if test2 else 'ÉCHEC'}")
    print(f"✅ Format complet correct : {'OK' if test3 else 'ÉCHEC'}")
    
    if test1 and test2 and test3:
        print(f"\n🎉 TOUTES LES CORRECTIONS FONCTIONNENT !")
        print(f"✅ Colonnes 'Labels' toutes identiques")
        print(f"✅ Aucune valeur 'nan' dans les fichiers")
        print(f"✅ Format parfait pour import")
        print(f"🎯 Prêt pour utilisation en production")
    else:
        print(f"\n⚠️  CERTAINES CORRECTIONS NÉCESSITENT DES AJUSTEMENTS")
        print(f"💡 Vérifiez les erreurs ci-dessus")

if __name__ == "__main__":
    main()
