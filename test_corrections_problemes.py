#!/usr/bin/env python3
"""
Test des corrections des problèmes identifiés
"""

import pandas as pd
import os
from json_steps_processor import J<PERSON>NStepsProcessor
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def print_header(title):
    print("\n" + "="*70)
    print(f"  {title}")
    print("="*70)

def test_lecture_csv_avec_point_virgule():
    """
    Test de la lecture CSV avec séparateur point-virgule
    """
    print_header("🔧 TEST LECTURE CSV AVEC POINT-VIRGULE")
    
    # Créer un fichier avec des données JSON complexes et point-virgule
    data = {
        'Summary': ['Test 1', 'Test 2'],
        'Description': ['Desc 1', 'Desc 2'],
        'Component/s': ['', ''],
        'Custom field (Manual Test Steps)': [
            '[]',  # JSON vide
            '[{"id":3844657,"index":1,"fields":{"Action":"Verify SOAP provisioning","Data":"","Expected Result":"SOAP command success"}}]'
        ],
        'Action': ['', ''],
        'Data': ['', ''],
        'Expected Result': ['', '']
    }
    
    df = pd.DataFrame(data)
    
    # Sauvegarder avec point-virgule et quoting
    import csv
    with open('test_semicolon.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=df.columns, delimiter=';', quoting=csv.QUOTE_ALL)
        writer.writeheader()
        for _, row in df.iterrows():
            writer.writerow(row.to_dict())
    
    print("📁 Fichier créé avec séparateur point-virgule")
    
    # Vérifier le contenu
    with open('test_semicolon.csv', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"En-tête : {lines[0].strip()}")
    print(f"Séparateurs ';' dans l'en-tête : {lines[0].count(';')}")
    
    # Test de lecture avec l'outil d'enrichissement
    try:
        tool = TestEnrichmentToolEnhanced()
        tool.enrich_csv_enhanced('test_semicolon.csv', 'test_semicolon_enriched.csv', 'separate')
        
        print("✅ Lecture et enrichissement réussis")
        
        # Vérifier le fichier de sortie
        if os.path.exists('test_semicolon_enriched.csv'):
            with open('test_semicolon_enriched.csv', 'r', encoding='utf-8') as f:
                output_lines = f.readlines()
            
            print(f"Fichier enrichi : {len(output_lines)} lignes")
            print(f"Séparateur point-virgule conservé : {';' in output_lines[0]}")
            
            # Nettoyer
            os.remove('test_semicolon_enriched.csv')
            success = True
        else:
            print("❌ Fichier enrichi non généré")
            success = False
    
    except Exception as e:
        print(f"❌ Erreur lors de l'enrichissement : {e}")
        success = False
    
    # Nettoyer
    os.remove('test_semicolon.csv')
    
    return success

def test_extraction_avec_json_vide():
    """
    Test de l'extraction avec JSON vide et JSON valide
    """
    print_header("📋 TEST EXTRACTION AVEC JSON VIDE")
    
    # Créer un fichier avec JSON vide et JSON valide
    data = {
        'Summary': ['Test File Storage DB', 'SOAP provisioning'],
        'Description': ['Test storage', 'Test SOAP'],
        'Component/s': ['', ''],
        'Custom field (Manual Test Steps)': [
            '[]',  # JSON vide comme dans votre exemple
            '[{"id":3844657,"index":1,"fields":{"Action":"Verify that user provisioning can be executed through SOAP command","Data":"","Expected Result":"User provisioning operate with success with SOAP command"}}]'
        ]
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_mixed_json.csv', index=False)
    
    print("📁 Fichier créé avec JSON vide et JSON valide")
    
    # Test d'extraction
    processor = JSONStepsProcessor()
    stats = processor.process_csv_file('test_mixed_json.csv', 'test_mixed_extracted.csv')
    
    print(f"📊 Statistiques d'extraction :")
    print(f"   Tests traités : {stats['processed']}")
    print(f"   Étapes extraites : {stats['extracted']}")
    print(f"   Erreurs : {stats['errors']}")
    
    # Vérifier le fichier généré
    if os.path.exists('test_mixed_extracted.csv'):
        with open('test_mixed_extracted.csv', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📁 Fichier extrait : {len(lines)} lignes")
        print(f"Séparateur point-virgule : {';' in lines[0]}")
        
        # Vérifier le contenu des lignes
        if len(lines) >= 3:
            print(f"\n📋 Contenu extrait :")
            # Analyser la première ligne (JSON vide)
            line1_parts = lines[1].split(';')
            print(f"   Test 1 (JSON vide) : Action = '{line1_parts[-3].strip('\"')}' (devrait être vide)")
            
            # Analyser la deuxième ligne (JSON valide)
            line2_parts = lines[2].split(';')
            print(f"   Test 2 (JSON valide) : Action = '{line2_parts[-3].strip('\"')[:50]}...'")
        
        # Nettoyer
        os.remove('test_mixed_extracted.csv')
        success = stats['processed'] == 2 and stats['errors'] == 0
    else:
        print("❌ Fichier extrait non généré")
        success = False
    
    # Nettoyer
    os.remove('test_mixed_json.csv')
    
    return success

def test_workflow_complet_avec_corrections():
    """
    Test du workflow complet avec les corrections
    """
    print_header("🚀 TEST WORKFLOW COMPLET AVEC CORRECTIONS")
    
    # Créer un fichier de test réaliste
    data = {
        'Summary': ['Test Storage', 'SOAP Provisioning'],
        'Description': ['Test file storage', 'Test SOAP provisioning'],
        'Component/s': ['', ''],
        'Custom field (Manual Test Steps)': [
            '[]',  # JSON vide
            '[{"id":3844657,"index":1,"fields":{"Action":"Verify SOAP provisioning","Data":"Test data","Expected Result":"Success"}}]'
        ]
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_workflow_complete.csv', index=False)
    
    print("📁 Fichier de test créé")
    
    # Étape 1 : Extraction JSON
    processor = JSONStepsProcessor()
    stats = processor.process_csv_file('test_workflow_complete.csv', 'test_workflow_steps.csv')
    
    print(f"🔧 Extraction : {stats['processed']} tests → {stats['extracted']} étapes")
    
    # Vérifier que le fichier avec étapes existe
    if not os.path.exists('test_workflow_steps.csv'):
        print("❌ Fichier avec étapes non généré")
        return False
    
    # Étape 2 : Enrichissement
    try:
        tool = TestEnrichmentToolEnhanced()
        tool.enrich_csv_enhanced('test_workflow_steps.csv', 'test_workflow_final.csv', 'separate')
        
        print("🎯 Enrichissement réussi")
        
        # Vérifier le fichier final
        if os.path.exists('test_workflow_final.csv'):
            with open('test_workflow_final.csv', 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"📁 Fichier final : {len(lines)} lignes")
            print(f"Séparateur point-virgule : {';' in lines[0]}")
            
            # Vérifier les colonnes
            header = lines[0]
            essential_columns = ['Summary', 'Action', 'Data', 'Expected Result', 'Component/s', 'Test Set']
            labels_count = header.count('Labels')
            
            print(f"Colonnes Labels : {labels_count}")
            print(f"Colonnes essentielles : {'✅ OK' if all(col in header for col in essential_columns) else '❌ Manquantes'}")
            
            # Nettoyer
            os.remove('test_workflow_final.csv')
            success = labels_count >= 8 and len(lines) >= 3
        else:
            print("❌ Fichier final non généré")
            success = False
    
    except Exception as e:
        print(f"❌ Erreur lors de l'enrichissement : {e}")
        success = False
    
    # Nettoyer
    os.remove('test_workflow_complete.csv')
    if os.path.exists('test_workflow_steps.csv'):
        os.remove('test_workflow_steps.csv')
    
    return success

def main():
    """
    Test complet des corrections
    """
    print("🧪 TEST DES CORRECTIONS DES PROBLÈMES")
    
    # Test 1 : Lecture CSV avec point-virgule
    test1 = test_lecture_csv_avec_point_virgule()
    
    # Test 2 : Extraction avec JSON vide
    test2 = test_extraction_avec_json_vide()
    
    # Test 3 : Workflow complet
    test3 = test_workflow_complet_avec_corrections()
    
    print_header("📊 RÉSULTATS DES CORRECTIONS")
    
    print(f"✅ Lecture CSV point-virgule : {'OK' if test1 else 'ÉCHEC'}")
    print(f"✅ Extraction JSON vide/valide : {'OK' if test2 else 'ÉCHEC'}")
    print(f"✅ Workflow complet : {'OK' if test3 else 'ÉCHEC'}")
    
    if test1 and test2 and test3:
        print(f"\n🎉 TOUTES LES CORRECTIONS FONCTIONNENT !")
        print(f"✅ Lecture robuste des CSV avec point-virgule")
        print(f"✅ Gestion correcte des JSON vides et valides")
        print(f"✅ Workflow complet opérationnel")
        print(f"✅ Téléchargement des fichiers corrigé")
        print(f"🎯 Les problèmes identifiés sont résolus")
    else:
        print(f"\n⚠️  CERTAINES CORRECTIONS NÉCESSITENT DES AJUSTEMENTS")

if __name__ == "__main__":
    main()
