#!/usr/bin/env python3
"""
Test de la version améliorée avec correction intégrée
"""

import pandas as pd
import os
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def print_header(title):
    print("\n" + "="*70)
    print(f"  {title}")
    print("="*70)

def test_enhanced_enrichment():
    """
    Test l'enrichissement amélioré avec correction intégrée
    """
    print_header("🚀 TEST ENRICHISSEMENT AMÉLIORÉ - CORRECTION INTÉGRÉE")
    
    if not os.path.exists('example_tests.csv'):
        print("❌ Fichier example_tests.csv non trouvé")
        return False
    
    # Test format standard
    print("\n📊 Test Format Standard...")
    tool = TestEnrichmentToolEnhanced()
    tool.enrich_csv_enhanced('example_tests.csv', 'test_enhanced_standard.csv', 'standard')
    
    # Test format séparé
    print("\n📊 Test Format Jira Optimisé...")
    tool.enrich_csv_enhanced('example_tests.csv', 'test_enhanced_separate.csv', 'separate')
    
    # Analyser les résultats
    print_header("📋 ANALYSE DES RÉSULTATS")
    
    # Format standard
    df_standard = pd.read_csv('test_enhanced_standard.csv')
    print(f"\n✅ FORMAT STANDARD:")
    print(f"   • Tests: {len(df_standard)}")
    print(f"   • Colonnes: {len(df_standard.columns)}")
    print(f"   • Exemple labels: {df_standard.iloc[0]['Labels']}")
    print(f"   • Composant: {df_standard.iloc[0]['Component/s']}")
    print(f"   • Test Set: {df_standard.iloc[0]['Test Set']}")
    
    # Format séparé
    df_separate = pd.read_csv('test_enhanced_separate.csv')
    label_columns = [col for col in df_separate.columns if col == 'Labels']
    
    print(f"\n✅ FORMAT JIRA OPTIMISÉ:")
    print(f"   • Tests: {len(df_separate)}")
    print(f"   • Colonnes: {len(df_separate.columns)}")
    print(f"   • Colonnes Labels: {len(label_columns)}")
    print(f"   • Composant: {df_separate.iloc[0]['Component/s']}")
    print(f"   • Test Set: {df_separate.iloc[0]['Test Set']}")
    
    # Afficher les labels du premier test
    first_test_labels = []
    for i, col in enumerate(df_separate.columns):
        if col == 'Labels':
            value = df_separate.iloc[0, i]
            if pd.notna(value) and value != '':
                first_test_labels.append(value)
    
    print(f"   • Labels séparés: {first_test_labels}")
    print(f"   • Nombre de labels: {len(first_test_labels)}")
    
    # Vérifier les améliorations
    print_header("🎯 VÉRIFICATION DES AMÉLIORATIONS")
    
    improvements = []
    
    # Vérifier les références 3GPP
    gpp_refs = [label for label in first_test_labels if '3GPP' in label]
    if gpp_refs:
        improvements.append(f"✅ Références 3GPP ajoutées: {gpp_refs}")
    
    # Vérifier les labels techniques
    tech_labels = [label for label in first_test_labels if label in ['2G', '4G', 'MAP', 'Diameter', 'S6a', 'IMS']]
    if tech_labels:
        improvements.append(f"✅ Labels techniques ajoutés: {tech_labels}")
    
    # Vérifier les composants
    if df_separate.iloc[0]['Component/s'] != 'COMMON':
        improvements.append(f"✅ Composant spécialisé: {df_separate.iloc[0]['Component/s']}")
    
    # Vérifier les Test Sets
    if 'Services' in df_separate.iloc[0]['Test Set']:
        improvements.append(f"✅ Test Set spécialisé: {df_separate.iloc[0]['Test Set']}")
    
    for improvement in improvements:
        print(f"   {improvement}")
    
    if not improvements:
        print("   ⚠️  Aucune amélioration détectée")
    
    # Comparer avec l'ancien format
    print_header("📊 COMPARAISON AVEC L'ANCIEN FORMAT")
    
    # Utiliser l'ancien outil pour comparaison
    from test_enrichment_tool import TestEnrichmentTool
    old_tool = TestEnrichmentTool()
    old_tool.enrich_csv('example_tests.csv', 'test_old_format.csv')
    
    df_old = pd.read_csv('test_old_format.csv')
    
    print(f"\n📈 ÉVOLUTION:")
    print(f"   ANCIEN FORMAT:")
    print(f"   • Labels: {df_old.iloc[0]['Labels']}")
    print(f"   • Composant: {df_old.iloc[0]['Component/s']}")
    print(f"   • Test Set: {df_old.iloc[0]['Test Set']}")
    
    print(f"\n   NOUVEAU FORMAT AMÉLIORÉ:")
    print(f"   • Labels: {first_test_labels}")
    print(f"   • Composant: {df_separate.iloc[0]['Component/s']}")
    print(f"   • Test Set: {df_separate.iloc[0]['Test Set']}")
    
    # Compter les améliorations
    old_labels = df_old.iloc[0]['Labels'].split(', ')
    new_labels_count = len(first_test_labels)
    old_labels_count = len(old_labels)
    
    print(f"\n   📊 STATISTIQUES:")
    print(f"   • Labels avant: {old_labels_count}")
    print(f"   • Labels après: {new_labels_count}")
    print(f"   • Amélioration: +{new_labels_count - old_labels_count} labels")
    
    # Nettoyer les fichiers de test
    for file in ['test_enhanced_standard.csv', 'test_enhanced_separate.csv', 'test_old_format.csv']:
        if os.path.exists(file):
            os.remove(file)
    
    return len(improvements) > 0

def test_specific_domains():
    """
    Test les domaines spécifiques
    """
    print_header("🔬 TEST DES DOMAINES SPÉCIFIQUES")
    
    # Créer des tests spécifiques pour chaque domaine
    test_data = {
        'Summary': [
            'CFU Test',  # HLR
            'S6a ULR',   # EPC-HSS
            'VoLTE Call', # IMS
            'Security Scan', # Security
            'Backup Operation', # Backup
            'OAM Configuration' # OAM
        ],
        'Description': [
            'Test Call Forwarding Unconditional service',
            'Test Update Location Request on S6a interface',
            'Test VoLTE call setup procedure',
            'Security vulnerability scanning',
            'Test backup operation',
            'Test OAM configuration via SOAP'
        ],
        'Component/s': ['', '', '', '', '', ''],
        'Action': ['Activate CFU', 'Send ULR', 'Setup call', 'Run scan', 'Execute backup', 'Configure'],
        'Expected Result': ['CFU activated', 'ULA received', 'Call established', 'No vulnerabilities', 'Backup created', 'Configuration applied']
    }
    
    df = pd.DataFrame(test_data)
    df.to_csv('test_domains.csv', index=False)
    
    # Enrichir avec la version améliorée
    tool = TestEnrichmentToolEnhanced()
    tool.enrich_csv_enhanced('test_domains.csv', 'test_domains_enriched.csv', 'separate')
    
    # Analyser les résultats par domaine
    df_enriched = pd.read_csv('test_domains_enriched.csv')
    
    expected_results = {
        'CFU Test': {'component': 'HLR', 'test_set': 'HLR Call Forwarding Services'},
        'S6a ULR': {'component': 'EPC-HSS', 'test_set': 'EPC HSS Location Management'},
        'VoLTE Call': {'component': 'IMS-HSS', 'test_set': 'IMS VoLTE Services'},
        'Security Scan': {'component': 'Security', 'test_set': 'Security Vulnerability Assessment'},
        'Backup Operation': {'component': 'COMMON', 'test_set': 'Backup and Restore Operations'},
        'OAM Configuration': {'component': 'OAM', 'test_set': 'OAM and Provisioning'}
    }
    
    all_correct = True
    
    for index, row in df_enriched.iterrows():
        summary = row['Summary']
        component = row['Component/s']
        test_set = row['Test Set']
        
        expected = expected_results.get(summary, {})
        
        print(f"\n🧪 {summary}:")
        print(f"   Composant: {component} {'✅' if component == expected.get('component') else '❌'}")
        print(f"   Test Set: {test_set} {'✅' if test_set == expected.get('test_set') else '❌'}")
        
        if component != expected.get('component') or test_set != expected.get('test_set'):
            all_correct = False
    
    # Nettoyer
    os.remove('test_domains.csv')
    os.remove('test_domains_enriched.csv')
    
    return all_correct

if __name__ == "__main__":
    print("🧪 TEST DE LA VERSION AMÉLIORÉE AVEC CORRECTION INTÉGRÉE")
    
    # Test 1 : Enrichissement amélioré
    test1 = test_enhanced_enrichment()
    
    # Test 2 : Domaines spécifiques
    test2 = test_specific_domains()
    
    print_header("📊 RÉSULTATS FINAUX")
    
    print(f"   Enrichissement amélioré : {'✅ OK' if test1 else '❌ ÉCHEC'}")
    print(f"   Domaines spécifiques : {'✅ OK' if test2 else '❌ ÉCHEC'}")
    
    if test1 and test2:
        print(f"\n🎉 TOUS LES TESTS PASSENT !")
        print(f"✅ La version améliorée avec correction intégrée fonctionne parfaitement")
        print(f"🎯 L'enrichissement est maintenant parfait dès le départ")
    else:
        print(f"\n⚠️  CERTAINS TESTS ÉCHOUENT")
        print(f"💡 Vérifiez les erreurs ci-dessus")
