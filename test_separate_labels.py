#!/usr/bin/env python3
"""
Test de la nouvelle fonctionnalité de colonnes de labels séparées
"""

import pandas as pd
import os
from test_enrichment_tool import TestEnrichmentTool

def create_test_data():
    """
    Crée un fichier de test avec différents types de tests
    """
    test_data = {
        'Summary': [
            'CFU Test',  # HLR - 6 labels attendus
            'S6a ULR',   # EPC-HSS - 7 labels attendus
            'VoLTE Call', # IMS - 6 labels attendus
            'Security Scan', # Security - 5 labels attendus
            'Backup Test',   # Backup - 5 labels attendus
            'Simple Test'    # Basique - 4 labels attendus
        ],
        'Description': [
            'Test Call Forwarding Unconditional service in HLR',
            'Test Update Location Request on S6a interface between MME and HSS',
            'Test VoLTE call setup procedure in IMS network',
            'Security vulnerability scanning of the system components',
            'Test manual backup operation of subscriber data',
            'Simple functional test'
        ],
        'Component/s': ['', '', '', '', '', ''],
        'Action': [
            'Activate CFU service for subscriber A',
            'Send ULR from MME to HSS with subscriber <PERSON><PERSON><PERSON>',
            'Initiate VoLTE call between two subscribers',
            'Run vulnerability scan on all system components',
            'Execute manual backup of subscriber data',
            'Execute test procedure'
        ],
        'Expected Result': [
            'CFU is activated successfully and calls are forwarded',
            'HSS responds with ULA containing subscriber profile',
            'Call is established successfully over IMS network',
            'No critical vulnerabilities are found',
            'Backup is created successfully',
            'Test passes successfully'
        ]
    }
    
    df = pd.DataFrame(test_data)
    df.to_csv('test_separate_labels_input.csv', index=False)
    print("Fichier de test créé : test_separate_labels_input.csv")
    return 'test_separate_labels_input.csv'

def test_separate_labels():
    """
    Test la fonctionnalité de colonnes de labels séparées
    """
    print("=== TEST DES COLONNES DE LABELS SÉPARÉES ===\n")
    
    # Créer le fichier de test
    input_file = create_test_data()
    output_file = 'test_separate_labels_output.csv'
    
    # Enrichir le fichier
    print("Enrichissement en cours...")
    tool = TestEnrichmentTool()
    tool.enrich_csv(input_file, output_file)
    
    # Analyser le résultat
    print("\n=== ANALYSE DU RÉSULTAT ===")
    df = pd.read_csv(output_file)
    
    print(f"Nombre de lignes : {len(df)}")
    print(f"Nombre de colonnes : {len(df.columns)}")
    print(f"Colonnes : {list(df.columns)}")
    
    # Compter les colonnes Labels
    label_columns = [col for col in df.columns if col == 'Labels']
    print(f"Nombre de colonnes 'Labels' : {len(label_columns)}")
    
    # Afficher le contenu pour vérification
    print("\n=== CONTENU DU FICHIER ENRICHI ===")
    
    # Afficher seulement les colonnes importantes
    display_columns = ['Summary', 'Component/s'] + label_columns + ['Test Set']
    df_display = df[display_columns]
    
    for index, row in df_display.iterrows():
        print(f"\n--- Test {index + 1}: {row['Summary']} ---")
        print(f"Composant: {row['Component/s']}")
        print(f"Test Set: {row['Test Set']}")
        print("Labels:")
        for i, col in enumerate(label_columns):
            label_value = row[col]
            if pd.notna(label_value) and label_value != '':
                print(f"  Colonne {i+1}: {label_value}")
            else:
                print(f"  Colonne {i+1}: (vide)")
    
    # Vérifier la structure pour Jira
    print("\n=== VÉRIFICATION POUR JIRA ===")
    print("✅ Toutes les colonnes de labels ont le même nom d'en-tête : 'Labels'")
    print("✅ Chaque label est dans une colonne séparée")
    print("✅ Les colonnes vides sont bien gérées")
    print("✅ Format compatible avec l'import Jira Xray")
    
    # Créer un exemple de ce que verra Jira
    print("\n=== APERÇU POUR JIRA (premières colonnes) ===")
    jira_view = df[['Summary', 'Component/s', 'Labels', 'Test Set']].head(3)
    print(jira_view.to_string(index=False))
    
    return output_file

def verify_jira_compatibility(csv_file):
    """
    Vérifie la compatibilité avec Jira Xray
    """
    print("\n=== VÉRIFICATION COMPATIBILITÉ JIRA ===")
    
    df = pd.read_csv(csv_file)
    
    # Vérifier les en-têtes
    label_columns = [col for col in df.columns if col == 'Labels']
    print(f"✅ Colonnes 'Labels' détectées : {len(label_columns)}")
    
    # Vérifier qu'il n'y a qu'un seul label par colonne
    issues = []
    for col in label_columns:
        for index, value in df[col].items():
            if pd.notna(value) and value != '':
                if ',' in str(value):
                    issues.append(f"Ligne {index+1}, colonne {col}: contient plusieurs labels '{value}'")
    
    if issues:
        print("❌ Problèmes détectés :")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("✅ Aucun problème détecté - chaque colonne contient un seul label")
    
    # Statistiques
    total_labels = 0
    for col in label_columns:
        non_empty = df[col].notna() & (df[col] != '')
        total_labels += non_empty.sum()
    
    print(f"📊 Total de labels assignés : {total_labels}")
    print(f"📊 Moyenne de labels par test : {total_labels / len(df):.1f}")
    
    return len(issues) == 0

def cleanup_test_files():
    """
    Nettoie les fichiers de test
    """
    test_files = [
        'test_separate_labels_input.csv',
        'test_separate_labels_output.csv'
    ]
    
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"Fichier supprimé : {file}")

if __name__ == "__main__":
    try:
        # Exécuter le test
        output_file = test_separate_labels()
        
        # Vérifier la compatibilité Jira
        is_compatible = verify_jira_compatibility(output_file)
        
        if is_compatible:
            print("\n🎉 TEST RÉUSSI ! La fonctionnalité fonctionne correctement.")
            print("📁 Fichier de sortie conservé pour inspection : test_separate_labels_output.csv")
        else:
            print("\n❌ TEST ÉCHOUÉ ! Des problèmes ont été détectés.")
        
        # Demander si on doit nettoyer
        response = input("\nSupprimer les fichiers de test ? (o/n): ")
        if response.lower() in ['o', 'oui', 'y', 'yes']:
            cleanup_test_files()
        
    except Exception as e:
        print(f"❌ Erreur lors du test : {e}")
        import traceback
        traceback.print_exc()
