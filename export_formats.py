#!/usr/bin/env python3
"""
Module pour l'export en différents formats
"""

import pandas as pd
import json
import xml.etree.ElementTree as ET
from xml.dom import minidom
import os

class ExportManager:
    def __init__(self):
        self.supported_formats = ['csv', 'xlsx', 'json', 'xml', 'tsv']
    
    def export_to_format(self, df, output_path, format_type, options=None):
        """
        Exporte le DataFrame vers le format spécifié
        """
        if options is None:
            options = {}
        
        format_type = format_type.lower()
        
        if format_type == 'csv':
            return self._export_csv(df, output_path, options)
        elif format_type == 'xlsx':
            return self._export_excel(df, output_path, options)
        elif format_type == 'json':
            return self._export_json(df, output_path, options)
        elif format_type == 'xml':
            return self._export_xml(df, output_path, options)
        elif format_type == 'tsv':
            return self._export_tsv(df, output_path, options)
        else:
            raise ValueError(f"Format non supporté: {format_type}")
    
    def _export_csv(self, df, output_path, options):
        """Export CSV avec options personnalisées"""
        separator = options.get('separator', ',')
        encoding = options.get('encoding', 'utf-8')
        include_index = options.get('include_index', False)
        
        df.to_csv(output_path, sep=separator, encoding=encoding, index=include_index)
        return output_path
    
    def _export_excel(self, df, output_path, options):
        """Export Excel avec feuilles multiples"""
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # Feuille principale
            df.to_excel(writer, sheet_name='Tests Enrichis', index=False)
            
            # Feuille de statistiques
            stats_data = self._generate_stats(df)
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='Statistiques', index=False)
            
            # Feuille par composant
            if 'Component/s' in df.columns:
                components = df['Component/s'].unique()
                for component in components[:5]:  # Limiter à 5 composants
                    if pd.notna(component) and component != '':
                        component_df = df[df['Component/s'] == component]
                        sheet_name = str(component)[:31]  # Limite Excel
                        component_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        return output_path
    
    def _export_json(self, df, output_path, options):
        """Export JSON avec structure personnalisée"""
        format_style = options.get('format', 'records')  # records, index, values
        indent = options.get('indent', 2)
        
        if format_style == 'grouped_by_component':
            # Grouper par composant
            result = {}
            if 'Component/s' in df.columns:
                for component in df['Component/s'].unique():
                    if pd.notna(component):
                        component_tests = df[df['Component/s'] == component]
                        result[component] = component_tests.to_dict('records')
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=indent, ensure_ascii=False)
        else:
            # Format standard
            df.to_json(output_path, orient=format_style, indent=indent, force_ascii=False)
        
        return output_path
    
    def _export_xml(self, df, output_path, options):
        """Export XML avec structure hiérarchique"""
        root = ET.Element("TestSuite")
        
        # Métadonnées
        metadata = ET.SubElement(root, "Metadata")
        ET.SubElement(metadata, "TotalTests").text = str(len(df))
        ET.SubElement(metadata, "ExportDate").text = pd.Timestamp.now().isoformat()
        
        # Tests
        tests_element = ET.SubElement(root, "Tests")
        
        for index, row in df.iterrows():
            test_element = ET.SubElement(tests_element, "Test", id=str(index))
            
            for column, value in row.items():
                if pd.notna(value):
                    # Nettoyer le nom de la colonne pour XML
                    clean_column = column.replace('/', '_').replace(' ', '_')
                    element = ET.SubElement(test_element, clean_column)
                    element.text = str(value)
        
        # Formater et sauvegarder
        rough_string = ET.tostring(root, 'unicode')
        reparsed = minidom.parseString(rough_string)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(reparsed.toprettyxml(indent="  "))
        
        return output_path
    
    def _export_tsv(self, df, output_path, options):
        """Export TSV (Tab-Separated Values)"""
        encoding = options.get('encoding', 'utf-8')
        include_index = options.get('include_index', False)
        
        df.to_csv(output_path, sep='\t', encoding=encoding, index=include_index)
        return output_path
    
    def _generate_stats(self, df):
        """Génère des statistiques pour l'export"""
        stats = []
        
        # Statistiques générales
        stats.append({'Métrique': 'Nombre total de tests', 'Valeur': len(df)})
        stats.append({'Métrique': 'Nombre de colonnes', 'Valeur': len(df.columns)})
        
        # Statistiques par composant
        if 'Component/s' in df.columns:
            component_counts = df['Component/s'].value_counts()
            for component, count in component_counts.items():
                stats.append({'Métrique': f'Tests {component}', 'Valeur': count})
        
        # Statistiques par type
        if 'Labels' in df.columns:
            functional_count = df['Labels'].str.contains('Functional', na=False).sum()
            non_functional_count = df['Labels'].str.contains('NonFunctional', na=False).sum()
            stats.append({'Métrique': 'Tests Fonctionnels', 'Valeur': functional_count})
            stats.append({'Métrique': 'Tests Non-Fonctionnels', 'Valeur': non_functional_count})
        
        return stats
    
    def create_jira_import_format(self, df, output_path):
        """
        Crée un format spécialement optimisé pour l'import Jira Xray
        """
        # Colonnes requises pour Jira Xray
        jira_columns = [
            'Summary', 'Description', 'Component/s', 'Labels', 'Test Set',
            'Action', 'Expected Result', 'Test Repository Path'
        ]
        
        # Créer le DataFrame pour Jira
        jira_df = pd.DataFrame()
        
        for col in jira_columns:
            if col in df.columns:
                jira_df[col] = df[col]
            else:
                jira_df[col] = ''  # Colonne vide si non présente
        
        # Nettoyer les données pour Jira
        jira_df = jira_df.fillna('')
        
        # Formater les labels pour Jira (séparés par des espaces)
        if 'Labels' in jira_df.columns:
            jira_df['Labels'] = jira_df['Labels'].str.replace(', ', ' ')
        
        # Sauvegarder
        jira_df.to_csv(output_path, index=False, encoding='utf-8')
        
        return output_path
    
    def create_comparison_report(self, original_df, enriched_df, output_path):
        """
        Crée un rapport de comparaison avant/après enrichissement
        """
        report = {
            'summary': {
                'original_tests': len(original_df),
                'enriched_tests': len(enriched_df),
                'original_columns': len(original_df.columns),
                'enriched_columns': len(enriched_df.columns),
                'added_columns': list(set(enriched_df.columns) - set(original_df.columns))
            },
            'column_analysis': {},
            'enrichment_stats': {}
        }
        
        # Analyse des colonnes
        for col in enriched_df.columns:
            if col in original_df.columns:
                report['column_analysis'][col] = {
                    'status': 'preserved',
                    'original_filled': original_df[col].notna().sum(),
                    'enriched_filled': enriched_df[col].notna().sum()
                }
            else:
                report['column_analysis'][col] = {
                    'status': 'added',
                    'filled_count': enriched_df[col].notna().sum()
                }
        
        # Statistiques d'enrichissement
        if 'Component/s' in enriched_df.columns:
            report['enrichment_stats']['components'] = enriched_df['Component/s'].value_counts().to_dict()
        
        if 'Labels' in enriched_df.columns:
            functional_count = enriched_df['Labels'].str.contains('Functional', na=False).sum()
            non_functional_count = enriched_df['Labels'].str.contains('NonFunctional', na=False).sum()
            report['enrichment_stats']['test_types'] = {
                'functional': functional_count,
                'non_functional': non_functional_count
            }
        
        # Sauvegarder le rapport
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return output_path

# Instance globale
export_manager = ExportManager()

if __name__ == "__main__":
    # Test du module d'export
    print("Test du module d'export...")
    
    # Créer un DataFrame de test
    test_data = {
        'Summary': ['Test 1', 'Test 2'],
        'Component/s': ['HLR', 'EPC-HSS'],
        'Labels': ['Functional, System', 'NonFunctional, Performance']
    }
    df = pd.DataFrame(test_data)
    
    # Tester l'export JSON
    export_manager.export_to_format(df, 'test_export.json', 'json')
    print("Export JSON créé: test_export.json")
    
    # Nettoyer
    if os.path.exists('test_export.json'):
        os.remove('test_export.json')
