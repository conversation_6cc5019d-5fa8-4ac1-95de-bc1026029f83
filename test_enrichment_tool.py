#!/usr/bin/env python3
"""
Outil d'enrichissement automatique pour tests Jira Xray
Applique les règles de labellisation selon les standards ISTQB et les spécificités télécoms
"""

import pandas as pd
import re
import sys
from typing import List, Tuple, Dict

class TestEnrichmentTool:
    def __init__(self):
        # Définition des patterns et règles de labellisation
        self.hlr_patterns = [
            r'call\s*forward', r'call\s*barr', r'supplementary', r'camel', r'hlr', 
            r'map', r'sri', r'ati', r'any\s*time\s*interrogation', r'location\s*management',
            r'2g', r'3g', r'gsm', r'umts'
        ]
        
        self.hss_epc_patterns = [
            r'attach', r'detach', r'update\s*location', r'authentication', r'hss', 
            r'epc', r's6a', r's6d', r'diameter', r'ulr', r'air', r'nor', r'4g', r'lte'
        ]
        
        self.ims_patterns = [
            r'volte', r'ims', r't-ads', r'srvcc', r'esrvcc', r'swx', r'sh', r'cx', 
            r'scc-as', r'atcf', r'voice\s*over\s*lte'
        ]
        
        self.oam_patterns = [
            r'provisioning', r'oam', r'soap', r'mml', r'configuration', r'management',
            r'web\s*agent', r'portal', r'om\s*portal'
        ]
        
        self.backup_patterns = [
            r'backup', r'restore', r'recovery', r'schedule.*backup'
        ]
        
        self.security_patterns = [
            r'cis', r'vulnerability', r'rbac', r'security', r'hardening', r'authentication',
            r'authorization', r'hsm', r'scan', r'cluster.*security'
        ]
        
        self.performance_patterns = [
            r'performance', r'resiliency', r'redundancy', r'vnf', r'lcm', r'scaling',
            r'autoscaling', r'load', r'geo.*redundancy'
        ]
        
        self.documentation_patterns = [
            r'documentation', r'help', r'online\s*help', r'conformance'
        ]

    def analyze_test_content(self, summary: str, description: str, action: str,
                           expected_result: str, test_path: str) -> Tuple[List[str], str, str]:
        """
        Analyse le contenu d'un test et détermine les labels, composant et test set appropriés
        """
        # Concaténer tout le contenu pour l'analyse
        full_content = f"{summary} {description} {action} {expected_result} {test_path}".lower()

        labels = ["Functional", "System", "Manual", "Regression"]  # Labels de base
        component = "COMMON"  # Composant par défaut
        test_set = "General Tests"  # Test set par défaut

        # Analyse Backup/Restore en premier (priorité haute)
        if any(re.search(pattern, full_content, re.IGNORECASE) for pattern in self.backup_patterns):
            labels = ["NonFunctional", "System", "Manual", "Regression", "BackupRestore"]
            test_set = "Backup and Restore Operations"
            return labels, component, test_set

        # Analyse Sécurité en deuxième (priorité haute)
        elif any(re.search(pattern, full_content, re.IGNORECASE) for pattern in self.security_patterns):
            labels = ["NonFunctional", "System", "Manual", "Regression", "Security"]
            component = "Security"

            if re.search(r'cis', full_content, re.IGNORECASE):
                labels.append("CIS")
                test_set = "Security CIS Compliance"
            elif re.search(r'vulnerability', full_content, re.IGNORECASE):
                labels.append("Vulnerability")
                test_set = "Security Vulnerability Assessment"
            elif re.search(r'rbac', full_content, re.IGNORECASE):
                labels.append("RBAC")
                test_set = "Security RBAC Management"
            else:
                test_set = "Security General Tests"
            return labels, component, test_set

        # Analyse Performance/Résilience
        elif any(re.search(pattern, full_content, re.IGNORECASE) for pattern in self.performance_patterns):
            labels = ["NonFunctional", "System", "Manual", "Regression"]

            if re.search(r'performance', full_content, re.IGNORECASE):
                labels.append("Performance")
                test_set = "Performance Testing"
            elif re.search(r'resiliency|redundancy', full_content, re.IGNORECASE):
                labels.append("Resiliency")
                test_set = "Resiliency and Redundancy"
            elif re.search(r'vnf|lcm', full_content, re.IGNORECASE):
                labels.append("VNF")
                labels.append("LCM")
                test_set = "VNF Lifecycle Management"
            else:
                test_set = "Non-Functional Testing"
            return labels, component, test_set

        # Analyse Documentation
        elif any(re.search(pattern, full_content, re.IGNORECASE) for pattern in self.documentation_patterns):
            labels = ["NonFunctional", "System", "Manual", "Regression", "Documentation"]
            test_set = "Documentation and Compliance"
            return labels, component, test_set

        # Analyse HSS/EPC (avant HLR pour éviter les conflits)
        elif any(re.search(pattern, full_content, re.IGNORECASE) for pattern in self.hss_epc_patterns):
            component = "EPC-HSS"
            labels.extend(["4G", "Diameter", "S6a"])

            if re.search(r'attach|detach', full_content, re.IGNORECASE):
                labels.append("AttachDetach")
                test_set = "EPC HSS Attach/Detach Procedures"
            elif re.search(r'update\s*location', full_content, re.IGNORECASE):
                labels.append("UpdateLocation")
                test_set = "EPC HSS Location Management"
            elif re.search(r'authentication', full_content, re.IGNORECASE):
                labels.append("Authentication")
                test_set = "EPC HSS Authentication Services"
            else:
                test_set = "EPC HSS General Services"

        # Analyse IMS
        elif any(re.search(pattern, full_content, re.IGNORECASE) for pattern in self.ims_patterns):
            component = "IMS-HSS"
            labels.extend(["4G", "IMS"])

            if re.search(r'volte', full_content, re.IGNORECASE):
                labels.append("VoLTE")
                test_set = "IMS VoLTE Services"
            elif re.search(r't-ads', full_content, re.IGNORECASE):
                labels.append("TADS")
                test_set = "IMS T-ADS Services"
            elif re.search(r'srvcc', full_content, re.IGNORECASE):
                labels.append("SRVCC")
                test_set = "IMS SRVCC Services"
            else:
                test_set = "IMS General Services"

        # Analyse OAM/Provisioning
        elif any(re.search(pattern, full_content, re.IGNORECASE) for pattern in self.oam_patterns):
            component = "OAM"
            labels.append("OAM")

            if re.search(r'soap', full_content, re.IGNORECASE):
                labels.append("SOAP")
            elif re.search(r'mml', full_content, re.IGNORECASE):
                labels.append("MML")

            test_set = "OAM and Provisioning"

        # Analyse HLR (en dernier pour éviter les conflits)
        elif any(re.search(pattern, full_content, re.IGNORECASE) for pattern in self.hlr_patterns):
            component = "HLR"
            labels.extend(["2G", "MAP"])

            if re.search(r'call\s*forward', full_content, re.IGNORECASE):
                labels.append("CallForwarding")
                test_set = "HLR Call Forwarding Services"
            elif re.search(r'call\s*barr', full_content, re.IGNORECASE):
                labels.append("CallBarring")
                test_set = "HLR Call Barring Services"
            elif re.search(r'supplementary', full_content, re.IGNORECASE):
                labels.append("SupplementaryService")
                test_set = "HLR Supplementary Services"
            elif re.search(r'camel', full_content, re.IGNORECASE):
                labels.append("CAMEL")
                test_set = "HLR CAMEL Services"
            else:
                test_set = "HLR General Services"

        return labels, component, test_set

    def enrich_csv(self, input_file: str, output_file: str):
        """
        Enrichit le fichier CSV avec les nouvelles colonnes
        """
        try:
            # Lecture du fichier CSV
            df = pd.read_csv(input_file)
            
            print(f"Lecture de {len(df)} lignes depuis {input_file}")
            
            # Initialisation des nouvelles colonnes
            df['Labels'] = ''
            df['Test Set'] = ''
            
            # Traitement de chaque ligne
            for index, row in df.iterrows():
                summary = str(row.get('Summary', ''))
                description = str(row.get('Description', ''))
                action = str(row.get('Action', ''))
                expected_result = str(row.get('Expected Result', ''))
                test_path = str(row.get('Custom field (Test Repository Path)', ''))
                current_component = str(row.get('Component/s', ''))
                
                # Analyse et enrichissement
                labels, component, test_set = self.analyze_test_content(
                    summary, description, action, expected_result, test_path
                )
                
                # Mise à jour des colonnes
                df.at[index, 'Labels'] = ', '.join(labels)
                df.at[index, 'Test Set'] = test_set
                
                # Mise à jour du composant si vide ou générique
                if not current_component or current_component in ['', 'nan', 'COMMON']:
                    df.at[index, 'Component/s'] = component
                
                # Affichage du progrès
                if (index + 1) % 100 == 0:
                    print(f"Traité {index + 1} lignes...")
            
            # Sauvegarde du fichier enrichi
            df.to_csv(output_file, index=False)
            print(f"Fichier enrichi sauvegardé : {output_file}")
            
            # Statistiques
            self.print_statistics(df)
            
        except Exception as e:
            print(f"Erreur lors du traitement : {e}")
            sys.exit(1)

    def print_statistics(self, df: pd.DataFrame):
        """
        Affiche des statistiques sur l'enrichissement
        """
        print("\n=== STATISTIQUES D'ENRICHISSEMENT ===")
        print(f"Total de tests traités : {len(df)}")
        
        # Répartition par composant
        print("\nRépartition par composant :")
        component_counts = df['Component/s'].value_counts()
        for component, count in component_counts.items():
            print(f"  {component}: {count}")
        
        # Répartition par type de test (Functional vs NonFunctional)
        print("\nRépartition par type :")
        functional_count = df['Labels'].str.contains('Functional', na=False).sum()
        non_functional_count = df['Labels'].str.contains('NonFunctional', na=False).sum()
        print(f"  Functional: {functional_count}")
        print(f"  NonFunctional: {non_functional_count}")

def main():
    if len(sys.argv) != 3:
        print("Usage: python test_enrichment_tool.py <input_file.csv> <output_file.csv>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    tool = TestEnrichmentTool()
    tool.enrich_csv(input_file, output_file)

if __name__ == "__main__":
    main()
