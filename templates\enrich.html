{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Indicateur d'étapes -->
        <div class="step-indicator">
            <div class="step completed">
                <div class="step-number"><i class="fas fa-check"></i></div>
                <span>Télécharger</span>
            </div>
            <div class="step completed">
                <div class="step-number"><i class="fas fa-check"></i></div>
                <span>Valider</span>
            </div>
            <div class="step active">
                <div class="step-number">3</div>
                <span>Enrichir</span>
            </div>
            <div class="step">
                <div class="step-number">4</div>
                <span>Télécharger</span>
            </div>
        </div>

        <div class="card shadow">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-magic"></i> Enrichissement automatique
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-file-csv"></i>
                    <strong>Fichier :</strong> {{ filename }}
                    <br>
                    <strong>Format :</strong>
                    {% if format_type == 'separate' %}
                        <span class="badge bg-success">Jira Optimisé (colonnes séparées)</span>
                    {% else %}
                        <span class="badge bg-info">Standard (labels combinés)</span>
                    {% endif %}
                </div>

                <!-- Processus d'enrichissement -->
                <div id="enrichment-process">
                    <div class="text-center">
                        <h5>Prêt à enrichir votre fichier</h5>
                        <p class="text-muted">
                            L'enrichissement appliquera automatiquement les règles de labellisation ISTQB
                            et les spécificités télécoms à vos tests.
                        </p>
                        
                        <button id="start-enrichment" class="btn btn-primary btn-lg">
                            <i class="fas fa-play"></i> Démarrer l'enrichissement
                        </button>
                    </div>
                </div>

                <!-- Barre de progression -->
                <div id="progress-section" style="display: none;">
                    <div class="text-center mb-3">
                        <h5 id="progress-title">Enrichissement en cours...</h5>
                        <p id="progress-description" class="text-muted">Analyse des tests et application des règles</p>
                    </div>
                    
                    <div class="progress mb-3" style="height: 25px;">
                        <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%">
                            <span id="progress-text">0%</span>
                        </div>
                    </div>
                    
                    <div id="progress-steps">
                        <div class="row text-center">
                            <div class="col-3">
                                <div id="step-1" class="progress-step">
                                    <i class="fas fa-file-import fa-2x text-muted"></i>
                                    <p class="small mt-1">Lecture du fichier</p>
                                </div>
                            </div>
                            <div class="col-3">
                                <div id="step-2" class="progress-step">
                                    <i class="fas fa-search fa-2x text-muted"></i>
                                    <p class="small mt-1">Analyse des tests</p>
                                </div>
                            </div>
                            <div class="col-3">
                                <div id="step-3" class="progress-step">
                                    <i class="fas fa-tags fa-2x text-muted"></i>
                                    <p class="small mt-1">Application des labels</p>
                                </div>
                            </div>
                            <div class="col-3">
                                <div id="step-4" class="progress-step">
                                    <i class="fas fa-file-excel fa-2x text-muted"></i>
                                    <p class="small mt-1">Génération des fichiers</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Résultats -->
                <div id="results-section" style="display: none;">
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <strong>Enrichissement terminé avec succès !</strong>
                    </div>
                    
                    <div id="enrichment-stats" class="row mb-4">
                        <!-- Les statistiques seront ajoutées dynamiquement -->
                    </div>
                    
                    <div id="preview-section">
                        <h5><i class="fas fa-eye"></i> Aperçu des résultats</h5>
                        <div class="table-responsive">
                            <table id="preview-table" class="table table-striped table-sm">
                                <!-- Le tableau sera rempli dynamiquement -->
                            </table>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <a id="download-csv" href="#" class="btn btn-success btn-lg me-2">
                            <i class="fas fa-download"></i> Télécharger CSV
                        </a>
                        <a id="download-excel" href="#" class="btn btn-primary btn-lg me-2">
                            <i class="fas fa-file-excel"></i> Télécharger Excel
                        </a>
                        <a id="export-options" href="#" class="btn btn-outline-info btn-lg">
                            <i class="fas fa-cogs"></i> Plus d'Options
                        </a>
                    </div>
                </div>

                <!-- Erreur -->
                <div id="error-section" style="display: none;">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Erreur lors de l'enrichissement :</strong>
                        <span id="error-message"></span>
                    </div>
                    
                    <div class="text-center">
                        <button id="retry-enrichment" class="btn btn-warning">
                            <i class="fas fa-redo"></i> Réessayer
                        </button>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-arrow-left"></i> Retour à l'accueil
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
const fileId = '{{ file_id }}';
const formatType = '{{ format_type }}';

document.getElementById('start-enrichment').addEventListener('click', function() {
    startEnrichment();
});

document.getElementById('retry-enrichment').addEventListener('click', function() {
    resetAndStart();
});

function resetAndStart() {
    document.getElementById('error-section').style.display = 'none';
    document.getElementById('results-section').style.display = 'none';
    startEnrichment();
}

function startEnrichment() {
    // Masquer le bouton de démarrage
    document.getElementById('enrichment-process').style.display = 'none';

    // Afficher la barre de progression
    document.getElementById('progress-section').style.display = 'block';

    // Démarrer l'enrichissement réel
    fetch(`/process/${fileId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            format: formatType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Démarrer le suivi du progrès
            startProgressTracking(data.job_id, data.estimated_time);
        } else {
            showError(data.message);
        }
    })
    .catch(error => {
        showError('Erreur de communication avec le serveur');
    });
}

function startProgressTracking(jobId, estimatedTime) {
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const progressDescription = document.getElementById('progress-description');

    // Afficher le temps estimé
    if (estimatedTime > 0) {
        const minutes = Math.ceil(estimatedTime / 60);
        progressDescription.textContent = `Temps estimé: ${minutes} minute(s)`;
    }

    // Fonction pour vérifier le statut
    function checkStatus() {
        fetch(`/job_status/${jobId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.status) {
                    const status = data.status;

                    // Mettre à jour la barre de progression
                    if (status.progress >= 0) {
                        progressBar.style.width = status.progress + '%';
                        progressText.textContent = status.progress + '%';
                    }

                    // Mettre à jour le message
                    progressDescription.textContent = status.message;

                    // Mettre à jour les étapes visuelles
                    updateProgressSteps(status.progress);

                    // Vérifier le statut
                    if (status.status === 'completed') {
                        showResults({
                            csv_file: status.csv_file,
                            excel_file: status.excel_file
                        });
                    } else if (status.status === 'error') {
                        showError(status.message);
                    } else {
                        // Continuer à vérifier
                        setTimeout(checkStatus, 1000);
                    }
                } else {
                    showError('Erreur lors du suivi du progrès');
                }
            })
            .catch(error => {
                showError('Erreur de communication avec le serveur');
            });
    }

    // Démarrer le suivi
    checkStatus();
}

function updateProgressSteps(progress) {
    const steps = [
        { id: 'step-1', threshold: 25 },
        { id: 'step-2', threshold: 50 },
        { id: 'step-3', threshold: 75 },
        { id: 'step-4', threshold: 95 }
    ];

    steps.forEach(step => {
        const stepElement = document.getElementById(step.id);
        const icon = stepElement.querySelector('i');

        if (progress >= step.threshold) {
            icon.className = 'fas fa-check-circle fa-2x text-success';
        } else if (progress >= step.threshold - 25) {
            icon.className = 'fas fa-spinner fa-spin fa-2x text-primary';
        } else {
            icon.className = 'fas fa-circle fa-2x text-muted';
        }
    });
}

function simulateProgress() {
    const steps = [
        { id: 'step-1', text: 'Lecture du fichier...', progress: 25 },
        { id: 'step-2', text: 'Analyse des tests...', progress: 50 },
        { id: 'step-3', text: 'Application des labels...', progress: 75 },
        { id: 'step-4', text: 'Génération des fichiers...', progress: 100 }
    ];
    
    let currentStep = 0;
    
    function nextStep() {
        if (currentStep < steps.length) {
            const step = steps[currentStep];
            
            // Mettre à jour l'icône de l'étape
            const stepElement = document.getElementById(step.id);
            const icon = stepElement.querySelector('i');
            icon.className = 'fas fa-spinner fa-spin fa-2x text-primary';
            
            // Mettre à jour la barre de progression
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            progressBar.style.width = step.progress + '%';
            progressText.textContent = step.progress + '%';
            
            // Mettre à jour la description
            document.getElementById('progress-description').textContent = step.text;
            
            currentStep++;
            
            setTimeout(() => {
                // Marquer l'étape comme terminée
                icon.className = 'fas fa-check-circle fa-2x text-success';
                
                if (currentStep < steps.length) {
                    setTimeout(nextStep, 500);
                }
            }, 1000);
        }
    }
    
    nextStep();
}

function showResults(data) {
    document.getElementById('progress-section').style.display = 'none';
    document.getElementById('results-section').style.display = 'block';
    
    // Configurer les liens de téléchargement
    document.getElementById('download-csv').href = `/download/${data.csv_file}`;
    document.getElementById('download-excel').href = `/download/${data.excel_file}`;
    document.getElementById('export-options').href = `/export_options/${fileId}`;
    
    // Charger l'aperçu
    loadPreview();
    
    // Mettre à jour l'indicateur d'étapes
    const step4 = document.querySelector('.step-indicator .step:last-child');
    step4.classList.add('completed');
    step4.classList.remove('active');
    step4.querySelector('.step-number').innerHTML = '<i class="fas fa-check"></i>';
}

function showError(message) {
    document.getElementById('progress-section').style.display = 'none';
    document.getElementById('error-section').style.display = 'block';
    document.getElementById('error-message').textContent = message;
}

function loadPreview() {
    fetch(`/preview/${fileId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayPreview(data.preview, data.stats, data.columns);
        }
    })
    .catch(error => {
        console.error('Erreur lors du chargement de l\'aperçu:', error);
    });
}

function displayPreview(preview, stats, columns) {
    // Afficher les statistiques
    const statsContainer = document.getElementById('enrichment-stats');
    statsContainer.innerHTML = `
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-primary">${stats.total_tests}</h4>
                    <p class="card-text">Tests enrichis</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-success">${stats.functional_count}</h4>
                    <p class="card-text">Tests fonctionnels</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-info">${stats.non_functional_count}</h4>
                    <p class="card-text">Tests non-fonctionnels</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-warning">${Object.keys(stats.components).length}</h4>
                    <p class="card-text">Composants identifiés</p>
                </div>
            </div>
        </div>
    `;
    
    // Afficher l'aperçu du tableau
    const table = document.getElementById('preview-table');
    let tableHTML = '<thead class="table-dark"><tr>';
    
    // En-têtes principales
    const mainColumns = ['Summary', 'Component/s', 'Test Set', 'Labels'];
    mainColumns.forEach(col => {
        if (columns.includes(col)) {
            tableHTML += `<th>${col}</th>`;
        }
    });
    tableHTML += '</tr></thead><tbody>';
    
    // Lignes de données (limité à 5 pour l'aperçu)
    preview.slice(0, 5).forEach(row => {
        tableHTML += '<tr>';
        mainColumns.forEach(col => {
            if (columns.includes(col)) {
                let value = row[col] || '';
                if (value.length > 50) {
                    value = value.substring(0, 50) + '...';
                }
                tableHTML += `<td>${value}</td>`;
            }
        });
        tableHTML += '</tr>';
    });
    
    tableHTML += '</tbody>';
    table.innerHTML = tableHTML;
}
</script>
{% endblock %}
