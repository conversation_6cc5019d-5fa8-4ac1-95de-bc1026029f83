Summary;Issue key;Issue id;Issue Type;Status;Project key;Project name;Project type;Project lead;Project description;Project url;Priority;Resolution;Assignee;Reporter;Creator;Created;Updated;Last Viewed;Resolved;Affects Version/s;Fix Version/s;Component/s;Due Date;Votes;Labels;Labels;Labels;Labels;Labels;Labels;Labels;Labels;Description;Environment;Watchers;Log Work;Original Estimate;Remaining Estimate;Time Spent;Work Ratio;Σ Original Estimate;Σ Remaining Estimate;Σ Time Spent;Security Level;Attachment;Custom field (Automation status);Custom field (Branch);Custom field (Business Gain);Custom field (CDR/MC);Custom field (Category List);Custom field (Change completion date);Custom field (Change start date);Custom field (Change type);Custom field (Cost);Custom field (Cucumber Scenario);Custom field (Cucumber Test Type);Customer Request Type;Custom field (Dataset values);Custom field (Dataset values);Custom field (Date MEP);Custom field (Date UAT);Demande SLA 16H;Demande SLA 16H simplified;Custom field (Domain List);Durée de traitement;Durée de traitement simplified;<PERSON><PERSON>lai de Qualification;<PERSON><PERSON>lai de <PERSON> simplified;<PERSON><PERSON>lai de prise en charge;D<PERSON>lai de prise en charge simplified;Custom field (Effects);Custom field (Entity List);Custom field (Environment);Custom field (Epic Link);Custom field (External Contributor/s);Custom field (External issue ID);Fermeture apres x jours;Fermeture apres x jours simplified;Custom field (First Backlog Transition);Custom field (First key);Custom field (First-Name);Custom field (Flagged);Custom field (Generic Test Definition);Custom field (Groups);Custom field (Groups);Custom field (Impact);Custom field (Impacted Entity);Custom field (Jira Project Type);Custom field (Last-Name);Custom field (Linked major incidents);Custom field (List Entity);Custom field (MVP Macro Budget (K€));Custom field (Mail);Custom field (Manual Test Steps);Custom field (Operational categorization);Custom field (Organizations);Custom field (Original story points);Custom field (Overcoast);Custom field (Parent Key);Custom field (Parent Link);Custom field (Penalties);Custom field (Platform);Custom field (Pre-Conditions association with a Test);Prise en compte;Prise en compte simplified;Custom field (Product categorization);Custom field (QC);Custom field (Qualification Date);Custom field (Rank);Custom field (Ref. Project CARTO);Custom field (Reference Code);Custom field (Request participants);Resolution Time SLA;Resolution Time SLA simplified;Response Time SLA;Response Time SLA simplified;Custom field (Result);Custom field (Review date);Custom field (Revision);Satisfaction score (out of 5);Custom field (Scoring);Sprint;Custom field (Steps Count);Custom field (Structure Index Monitor);Custom field (Support);Custom field (Target end);Custom field (Target start);Custom field (Team);Custom field (Team List);"Temps d&#39;attribution";"Temps d&#39;attribution simplified";Temps première réponse;Temps première réponse simplified;Custom field (Test Execution Status);Custom field (Test Plans associated with a Test);Custom field (Test Repository Path);Custom field (Test Sets association with a Test);Custom field (Test Sets association with a Test);Custom field (Test Type);Custom field (TestRunStatus);Time to close after resolution;Time to close after resolution simplified;Time to first response;Time to first response simplified;Time to resolution;Time to resolution simplified;Custom field (TimeRecup (deprecated));Custom field (User Activity);Custom field (Workaround (deprecated));Comment;Action;Data;Expected Result
test File Storage DB;SANTSDM-1896;8939088;Test;To Do;SANTSDM;SANDBOX_TEST_SDM;software;<EMAIL>;;;Lowest;;;<EMAIL>;<EMAIL>;11/Jul/25 6:44 PM;12/Jul/25 3:45 PM;17/Jul/25 9:59 AM;;;;ZTE-SPECIFIC;;0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;SANTSDM-1896;;;;;;;;software;;;;;;[];;;;;;;;;;;;;;;0|j1qd1k:;;;;;;;;;;;;;;0.0;;;;;;;;;;;"{""issueId"":8939088,""testStatuses"":[]}";;;;;Manual;TODO;;;;;;;;;;;;;
SOAP provisionning;SANTSDM-1895;8939087;Test;To Do;SANTSDM;SANDBOX_TEST_SDM;software;<EMAIL>;;;Lowest;;;<EMAIL>;<EMAIL>;11/Jul/25 6:44 PM;15/Jul/25 5:34 PM;15/Jul/25 12:34 PM;;;;;;0;;;;;;;;;"SUMMARY : None
PRECONDITIONS : None";;;;;;;;;;;;;;;;;;;;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;SANTSDM-1895;;;;;;;;software;;;;;;"[{""id"":3844657,""index"":1,""fields"":{""Action"":""\""- Verify that user provisionning can be executed through SOAP command\n\"""",""Data"":"""",""Expected Result"":""\""- User provisionning operate with success with SOAP command\n\""""},""attachments"":[],""testVersionId"":1102789}]";;;;;;;;;;;;;;;0|j1qd1c:;;;;;;;;;;;;;;1.0;;;;;;;;;;;"{""issueId"":8939087,""testStatuses"":[]}";;/SDM COMMON TESTCASES/NON REGRESSION Testcases/COMMON Non Regression/Subscriber management;;;Manual;TODO;;;;;;;;;;;- Verify that user provisionning can be executed through SOAP command;;- User provisionning operate with success with SOAP command
MML provisionning;SANTSDM-1894;8939086;Test;To Do;SANTSDM;SANDBOX_TEST_SDM;software;<EMAIL>;;;Lowest;;;<EMAIL>;<EMAIL>;11/Jul/25 6:44 PM;15/Jul/25 5:34 PM;17/Jul/25 10:39 AM;;;;;;0;;;;;;;;;"SUMMARY : None
PRECONDITIONS : None";;;;;;;;;;;;;;;;;;;;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;SANTSDM-1894;;;;;;;;software;;;;;;"[{""id"":3844656,""index"":1,""fields"":{""Action"":""\""- Verify that user provisionning can be executed through MMLcommand\n\"""",""Data"":"""",""Expected Result"":""\""- User provisionning operate with success with MML command\n\""""},""attachments"":[],""testVersionId"":1102788}]";;;;;;;;;;;;;;;0|j1qd14:;;;;;;;;;;;;;;1.0;;;;;;;;;;;"{""issueId"":8939086,""testStatuses"":[]}";;/SDM COMMON TESTCASES/NON REGRESSION Testcases/COMMON Non Regression/Subscriber management;;;Manual;TODO;;;;;;;;;;;- Verify that user provisionning can be executed through MMLcommand;;- User provisionning operate with success with MML command
CFU;SANTSDM-1893;8939085;Test;To Do;SANTSDM;SANDBOX_TEST_SDM;software;<EMAIL>;;;Lowest;;;<EMAIL>;<EMAIL>;11/Jul/25 6:44 PM;15/Jul/25 3:08 PM;11/Jul/25 6:48 PM;;;;HLR;;0;System;System;System;System;System;System;System;System;"SUMMARY : Call Forward service

CFU

The purpose of the test is to verify that it is possible to provision a subscriber with CFU.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 1.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFU provisioned.
	MS is attached.";;;;;;;;;;;;;;;;;;;;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;SANTSDM-1893;;;;;;;;software;;;;;;"[{""id"":3844654,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFUservice in the HLR.\n\tRegister and Activate CFU service\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFU provision data.\n\n\""""},""attachments"":[],""testVersionId"":1102787},{""id"":3844655,""index"":2,""fields"":{""Action"":""\""Messages fows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102787}]";;;;;;;;;;;;;;;0|j1qd0w:;;;;;;;;;;;;;;2.0;;;;;;;;;;;"{""issueId"":8939085,""testStatuses"":[]}";;/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service;;;Manual;TODO;;;;;;;;;;;"Modify the subscriber subscription information to add CFUservice in the HLR.
	Register and Activate CFU service";;"Verify that the monitored message sequence is correct.
	Verify that the provision is completed successfully.
	Verify that the ISD message is sent by the HLR containing CFU provision data."
CFU;SANTSDM-1893;8939085;Test;To Do;SANTSDM;SANDBOX_TEST_SDM;software;<EMAIL>;;;Lowest;;;<EMAIL>;<EMAIL>;11/Jul/25 6:44 PM;15/Jul/25 3:08 PM;11/Jul/25 6:48 PM;;;;HLR;;0;System;System;System;System;System;System;System;System;"SUMMARY : Call Forward service

CFU

The purpose of the test is to verify that it is possible to provision a subscriber with CFU.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 1.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFU provisioned.
	MS is attached.";;;;;;;;;;;;;;;;;;;;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;SANTSDM-1893;;;;;;;;software;;;;;;"[{""id"":3844654,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFUservice in the HLR.\n\tRegister and Activate CFU service\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFU provision data.\n\n\""""},""attachments"":[],""testVersionId"":1102787},{""id"":3844655,""index"":2,""fields"":{""Action"":""\""Messages fows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102787}]";;;;;;;;;;;;;;;0|j1qd0w:;;;;;;;;;;;;;;2.0;;;;;;;;;;;"{""issueId"":8939085,""testStatuses"":[]}";;/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service;;;Manual;TODO;;;;;;;;;;;Messages fows :;;"VLR
			
			
			--
			
			
			HLR
			
			
			Insert subscriber data
			
		
		
			
			VLR
			
			
			--
			
			
			HLR
			
			
			Insert subscriber data acknowledge"
CFB;SANTSDM-1892;8939084;Test;To Do;SANTSDM;SANDBOX_TEST_SDM;software;<EMAIL>;;;Lowest;;;<EMAIL>;<EMAIL>;11/Jul/25 6:44 PM;15/Jul/25 3:08 PM;11/Jul/25 6:44 PM;;;;HLR;;0;System;System;System;System;System;System;System;System;"SUMMARY : Call Forward service

CFB

The purpose of the test is to verify that it is possible to provision a subscriber with CFB.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 2.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFB provisioned.
	MS is attached.";;;;;;;;;;;;;;;;;;;;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;SANTSDM-1892;;;;;;;;software;;;;;;"[{""id"":3844652,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFBservice in the HLR.\n\tRegister and Activate CFBservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFBprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102786},{""id"":3844653,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102786}]";;;;;;;;;;;;;;;0|j1qd0o:;;;;;;;;;;;;;;2.0;;;;;;;;;;;"{""issueId"":8939084,""testStatuses"":[]}";;/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service;;;Manual;TODO;;;;;;;;;;;"Modify the subscriber subscription information to add CFBservice in the HLR.
	Register and Activate CFBservice";;"Verify that the monitored message sequence is correct.
	Verify that the provision is completed successfully.
	Verify that the ISD message is sent by the HLR containing CFBprovision data."
CFB;SANTSDM-1892;8939084;Test;To Do;SANTSDM;SANDBOX_TEST_SDM;software;<EMAIL>;;;Lowest;;;<EMAIL>;<EMAIL>;11/Jul/25 6:44 PM;15/Jul/25 3:08 PM;11/Jul/25 6:44 PM;;;;HLR;;0;System;System;System;System;System;System;System;System;"SUMMARY : Call Forward service

CFB

The purpose of the test is to verify that it is possible to provision a subscriber with CFB.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 2.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFB provisioned.
	MS is attached.";;;;;;;;;;;;;;;;;;;;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;SANTSDM-1892;;;;;;;;software;;;;;;"[{""id"":3844652,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFBservice in the HLR.\n\tRegister and Activate CFBservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFBprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102786},{""id"":3844653,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102786}]";;;;;;;;;;;;;;;0|j1qd0o:;;;;;;;;;;;;;;2.0;;;;;;;;;;;"{""issueId"":8939084,""testStatuses"":[]}";;/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service;;;Manual;TODO;;;;;;;;;;;Messages flows :;;"VLR
			
			
			--
			
			
			HLR
			
			
			Insert subscriber data
			
		
		
			
			VLR
			
			
			--
			
			
			HLR
			
			
			Insert subscriber data acknowledge"
CFNRy;SANTSDM-1891;8939083;Test;To Do;SANTSDM;SANDBOX_TEST_SDM;software;<EMAIL>;;;Lowest;;;<EMAIL>;<EMAIL>;11/Jul/25 6:44 PM;15/Jul/25 3:08 PM;17/Jul/25 10:39 AM;;;;HLR;;0;System;System;System;System;System;System;System;System;"SUMMARY : Call Forward service

CFNRy

The purpose of the test is to verify that it is possible to provision a subscriber with CFNRy.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 3.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFNRy provisioned.
	MS is attached.";;;;;;;;;;;;;;;;;;;;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;SANTSDM-1891;;;;;;;;software;;;;;;"[{""id"":3844650,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFNRyservice in the HLR.\n\tRegister and Activate CFNRyservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFRyprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102785},{""id"":3844651,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102785}]";;;;;;;;;;;;;;;0|j1qd0g:;;;;;;;;;;;;;;2.0;;;;;;;;;;;"{""issueId"":8939083,""testStatuses"":[]}";;/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service;;;Manual;TODO;;;;;;;;;;;"Modify the subscriber subscription information to add CFNRyservice in the HLR.
	Register and Activate CFNRyservice";;"Verify that the monitored message sequence is correct.
	Verify that the provision is completed successfully.
	Verify that the ISD message is sent by the HLR containing CFRyprovision data."
CFNRy;SANTSDM-1891;8939083;Test;To Do;SANTSDM;SANDBOX_TEST_SDM;software;<EMAIL>;;;Lowest;;;<EMAIL>;<EMAIL>;11/Jul/25 6:44 PM;15/Jul/25 3:08 PM;17/Jul/25 10:39 AM;;;;HLR;;0;System;System;System;System;System;System;System;System;"SUMMARY : Call Forward service

CFNRy

The purpose of the test is to verify that it is possible to provision a subscriber with CFNRy.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 3.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFNRy provisioned.
	MS is attached.";;;;;;;;;;;;;;;;;;;;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;SANTSDM-1891;;;;;;;;software;;;;;;"[{""id"":3844650,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFNRyservice in the HLR.\n\tRegister and Activate CFNRyservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFRyprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102785},{""id"":3844651,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102785}]";;;;;;;;;;;;;;;0|j1qd0g:;;;;;;;;;;;;;;2.0;;;;;;;;;;;"{""issueId"":8939083,""testStatuses"":[]}";;/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service;;;Manual;TODO;;;;;;;;;;;Messages flows :;;"VLR
			
			
			--
			
			
			HLR
			
			
			Insert subscriber data
			
		
		
			
			VLR
			
			
			--
			
			
			HLR
			
			
			Insert subscriber data acknowledge"
CFNRc;SANTSDM-1890;8939082;Test;To Do;SANTSDM;SANDBOX_TEST_SDM;software;<EMAIL>;;;Lowest;;;<EMAIL>;<EMAIL>;11/Jul/25 6:44 PM;15/Jul/25 3:08 PM;17/Jul/25 10:39 AM;;;;HLR;;0;System;System;System;System;System;System;System;System;"SUMMARY : Call Forward service

CFNRc

The purpose of the test is to verify that it is possible to provision a subscriber with CFNRc.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 4.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFNRc provisioned.
	MS is attached.";;;;;;;;;;;;;;;;;;;;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;SANTSDM-1890;;;;;;;;software;;;;;;"[{""id"":3844648,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFNRcservice in the HLR.\n\tRegister and Activate CFNRcservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFNRcprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102784},{""id"":3844649,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102784}]";;;;;;;;;;;;;;;0|j1qd08:;;;;;;;;;;;;;;2.0;;;;;;;;;;;"{""issueId"":8939082,""testStatuses"":[]}";;/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service;;;Manual;TODO;;;;;;;;;;;"Modify the subscriber subscription information to add CFNRcservice in the HLR.
	Register and Activate CFNRcservice";;"Verify that the monitored message sequence is correct.
	Verify that the provision is completed successfully.
	Verify that the ISD message is sent by the HLR containing CFNRcprovision data."
CFNRc;SANTSDM-1890;8939082;Test;To Do;SANTSDM;SANDBOX_TEST_SDM;software;<EMAIL>;;;Lowest;;;<EMAIL>;<EMAIL>;11/Jul/25 6:44 PM;15/Jul/25 3:08 PM;17/Jul/25 10:39 AM;;;;HLR;;0;System;System;System;System;System;System;System;System;"SUMMARY : Call Forward service

CFNRc

The purpose of the test is to verify that it is possible to provision a subscriber with CFNRc.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 4.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFNRc provisioned.
	MS is attached.";;;;;;;;;;;;;;;;;;;;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;SANTSDM-1890;;;;;;;;software;;;;;;"[{""id"":3844648,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFNRcservice in the HLR.\n\tRegister and Activate CFNRcservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFNRcprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102784},{""id"":3844649,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102784}]";;;;;;;;;;;;;;;0|j1qd08:;;;;;;;;;;;;;;2.0;;;;;;;;;;;"{""issueId"":8939082,""testStatuses"":[]}";;/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service;;;Manual;TODO;;;;;;;;;;;Messages flows :;;"VLR
			
			
			--
			
			
			HLR
			
			
			Insert subscriber data
			
		
		
			
			VLR
			
			
			--
			
			
			HLR
			
			
			Insert subscriber data acknowledge"
