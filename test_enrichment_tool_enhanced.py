#!/usr/bin/env python3
"""
Outil d'enrichissement amélioré avec correction intégrée
Intègre directement toute la logique de correction pour un enrichissement parfait dès le départ
Basé sur l'analyse complète des règles Core Network et standards 3GPP
"""

import pandas as pd
import csv
import re
import sys
from typing import List, Tuple, Dict
from core_network_rules_complete import CoreNetworkRulesComplete

class TestEnrichmentToolEnhanced:
    """
    Version améliorée qui intègre directement toutes les corrections
    """
    
    def __init__(self):
        # Charger les règles complètes Core Network
        self.rules = CoreNetworkRulesComplete()

        # Règles d'enrichissement simplifiées pour compatibilité
        self.enrichment_rules = {
            # HLR (2G/3G) - Services traditionnels
            'hlr': {
                'patterns': [r'call\s*forward', r'cfu', r'cfb', r'cfnr', r'call\s*barr', r'supplementary', 
                           r'camel', r'hlr', r'map', r'sri', r'ati', r'2g', r'3g', r'gsm'],
                'base_labels': ['Functional', 'System', 'Manual', 'Regression', '2G', 'MAP'],
                'component': 'HLR',
                'specific_rules': {
                    'call_forwarding': {
                        'patterns': [r'call\s*forward', r'cfu', r'cfb', r'cfnr'],
                        'labels': ['CallForwarding', '3GPP_TS_23.082'],
                        'test_set': 'HLR Call Forwarding Services'
                    },
                    'call_barring': {
                        'patterns': [r'call\s*barr'],
                        'labels': ['CallBarring', '3GPP_TS_23.088'],
                        'test_set': 'HLR Call Barring Services'
                    },
                    'camel': {
                        'patterns': [r'camel'],
                        'labels': ['CAMEL', '3GPP_TS_23.078'],
                        'test_set': 'HLR CAMEL Services'
                    }
                },
                'default_test_set': 'HLR General Services'
            },
            
            # EPC-HSS (4G) - Services LTE
            'epc_hss': {
                'patterns': [r's6a', r'ulr', r'hss', r'epc', r'diameter', r'attach', r'detach',
                           r'update.*location', r'authentication', r'nor', r'reset', r'4g', r'lte'],
                'base_labels': ['Functional', 'System', 'Manual', 'Regression', '4G', 'Diameter', 'S6a'],
                'component': 'EPC-HSS',
                'specific_rules': {
                    'location': {
                        'patterns': [r'update.*location', r'ulr'],
                        'labels': ['UpdateLocation', '3GPP_TS_29.272'],
                        'test_set': 'EPC HSS Location Management'
                    },
                    'authentication': {
                        'patterns': [r'authentication', r'air'],
                        'labels': ['Authentication', '3GPP_TS_29.272'],
                        'test_set': 'EPC HSS Authentication'
                    }
                },
                'default_test_set': 'EPC HSS General Services'
            },
            
            # IMS (VoLTE/RCS)
            'ims': {
                'patterns': [r'volte', r'ims', r't-ads', r'tads', r'srvcc', r'swx', r'sh', r'cx', r'voice.*over.*lte'],
                'base_labels': ['Functional', 'System', 'Manual', 'Regression', '4G', 'IMS'],
                'component': 'IMS-HSS',
                'specific_rules': {
                    'volte': {
                        'patterns': [r'volte', r'srvcc', r'voice.*over.*lte'],
                        'labels': ['VoLTE', '3GPP_TS_23.216'],
                        'test_set': 'IMS VoLTE Services'
                    },
                    'tads': {
                        'patterns': [r't-ads', r'tads'],
                        'labels': ['TADS'],
                        'test_set': 'IMS T-ADS Services'
                    }
                },
                'default_test_set': 'IMS General Services'
            },
            
            # Sécurité
            'security': {
                'patterns': [r'security', r'vulnerability', r'cis', r'rbac', r'hardening', r'authentication'],
                'base_labels': ['NonFunctional', 'System', 'Manual', 'Regression', 'Security'],
                'component': 'Security',
                'specific_rules': {
                    'vulnerability': {
                        'patterns': [r'vulnerability', r'scan'],
                        'labels': ['Vulnerability'],
                        'test_set': 'Security Vulnerability Assessment'
                    },
                    'cis': {
                        'patterns': [r'cis'],
                        'labels': ['CIS'],
                        'test_set': 'Security CIS Compliance'
                    },
                    'rbac': {
                        'patterns': [r'rbac'],
                        'labels': ['RBAC'],
                        'test_set': 'Security RBAC Management'
                    }
                },
                'default_test_set': 'Security General Tests'
            },
            
            # Backup/Restore
            'backup': {
                'patterns': [r'backup', r'restore', r'recovery', r'schedule.*backup'],
                'base_labels': ['NonFunctional', 'System', 'Manual', 'Regression', 'BackupRestore'],
                'component': 'COMMON',
                'specific_rules': {},
                'default_test_set': 'Backup and Restore Operations'
            },
            
            # OAM/Provisioning
            'oam': {
                'patterns': [r'provisioning', r'oam', r'soap', r'mml', r'configuration', r'management'],
                'base_labels': ['Functional', 'System', 'Manual', 'Regression', 'OAM'],
                'component': 'OAM',
                'specific_rules': {
                    'soap': {
                        'patterns': [r'soap'],
                        'labels': ['SOAP'],
                        'test_set': 'OAM SOAP Interface'
                    }
                },
                'default_test_set': 'OAM and Provisioning'
            },
            
            # Performance
            'performance': {
                'patterns': [r'performance', r'load', r'stress', r'capacity'],
                'base_labels': ['NonFunctional', 'System', 'Manual', 'Regression', 'Performance'],
                'component': 'COMMON',
                'specific_rules': {},
                'default_test_set': 'Performance Testing'
            },
            
            # Résilience
            'resiliency': {
                'patterns': [r'resiliency', r'redundancy', r'failover', r'geo.*redundancy'],
                'base_labels': ['NonFunctional', 'System', 'Manual', 'Regression', 'Resiliency'],
                'component': 'COMMON',
                'specific_rules': {},
                'default_test_set': 'Resiliency and Redundancy'
            }
        }
    
    def analyze_test_content_enhanced(self, summary: str, description: str, action: str,
                                    expected_result: str, test_path: str) -> Tuple[List[str], str, str]:
        """
        Analyse améliorée avec correction intégrée utilisant les règles complètes
        """
        # Concaténer tout le contenu pour l'analyse
        full_content = f"{summary} {description} {action} {expected_result} {test_path}"

        # Utiliser les nouvelles règles complètes pour trouver le test set approprié
        test_set_name, test_set_config = self.rules.get_test_set_by_pattern(full_content)

        if test_set_name and test_set_config:
            # Utiliser les labels obligatoires du test set
            labels = test_set_config['mandatory_labels'].copy()

            # Déterminer le composant selon le domaine
            component = self._determine_component_from_test_set(test_set_name)

            # Ajouter la référence 3GPP si disponible
            gpp_ref = test_set_config.get('gpp_ref', '')
            if gpp_ref and gpp_ref not in labels:
                labels.append(gpp_ref)

            return labels, component, test_set_name

        # Fallback vers l'ancienne méthode si aucun match dans les nouvelles règles
        return self._analyze_with_legacy_rules(full_content)

    def _determine_component_from_test_set(self, test_set_name: str) -> str:
        """
        Détermine le composant selon le test set
        """
        if 'HLR' in test_set_name:
            return 'HLR'
        elif 'EPC HSS' in test_set_name:
            return 'EPC-HSS'
        elif 'IMS' in test_set_name:
            return 'IMS-HSS'
        elif 'Security' in test_set_name:
            return 'Security'
        elif 'OAM' in test_set_name:
            return 'OAM'
        elif 'Performance' in test_set_name:
            return 'COMMON'
        elif 'Backup' in test_set_name or 'Restore' in test_set_name:
            return 'COMMON'
        elif 'Resiliency' in test_set_name or 'Availability' in test_set_name:
            return 'COMMON'
        else:
            return 'COMMON'

    def _analyze_with_legacy_rules(self, full_content: str) -> Tuple[List[str], str, str]:
        """
        Méthode de fallback utilisant les anciennes règles
        """
        full_content_lower = full_content.lower()

        # Analyser selon les règles d'enrichissement avec priorité
        best_match = None
        best_score = 0

        for domain, rules in self.enrichment_rules.items():
            # Compter les matches pour ce domaine
            matches = sum(1 for pattern in rules['patterns'] if re.search(pattern, full_content_lower, re.IGNORECASE))

            if matches > best_score:
                best_score = matches
                best_match = (domain, rules)

        if best_match:
            domain, rules = best_match
            # Commencer avec les labels de base
            labels = rules['base_labels'].copy()
            component = rules['component']
            test_set = rules['default_test_set']

            # Vérifier les règles spécifiques
            for specific_type, specific_rule in rules['specific_rules'].items():
                if any(re.search(pattern, full_content_lower, re.IGNORECASE) for pattern in specific_rule['patterns']):
                    # Ajouter les labels spécifiques
                    labels.extend(specific_rule['labels'])
                    test_set = specific_rule['test_set']
                    break

            return labels, component, test_set

        # Cas par défaut si aucune règle ne correspond
        return ['Functional', 'System', 'Manual', 'Regression'], 'COMMON', 'General Tests'
    
    def enrich_csv_enhanced(self, input_file: str, output_file: str, format_type: str = 'separate', progress_callback=None):
        """
        Enrichissement amélioré avec correction intégrée
        """
        try:
            # Lecture robuste du fichier CSV avec adaptation automatique
            def read_csv_robust(file_path):
                """Lecture robuste d'un fichier CSV avec détection automatique des paramètres"""

                # Analyser le fichier pour détecter le format
                with open(file_path, 'r', encoding='utf-8') as f:
                    first_line = f.readline().strip()
                    second_line = f.readline().strip() if f.tell() < len(open(file_path, 'r', encoding='utf-8').read()) else ""

                print(f"📋 Analyse du fichier CSV :")
                print(f"   Première ligne : {len(first_line)} caractères")
                print(f"   Virgules dans l'en-tête : {first_line.count(',')}")
                print(f"   Points-virgules dans l'en-tête : {first_line.count(';')}")

                # Déterminer le séparateur
                if first_line.count(';') > first_line.count(','):
                    separator = ';'
                else:
                    separator = ','

                print(f"   Séparateur détecté : '{separator}'")

                # Essayer différentes méthodes de lecture
                read_methods = [
                    # Méthode 1 : Lecture standard
                    lambda: pd.read_csv(file_path, sep=separator, engine='python'),

                    # Méthode 2 : Avec quoting minimal
                    lambda: pd.read_csv(file_path, sep=separator, quoting=csv.QUOTE_MINIMAL, engine='python'),

                    # Méthode 3 : Avec quoting all
                    lambda: pd.read_csv(file_path, sep=separator, quoting=csv.QUOTE_ALL, engine='python'),

                    # Méthode 4 : Sans quoting avec escapechar
                    lambda: pd.read_csv(file_path, sep=separator, quoting=csv.QUOTE_NONE, engine='python', escapechar='\\'),

                    # Méthode 5 : Avec l'autre séparateur
                    lambda: pd.read_csv(file_path, sep=',' if separator == ';' else ';', engine='python'),
                ]

                for i, method in enumerate(read_methods, 1):
                    try:
                        print(f"   Tentative méthode {i}...")
                        df = method()
                        print(f"   ✅ Succès avec méthode {i} : {len(df)} lignes, {len(df.columns)} colonnes")
                        return df, separator
                    except Exception as e:
                        print(f"   ❌ Méthode {i} échouée : {str(e)[:100]}...")
                        continue

                # Si toutes les méthodes échouent
                raise Exception("Impossible de lire le fichier CSV avec toutes les méthodes testées")

            # Lire le fichier avec la méthode robuste
            df, separator = read_csv_robust(input_file)

            print(f"📊 Fichier lu avec succès :")
            print(f"   Lignes : {len(df)}")
            print(f"   Colonnes : {len(df.columns)}")
            print(f"   Format de sortie : {format_type}")

            # Afficher quelques colonnes pour diagnostic
            print(f"📋 Premières colonnes détectées :")
            for i, col in enumerate(df.columns[:10]):
                print(f"   {i+1}. '{col}'")
            if len(df.columns) > 10:
                print(f"   ... et {len(df.columns) - 10} autres colonnes")

            # Supprimer les colonnes Labels existantes pour éviter les doublons
            print("🧹 Suppression des labels existants pour éviter les doublons...")
            labels_columns = [col for col in df.columns if col == 'Labels' or col.startswith('Labels')]
            if labels_columns:
                df = df.drop(columns=labels_columns)
                print(f"   Supprimé {len(labels_columns)} colonnes Labels existantes :")
                for col in labels_columns:
                    print(f"     - '{col}'")
            else:
                print("   Aucune colonne Labels existante trouvée")

            # Première passe : collecter tous les labels pour déterminer le nombre de colonnes
            all_labels_sets = []
            total_rows = len(df)

            print("Analyse et enrichissement avec correction intégrée...")
            for index, row in df.iterrows():
                summary = str(row.get('Summary', ''))
                description = str(row.get('Description', ''))
                action = str(row.get('Action', ''))
                expected_result = str(row.get('Expected Result', ''))
                test_path = str(row.get('Custom field (Test Repository Path)', ''))
                
                # Analyse améliorée avec correction intégrée
                labels, component, test_set = self.analyze_test_content_enhanced(
                    summary, description, action, expected_result, test_path
                )
                
                all_labels_sets.append({
                    'labels': labels,
                    'component': component,
                    'test_set': test_set
                })
            
            # Déterminer le nombre maximum de labels
            max_labels = max(len(item['labels']) for item in all_labels_sets) if all_labels_sets else 0
            print(f"Nombre maximum de labels par test : {max_labels}")
            
            # Créer la structure selon le format demandé
            if format_type == 'separate':
                # Format avec colonnes séparées
                df['Test Set'] = ''
                
                # Créer les colonnes de labels
                label_columns = []
                for i in range(max_labels):
                    if i == 0:
                        df['Labels'] = ''
                        label_columns.append('Labels')
                    else:
                        temp_col_name = f'Labels_{i}'
                        df[temp_col_name] = ''
                        label_columns.append(temp_col_name)
                
                # Remplir les données
                for index, test_data in enumerate(all_labels_sets):
                    # Mettre à jour le composant
                    current_component = str(df.at[index, 'Component/s'])
                    if pd.isna(current_component) or current_component in ['', 'nan', 'COMMON']:
                        df.at[index, 'Component/s'] = test_data['component']
                    
                    # Mettre à jour le Test Set
                    df.at[index, 'Test Set'] = test_data['test_set']
                    
                    # Remplir les colonnes de labels UNIQUES
                    labels = test_data['labels']
                    print(f"   Test {index+1}: {len(labels)} labels → {labels}")

                    # Répartir chaque label dans une colonne Labels différente
                    for i in range(max_labels):
                        if i < len(labels):
                            df.at[index, label_columns[i]] = labels[i]
                            print(f"     Colonne {i+1}: '{labels[i]}'")
                        else:
                            df.at[index, label_columns[i]] = ''  # Colonne vide si pas assez de labels
                            print(f"     Colonne {i+1}: (vide)")
                    
                    # Callback de progression
                    if progress_callback:
                        progress = int((index + 1) / total_rows * 100)
                        progress_callback(progress, index + 1, total_rows)
                
                # Créer le fichier CSV manuellement pour avoir des colonnes Labels identiques
                import csv

                with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile, delimiter=',')  # Utiliser virgule comme séparateur

                    # Écrire l'en-tête avec toutes les colonnes Labels nommées identiquement
                    header = []
                    for col in df.columns:
                        if col in label_columns:
                            header.append('Labels')
                        else:
                            header.append(col)
                    writer.writerow(header)

                    # Écrire les données en nettoyant les valeurs "nan"
                    for index, row in df.iterrows():
                        clean_row = []
                        for value in row.values:
                            # Remplacer nan, NaN, "nan" par des chaînes vides
                            if pd.isna(value) or str(value).lower() in ['nan', 'none', 'null']:
                                clean_row.append('')
                            else:
                                clean_row.append(str(value))
                        writer.writerow(clean_row)
                
            else:
                # Format standard avec labels combinés
                df['Labels'] = ''
                df['Test Set'] = ''
                
                for index, test_data in enumerate(all_labels_sets):
                    # Mettre à jour le composant
                    current_component = str(df.at[index, 'Component/s'])
                    if pd.isna(current_component) or current_component in ['', 'nan', 'COMMON']:
                        df.at[index, 'Component/s'] = test_data['component']
                    
                    # Mettre à jour les labels (combinés)
                    df.at[index, 'Labels'] = ', '.join(test_data['labels'])
                    df.at[index, 'Test Set'] = test_data['test_set']
                    
                    # Callback de progression
                    if progress_callback:
                        progress = int((index + 1) / total_rows * 100)
                        progress_callback(progress, index + 1, total_rows)
                
                # Nettoyer les valeurs "nan" et sauvegarder avec séparateur point-virgule
                df_clean = df.copy()
                for col in df_clean.columns:
                    df_clean[col] = df_clean[col].apply(lambda x: '' if pd.isna(x) or str(x).lower() in ['nan', 'none', 'null'] else str(x))

                # Toujours utiliser des virgules en sortie pour la compatibilité
                print(f"📁 Sauvegarde avec séparateur virgule (,) pour compatibilité")
                df_clean.to_csv(output_file, index=False, sep=',')
            
            print(f"Fichier enrichi sauvegardé : {output_file}")
            print(f"Format utilisé : {format_type}")
            if format_type == 'separate':
                print(f"Colonnes Labels créées : {max_labels}")
            
            # Statistiques - utiliser le DataFrame en mémoire pour éviter les problèmes de parsing
            print(f"\n📊 STATISTIQUES D'ENRICHISSEMENT :")
            print(f"   Tests traités : {len(df)}")
            print(f"   Colonnes totales : {len(df.columns)}")
            if format_type == 'separate':
                print(f"   Colonnes Labels créées : {max_labels}")
            print(f"   Format utilisé : {format_type}")
            
        except Exception as e:
            print(f"Erreur lors du traitement : {e}")
            if progress_callback:
                progress_callback(-1, 0, 0)
            sys.exit(1)
    
    def print_enhanced_statistics(self, df: pd.DataFrame, format_type: str, label_columns_count: int):
        """
        Affiche des statistiques sur l'enrichissement amélioré
        """
        print("\n=== STATISTIQUES D'ENRICHISSEMENT AMÉLIORÉ ===")
        print(f"Total de tests traités : {len(df)}")
        print(f"Format utilisé : {format_type}")
        print(f"Colonnes Labels : {label_columns_count}")
        
        # Répartition par composant
        print("\nRépartition par composant :")
        component_counts = df['Component/s'].value_counts()
        for component, count in component_counts.items():
            print(f"  {component}: {count}")
        
        # Répartition par Test Set
        print("\nRépartition par Test Set :")
        test_set_counts = df['Test Set'].value_counts()
        for test_set, count in test_set_counts.items():
            print(f"  {test_set}: {count}")
        
        # Compter les labels selon le format
        if format_type == 'separate':
            # Collecter tous les labels de toutes les colonnes Labels
            all_labels = []
            labels_columns_indices = []

            # Trouver tous les indices des colonnes Labels
            for col_idx, col_name in enumerate(df.columns):
                if col_name == 'Labels':
                    labels_columns_indices.append(col_idx)

            # Collecter les labels de toutes les colonnes Labels
            for col_idx in labels_columns_indices:
                labels_in_col = df.iloc[:, col_idx].dropna()
                labels_in_col = labels_in_col[labels_in_col != '']
                all_labels.extend(labels_in_col.tolist())

            from collections import Counter
            label_counts = Counter(all_labels)

            print(f"\nTop 10 des labels les plus utilisés :")
            for label, count in label_counts.most_common(10):
                print(f"  {label}: {count}")
        else:
            # Format standard
            print(f"\nExemple de labels (premier test) :")
            if len(df) > 0:
                print(f"  {df.iloc[0]['Labels']}")

def main():
    """
    Fonction principale pour tester l'outil amélioré
    """
    if len(sys.argv) != 4:
        print("Usage: python test_enrichment_tool_enhanced.py <input_file.csv> <output_file.csv> <format>")
        print("Format: 'standard' ou 'separate'")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    format_type = sys.argv[3]
    
    print("=== OUTIL D'ENRICHISSEMENT AMÉLIORÉ - CORRECTION INTÉGRÉE ===")
    
    tool = TestEnrichmentToolEnhanced()
    tool.enrich_csv_enhanced(input_file, output_file, format_type)
    
    print("\n✅ Enrichissement amélioré terminé avec succès !")
    print(f"📁 Fichier de sortie : {output_file}")
    print("🎯 Toutes les corrections sont intégrées dès l'enrichissement !")

if __name__ == "__main__":
    main()
