﻿Test Case Identifier;Summary;Description;Component/s;Action;Expected Result;Labels;Labels;Labels;Labels;Labels;Labels;Test Set
1;CFU Test;Test Call Forwarding Unconditional service in HLR;HLR;Activate CFU service for subscriber A;CFU is activated successfully and calls are forwarded;Functional;System;Manual;CallForwarding;;;HLR Call Forwarding Services
2;CFB Test;Test Call Forwarding Busy service;HLR;Activate CFB when subscriber is busy;Calls are forwarded when subscriber is busy;Functional;Manual;System;CallForwarding;;;HLR Call Forwarding Services
3;CFNR Test;Test Call Forwarding No Reply service;HLR;Configure CFNR with 20 seconds timeout;Calls are forwarded after 20 seconds if no reply;"Functional
System
Manual
Regression
2G
MAP
CallForwarding";2G;MAP;System;CallForwarding;;HLR Call Forwarding Services
4;Call Barring Test;Test outgoing call barring supplementary service;HLR;Activate outgoing call barring for subscriber;Outgoing calls are blocked successfully;"Functional
System
Manual
Regression
2G
MAP
CallBarring";;;;;;HLR Call Barring Services
5;CAMEL Service;Test CAMEL service for prepaid subscribers;HLR;Trigger CAMEL service during call setup;CAMEL service processes the call correctly;"""Test1"",""Description"",""label1_label2_label3""";;;;;;HLR CAMEL Services
6;S6a ULR;Test Update Location Request on S6a interface between MME and HSS;EPC-HSS;Send ULR from MME to HSS with subscriber IMSI;HSS responds with ULA containing subscriber profile;"""Test1"",""Description"",""label1_label2_label3""";;;;;;EPC HSS Location Management
7;S6a AIR;Test Authentication Information Request on S6a;Security;Send AIR from MME to HSS for authentication;HSS returns authentication vectors in AIA;"""Test1"",""Description"",""label1,label2,label3""";;;;;;Security General Tests
8;S6a NOR;Test Notification Request on S6a interface;EPC-HSS;Send NOR from HSS to MME;MME acknowledges with NOA;"""Test1"",""Description"",""label1,label2,label3""";;;;;;EPC HSS General Services
9;HSS Reset;Test HSS Reset procedure on S6a interface;EPC-HSS;Send Reset Request from HSS to MME;MME responds with Reset Answer;Functional, System, Manual, Regression, 4G, Diameter, S6a;;;;;;EPC HSS General Services
10;EPC Attach;Test EPC attach procedure with authentication;Security;UE initiates attach procedure;UE successfully attaches to EPC network;NonFunctional, System, Manual, Regression, Security;;;;;;Security General Tests
11;VoLTE Call Setup;Test VoLTE call setup procedure in IMS;EPC-HSS;Initiate VoLTE call between two subscribers;Call is established successfully over IMS;Functional, System, Manual, Regression, 4G, Diameter, S6a;;;;;;EPC HSS General Services
12;IMS Registration;Test IMS registration procedure;IMS-HSS;UE registers to IMS network;Registration is successful and subscriber is authenticated;Functional, System, Manual, Regression, 4G, IMS;;;;;;IMS General Services
13;SRVCC Handover;Test SRVCC handover from VoLTE to CS domain;EPC-HSS;Initiate SRVCC during active VoLTE call;Call continues seamlessly on CS domain;Functional, System, Manual, Regression, 4G, Diameter, S6a;;;;;;EPC HSS General Services
14;T-ADS Service;Test Terminating Access Domain Selection;IMS-HSS;Incoming call triggers T-ADS procedure;Correct domain is selected for call termination;Functional, System, Manual, Regression, 4G, IMS, TADS;;;;;;IMS T-ADS Services
15;SWx Authentication;Test SWx interface authentication for non-3GPP access;Security;UE authenticates via SWx interface;Authentication is successful;NonFunctional, System, Manual, Regression, Security;;;;;;Security General Tests
16;Vulnerability Scan;Security vulnerability scanning of the system;Security;Run vulnerability scan on all system components;No critical vulnerabilities are found;NonFunctional, System, Manual, Regression, Security, Vulnerability;;;;;;Security Vulnerability Assessment
17;CIS Compliance;Test CIS benchmark compliance;Security;Verify system compliance with CIS benchmarks;System meets all CIS requirements;NonFunctional, System, Manual, Regression, Security, CIS;;;;;;Security CIS Compliance
18;RBAC Test;Test Role-Based Access Control;Security;Verify user access permissions;Users can only access authorized resources;NonFunctional, System, Manual, Regression, Security, RBAC;;;;;;Security RBAC Management
19;Security Hardening;Test system security hardening measures;Security;Apply security hardening configuration;System security is enhanced according to standards;NonFunctional, System, Manual, Regression, Security;;;;;;Security General Tests
20;Backup Operation;Test manual backup operation;COMMON;Execute manual backup of subscriber data;Backup is created successfully;NonFunctional, System, Manual, Regression, BackupRestore;;;;;;Backup and Restore Operations
21;Restore Operation;Test restore operation from backup;COMMON;Restore subscriber data from backup;Data is restored correctly and completely;NonFunctional, System, Manual, Regression, BackupRestore;;;;;;Backup and Restore Operations
22;Schedule Backup;Test scheduled automatic backup;COMMON;Configure automatic backup schedule;Backups are created automatically as scheduled;NonFunctional, System, Manual, Regression, BackupRestore;;;;;;Backup and Restore Operations
23;OAM Configuration;Test OAM configuration management;OAM;Configure network elements via OAM;Configuration is applied successfully;Functional, System, Manual, Regression, OAM;;;;;;OAM and Provisioning
24;Provisioning Test;Test subscriber provisioning via SOAP interface;OAM;Provision new subscriber via SOAP;Subscriber is created with correct profile;Functional, System, Manual, Regression, OAM, SOAP;;;;;;OAM and Provisioning
25;Performance Test;Test system performance under load;COMMON;Apply high traffic load to the system;System maintains acceptable performance;NonFunctional, System, Manual, Regression, Performance;;;;;;Performance Testing
26;Load Balancing;Test load balancing between multiple nodes;COMMON;Distribute traffic across multiple nodes;Traffic is balanced correctly;NonFunctional, System, Manual, Regression;;;;;;Non-Functional Testing
27;Geo Redundancy;Test geographical redundancy failover;COMMON;Simulate failure of primary site;Traffic fails over to secondary site successfully;NonFunctional, System, Manual, Regression, Resiliency;;;;;;Resiliency and Redundancy
