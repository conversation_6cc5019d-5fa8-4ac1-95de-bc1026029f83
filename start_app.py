#!/usr/bin/env python3
"""
Script de lancement simple pour l'application web
"""

import os
import sys

def main():
    """
    Lance l'application web avec gestion d'erreurs
    """
    print("🚀 LANCEMENT DE L'APPLICATION WEB")
    print("="*50)
    
    try:
        # Vérifier que nous sommes dans le bon répertoire
        if not os.path.exists('app.py'):
            print("❌ Fichier app.py non trouvé")
            print("   Assurez-vous d'être dans le bon répertoire")
            return False
        
        print("📋 Vérification des modules...")
        
        # Test des imports
        try:
            from app import app
            print("✅ Module app importé")
        except Exception as e:
            print(f"❌ Erreur import app: {e}")
            return False
        
        try:
            from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced
            print("✅ Module enrichissement importé")
        except Exception as e:
            print(f"❌ Erreur import enrichissement: {e}")
            return False
        
        try:
            from json_steps_processor import JSONStepsProcessor
            print("✅ Module extraction JSON importé")
        except Exception as e:
            print(f"❌ Erreur import JSON: {e}")
            return False
        
        print("\n🎯 Tous les modules sont OK")
        print("\n🌐 Lancement du serveur Flask...")
        print("   URL: http://127.0.0.1:5000")
        print("   Appuyez sur Ctrl+C pour arrêter")
        print("-"*50)
        
        # Lancer l'application
        app.run(
            debug=False,  # Désactiver le debug pour éviter les problèmes
            host='127.0.0.1',
            port=5000,
            use_reloader=False  # Désactiver le reloader pour éviter les problèmes
        )
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Application arrêtée par l'utilisateur")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur lors du lancement: {e}")
        import traceback
        print("\nDétails de l'erreur:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ Application fermée proprement")
    else:
        print("\n❌ Problème lors du lancement")
        print("\n💡 Solutions possibles:")
        print("   1. Vérifiez que vous êtes dans le bon répertoire")
        print("   2. Vérifiez que tous les fichiers sont présents")
        print("   3. Redémarrez votre terminal")
        
    input("\nAppuyez sur Entrée pour fermer...")
