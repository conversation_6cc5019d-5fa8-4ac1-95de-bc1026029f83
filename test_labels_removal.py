#!/usr/bin/env python3
"""
Test de la suppression des labels existants
"""

import pandas as pd
import os
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def create_test_file_with_existing_labels():
    """
    Crée un fichier de test avec des labels existants
    """
    data = {
        'Summary': [
            'CFU Test',
            'S6a ULR Test',
            'VoLTE Call Test'
        ],
        'Description': [
            'Test Call Forwarding Unconditional',
            'Test Update Location Request',
            'Test VoLTE call setup'
        ],
        'Component/s': ['', '', ''],
        'Action': [
            'Activate CFU',
            'Send ULR',
            'Setup call'
        ],
        'Expected Result': [
            'CFU activated',
            'ULA received',
            'Call established'
        ],
        'Labels': [
            'OldLabel1',
            'OldLabel2', 
            'OldLabel3'
        ],
        'Labels': [  # Colonne dupliquée pour tester
            'DuplicateLabel1',
            'DuplicateLabel2',
            'DuplicateLabel3'
        ]
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_with_existing_labels.csv', index=False)
    return 'test_with_existing_labels.csv'

def test_labels_removal():
    """
    Test la suppression des labels existants
    """
    print("🧪 TEST DE SUPPRESSION DES LABELS EXISTANTS")
    
    # Créer un fichier avec des labels existants
    input_file = create_test_file_with_existing_labels()
    
    print(f"\n📁 Fichier de test créé : {input_file}")
    
    # Lire le fichier original
    df_original = pd.read_csv(input_file)
    print(f"📊 Fichier original :")
    print(f"   Colonnes : {list(df_original.columns)}")
    print(f"   Labels columns : {[col for col in df_original.columns if col == 'Labels' or col.startswith('Labels')]}")
    
    # Enrichir avec suppression des labels existants
    tool = TestEnrichmentToolEnhanced()
    output_file = 'test_labels_removed.csv'
    
    print(f"\n🔄 Enrichissement avec suppression des labels existants...")
    tool.enrich_csv_enhanced(input_file, output_file, 'separate')
    
    # Lire le fichier enrichi
    df_enriched = pd.read_csv(output_file)
    print(f"\n📊 Fichier enrichi :")
    print(f"   Colonnes : {list(df_enriched.columns)}")
    
    # Compter les colonnes Labels
    labels_columns = [col for col in df_enriched.columns if col == 'Labels']
    print(f"   Colonnes Labels : {len(labels_columns)}")
    
    # Vérifier le contenu du premier test
    print(f"\n🔍 Premier test enrichi :")
    print(f"   Summary : {df_enriched.iloc[0]['Summary']}")
    print(f"   Component : {df_enriched.iloc[0]['Component/s']}")
    print(f"   Test Set : {df_enriched.iloc[0]['Test Set']}")
    
    # Afficher tous les labels du premier test
    first_test_labels = []
    for i, col in enumerate(df_enriched.columns):
        if col == 'Labels':
            value = df_enriched.iloc[0, i]
            if pd.notna(value) and value != '':
                first_test_labels.append(value)
    
    print(f"   Nouveaux labels : {first_test_labels}")
    print(f"   Nombre de labels : {len(first_test_labels)}")
    
    # Vérifier qu'il n'y a plus d'anciens labels
    old_labels_found = False
    for label in first_test_labels:
        if label in ['OldLabel1', 'OldLabel2', 'OldLabel3', 'DuplicateLabel1', 'DuplicateLabel2', 'DuplicateLabel3']:
            old_labels_found = True
            print(f"   ⚠️  Ancien label trouvé : {label}")
    
    if not old_labels_found:
        print(f"   ✅ Aucun ancien label trouvé - Suppression réussie !")
    
    # Nettoyer
    os.remove(input_file)
    os.remove(output_file)
    
    return not old_labels_found

def test_enrichment_quality():
    """
    Test la qualité de l'enrichissement après suppression
    """
    print(f"\n🎯 TEST DE QUALITÉ DE L'ENRICHISSEMENT")
    
    # Utiliser le fichier d'exemple
    if not os.path.exists('example_tests.csv'):
        print("❌ Fichier example_tests.csv non trouvé")
        return False
    
    # Enrichir avec la nouvelle version
    tool = TestEnrichmentToolEnhanced()
    output_file = 'test_quality_check.csv'
    
    print(f"🔄 Enrichissement du fichier d'exemple...")
    tool.enrich_csv_enhanced('example_tests.csv', output_file, 'separate')
    
    # Analyser les résultats
    df = pd.read_csv(output_file)
    
    print(f"\n📊 RÉSULTATS D'ENRICHISSEMENT :")
    print(f"   Tests traités : {len(df)}")
    print(f"   Colonnes totales : {len(df.columns)}")
    
    # Compter les colonnes Labels
    labels_columns = [col for col in df.columns if col == 'Labels']
    print(f"   Colonnes Labels : {len(labels_columns)}")
    
    # Analyser les composants
    component_counts = df['Component/s'].value_counts()
    print(f"\n🏗️  RÉPARTITION PAR COMPOSANT :")
    for comp, count in component_counts.items():
        print(f"   {comp:15s} : {count:2d} tests")
    
    # Analyser les Test Sets
    test_set_counts = df['Test Set'].value_counts()
    print(f"\n📁 TOP 5 TEST SETS :")
    for test_set, count in test_set_counts.head().items():
        print(f"   {test_set[:40]:40s} : {count:2d} tests")
    
    # Compter tous les labels
    all_labels = []
    for col_idx, col_name in enumerate(df.columns):
        if col_name == 'Labels':
            labels_in_col = df.iloc[:, col_idx].dropna()
            labels_in_col = labels_in_col[labels_in_col != '']
            all_labels.extend(labels_in_col.tolist())
    
    from collections import Counter
    label_counts = Counter(all_labels)
    
    print(f"\n🏷️  TOP 10 LABELS :")
    for label, count in label_counts.most_common(10):
        print(f"   {label:20s} : {count:2d} fois")
    
    # Vérifier les références 3GPP
    gpp_refs = [label for label in all_labels if '3GPP' in label]
    print(f"\n📚 RÉFÉRENCES 3GPP AJOUTÉES : {len(set(gpp_refs))}")
    for ref in set(gpp_refs):
        print(f"   {ref}")
    
    # Nettoyer
    os.remove(output_file)
    
    return len(labels_columns) >= 8 and len(set(gpp_refs)) >= 3

if __name__ == "__main__":
    print("🧪 TEST COMPLET - SUPPRESSION LABELS + ENRICHISSEMENT")
    
    # Test 1 : Suppression des labels existants
    test1 = test_labels_removal()
    
    # Test 2 : Qualité de l'enrichissement
    test2 = test_enrichment_quality()
    
    print(f"\n📊 RÉSULTATS FINAUX :")
    print(f"   Suppression labels : {'✅ OK' if test1 else '❌ ÉCHEC'}")
    print(f"   Qualité enrichissement : {'✅ OK' if test2 else '❌ ÉCHEC'}")
    
    if test1 and test2:
        print(f"\n🎉 TOUS LES TESTS PASSENT !")
        print(f"✅ Les labels existants sont supprimés correctement")
        print(f"✅ L'enrichissement produit des résultats de qualité")
        print(f"🎯 Le système est prêt pour la production")
    else:
        print(f"\n⚠️  CERTAINS TESTS ÉCHOUENT")
        print(f"💡 Vérifiez les erreurs ci-dessus")
