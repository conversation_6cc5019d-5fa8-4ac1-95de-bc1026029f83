#!/usr/bin/env python3
"""
Script de lancement de l'application web d'enrichissement
"""

import os
import sys

def main():
    print("=== LANCEMENT DE L'APPLICATION WEB D'ENRICHISSEMENT ===")
    print()
    
    # Vérifier les dépendances
    try:
        import flask
        import pandas
        import openpyxl
        print("✅ Toutes les dépendances sont installées")
    except ImportError as e:
        print(f"❌ Dépendance manquante: {e}")
        print("Installez les dépendances avec: pip install flask pandas openpyxl")
        return
    
    # Créer les dossiers nécessaires
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('processed', exist_ok=True)
    os.makedirs('templates', exist_ok=True)
    print("✅ Dossiers créés")
    
    # Vérifier les fichiers nécessaires
    required_files = [
        'app.py',
        'test_enrichment_tool_enhanced.py',
        'test_set_analyzer.py',
        'test_quality_dashboard.py',
        'json_steps_processor.py',
        'core_network_rules_complete.py',
        'templates/base.html',
        'templates/index.html',
        'templates/enrich.html'
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"❌ Fichiers manquants: {missing_files}")
        return
    
    print("✅ Tous les fichiers sont présents")
    print()
    
    # Lancer l'application
    print("🚀 Lancement de l'application web...")
    print("📱 L'application sera accessible à l'adresse: http://127.0.0.1:5000")
    print("🛑 Pour arrêter l'application, appuyez sur Ctrl+C")
    print()
    
    try:
        from app import app
        app.run(debug=True, host='127.0.0.1', port=5000)
    except KeyboardInterrupt:
        print("\n👋 Application arrêtée par l'utilisateur")
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")

if __name__ == "__main__":
    main()
