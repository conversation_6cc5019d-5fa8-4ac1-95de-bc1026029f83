#!/usr/bin/env python3
"""
Tableau de bord qualité pour les Test Sets
Métriques de conformité 3GPP et ISTQB
"""

import pandas as pd
import json
from typing import Dict, List
from test_set_analyzer import TestSetAnalyzer
from collections import Counter, defaultdict

class TestQualityDashboard:
    """
    Tableau de bord pour analyser la qualité des Test Sets
    """
    
    def __init__(self):
        self.analyzer = TestSetAnalyzer()
        
        # Métriques de qualité
        self.quality_metrics = {
            'conformity_thresholds': {
                'excellent': 95,
                'good': 85,
                'acceptable': 70,
                'poor': 50
            },
            'required_coverage': {
                '3GPP_references': 80,
                'technical_labels': 90,
                'functional_classification': 95,
                'test_set_alignment': 85
            }
        }
    
    def generate_dashboard(self, csv_file: str) -> Dict:
        """
        Génère un tableau de bord complet
        """
        df = pd.read_csv(csv_file)
        analysis = self.analyzer.analyze_test_file(csv_file)
        
        dashboard = {
            'overview': self._generate_overview(df, analysis),
            'quality_metrics': self._calculate_quality_metrics(df, analysis),
            'test_set_distribution': self._analyze_test_set_distribution(df),
            'label_analysis': self._analyze_labels(df),
            'compliance_score': self._calculate_compliance_score(df, analysis),
            'recommendations': self._generate_dashboard_recommendations(analysis),
            'trends': self._analyze_trends(df)
        }
        
        return dashboard
    
    def _generate_overview(self, df: pd.DataFrame, analysis: Dict) -> Dict:
        """
        Vue d'ensemble générale
        """
        total_tests = len(df)
        
        return {
            'total_tests': total_tests,
            'total_test_sets': df['Test Set'].nunique() if 'Test Set' in df.columns else 0,
            'total_components': df['Component/s'].nunique() if 'Component/s' in df.columns else 0,
            'tests_with_issues': len(analysis['misclassified_tests']) + len(analysis['missing_labels']),
            'overall_health': self._calculate_overall_health(analysis),
            'last_analysis': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def _calculate_quality_metrics(self, df: pd.DataFrame, analysis: Dict) -> Dict:
        """
        Calcule les métriques de qualité détaillées
        """
        total = len(df)
        
        metrics = {
            'test_set_alignment': {
                'score': round((total - len(analysis['misclassified_tests'])) / total * 100, 1) if total > 0 else 0,
                'status': 'good',
                'details': f"{len(analysis['misclassified_tests'])} tests mal classés"
            },
            'label_completeness': {
                'score': round((total - len(analysis['missing_labels'])) / total * 100, 1) if total > 0 else 0,
                'status': 'good',
                'details': f"{len(analysis['missing_labels'])} tests avec labels manquants"
            },
            'gpp_coverage': {
                'score': round((total - len(analysis['missing_3gpp_refs'])) / total * 100, 1) if total > 0 else 0,
                'status': 'acceptable',
                'details': f"{len(analysis['missing_3gpp_refs'])} tests sans référence 3GPP"
            },
            'label_consistency': {
                'score': round((total - len(analysis['label_inconsistencies'])) / total * 100, 1) if total > 0 else 0,
                'status': 'good',
                'details': f"{len(analysis['label_inconsistencies'])} tests avec incohérences"
            }
        }
        
        # Déterminer le statut basé sur les seuils
        for metric in metrics.values():
            score = metric['score']
            if score >= self.quality_metrics['conformity_thresholds']['excellent']:
                metric['status'] = 'excellent'
            elif score >= self.quality_metrics['conformity_thresholds']['good']:
                metric['status'] = 'good'
            elif score >= self.quality_metrics['conformity_thresholds']['acceptable']:
                metric['status'] = 'acceptable'
            else:
                metric['status'] = 'poor'
        
        return metrics
    
    def _analyze_test_set_distribution(self, df: pd.DataFrame) -> Dict:
        """
        Analyse la distribution des Test Sets
        """
        if 'Test Set' not in df.columns:
            return {'error': 'Colonne Test Set non trouvée'}
        
        test_set_counts = df['Test Set'].value_counts()
        
        return {
            'total_test_sets': len(test_set_counts),
            'largest_test_set': {
                'name': test_set_counts.index[0] if len(test_set_counts) > 0 else 'N/A',
                'count': test_set_counts.iloc[0] if len(test_set_counts) > 0 else 0
            },
            'smallest_test_set': {
                'name': test_set_counts.index[-1] if len(test_set_counts) > 0 else 'N/A',
                'count': test_set_counts.iloc[-1] if len(test_set_counts) > 0 else 0
            },
            'distribution': test_set_counts.head(10).to_dict(),
            'balance_score': self._calculate_balance_score(test_set_counts)
        }
    
    def _analyze_labels(self, df: pd.DataFrame) -> Dict:
        """
        Analyse détaillée des labels
        """
        all_labels = []
        
        # Collecter tous les labels
        for col in df.columns:
            if col == 'Labels':
                labels_in_col = df[col].dropna()
                labels_in_col = labels_in_col[labels_in_col != '']
                all_labels.extend(labels_in_col.tolist())
        
        label_counts = Counter(all_labels)
        
        # Analyser les types de labels
        functional_labels = [l for l in all_labels if 'Functional' in l]
        technical_labels = [l for l in all_labels if l in ['2G', '3G', '4G', '5G', 'MAP', 'Diameter', 'S6a', 'IMS', 'SOAP']]
        domain_labels = [l for l in all_labels if l in ['HLR', 'HSS', 'EPC-HSS', 'IMS-HSS', 'Security', 'OAM']]
        
        return {
            'total_labels': len(all_labels),
            'unique_labels': len(label_counts),
            'most_used': label_counts.most_common(10),
            'label_types': {
                'functional': len(functional_labels),
                'technical': len(technical_labels),
                'domain': len(domain_labels)
            },
            'coverage_analysis': {
                'has_functional_classification': len([l for l in all_labels if 'Functional' in l or 'NonFunctional' in l]) / len(df) * 100,
                'has_technical_labels': len(technical_labels) / len(df) * 100,
                'has_domain_labels': len(domain_labels) / len(df) * 100
            }
        }
    
    def _calculate_compliance_score(self, df: pd.DataFrame, analysis: Dict) -> Dict:
        """
        Calcule un score de conformité global
        """
        total = len(df)
        
        # Pondération des critères
        weights = {
            'test_set_alignment': 0.3,
            'label_completeness': 0.25,
            'gpp_coverage': 0.2,
            'label_consistency': 0.25
        }
        
        scores = {
            'test_set_alignment': (total - len(analysis['misclassified_tests'])) / total * 100,
            'label_completeness': (total - len(analysis['missing_labels'])) / total * 100,
            'gpp_coverage': (total - len(analysis['missing_3gpp_refs'])) / total * 100,
            'label_consistency': (total - len(analysis['label_inconsistencies'])) / total * 100
        }
        
        weighted_score = sum(scores[criterion] * weight for criterion, weight in weights.items())
        
        # Déterminer le niveau
        if weighted_score >= 90:
            level = 'EXCELLENT'
            color = 'green'
        elif weighted_score >= 80:
            level = 'GOOD'
            color = 'blue'
        elif weighted_score >= 70:
            level = 'ACCEPTABLE'
            color = 'orange'
        else:
            level = 'NEEDS_IMPROVEMENT'
            color = 'red'
        
        return {
            'overall_score': round(weighted_score, 1),
            'level': level,
            'color': color,
            'breakdown': {k: round(v, 1) for k, v in scores.items()},
            'weights': weights
        }
    
    def _generate_dashboard_recommendations(self, analysis: Dict) -> List[Dict]:
        """
        Génère des recommandations pour le tableau de bord
        """
        recommendations = []
        
        # Top 3 des actions prioritaires
        if analysis['misclassified_tests']:
            recommendations.append({
                'priority': 'HIGH',
                'title': 'Réorganiser les Test Sets',
                'description': f"{len(analysis['misclassified_tests'])} tests mal classés",
                'action': 'Utiliser le correcteur automatique',
                'impact': 'Améliore la traçabilité et la maintenance'
            })
        
        if analysis['missing_labels']:
            recommendations.append({
                'priority': 'MEDIUM',
                'title': 'Compléter les Labels',
                'description': f"{len(analysis['missing_labels'])} tests avec labels manquants",
                'action': 'Ajouter les labels techniques obligatoires',
                'impact': 'Améliore la recherche et le reporting'
            })
        
        if analysis['missing_3gpp_refs']:
            recommendations.append({
                'priority': 'LOW',
                'title': 'Ajouter Références 3GPP',
                'description': f"{len(analysis['missing_3gpp_refs'])} tests sans référence",
                'action': 'Mapper avec les spécifications 3GPP',
                'impact': 'Améliore la traçabilité normative'
            })
        
        return recommendations
    
    def _analyze_trends(self, df: pd.DataFrame) -> Dict:
        """
        Analyse les tendances (simulation pour l'exemple)
        """
        return {
            'test_growth': {
                'current_month': len(df),
                'trend': 'stable',
                'projection': len(df) + 5
            },
            'quality_evolution': {
                'current_score': 85.2,
                'previous_score': 82.1,
                'trend': 'improving'
            },
            'coverage_gaps': [
                'Tests 5G manquants',
                'Couverture IMS incomplète',
                'Tests de performance limités'
            ]
        }
    
    def _calculate_overall_health(self, analysis: Dict) -> str:
        """
        Calcule la santé globale du référentiel
        """
        total_issues = (len(analysis['misclassified_tests']) + 
                       len(analysis['missing_labels']) + 
                       len(analysis['label_inconsistencies']))
        
        if total_issues == 0:
            return 'EXCELLENT'
        elif total_issues <= 5:
            return 'GOOD'
        elif total_issues <= 15:
            return 'ACCEPTABLE'
        else:
            return 'NEEDS_ATTENTION'
    
    def _calculate_balance_score(self, test_set_counts: pd.Series) -> float:
        """
        Calcule un score d'équilibre des Test Sets
        """
        if len(test_set_counts) == 0:
            return 0
        
        # Coefficient de variation (plus bas = plus équilibré)
        mean_count = test_set_counts.mean()
        std_count = test_set_counts.std()
        
        if mean_count == 0:
            return 0
        
        cv = std_count / mean_count
        # Convertir en score (0-100, plus haut = plus équilibré)
        balance_score = max(0, 100 - (cv * 50))
        
        return round(balance_score, 1)
    
    def export_dashboard_html(self, dashboard: Dict, output_file: str):
        """
        Exporte le tableau de bord en HTML
        """
        html_template = """<!DOCTYPE html>
<html>
<head>
    <title>Tableau de Bord Qualité Test Sets</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .metric {{ display: inline-block; margin: 10px; padding: 15px; border-radius: 5px; }}
        .excellent {{ background-color: #d4edda; }}
        .good {{ background-color: #cce5ff; }}
        .acceptable {{ background-color: #fff3cd; }}
        .poor {{ background-color: #f8d7da; }}
        .score {{ font-size: 24px; font-weight: bold; }}
        .recommendation {{ margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; }}
        .high {{ border-color: #dc3545; }}
        .medium {{ border-color: #ffc107; }}
        .low {{ border-color: #28a745; }}
    </style>
</head>
<body>
    <h1>📊 Tableau de Bord Qualité Test Sets</h1>

    <h2>Vue d'ensemble</h2>
    <p><strong>Total tests:</strong> {total_tests}</p>
    <p><strong>Test Sets:</strong> {total_test_sets}</p>
    <p><strong>Santé globale:</strong> {overall_health}</p>

    <h2>Métriques de Qualité</h2>
    <div class="metric {test_set_status}">
        <div>Alignement Test Sets</div>
        <div class="score">{test_set_score}%</div>
    </div>
    <div class="metric {label_status}">
        <div>Complétude Labels</div>
        <div class="score">{label_score}%</div>
    </div>
    <div class="metric {gpp_status}">
        <div>Couverture 3GPP</div>
        <div class="score">{gpp_score}%</div>
    </div>

    <h2>Score de Conformité Global</h2>
    <div class="metric {compliance_color}">
        <div>Score Global</div>
        <div class="score">{compliance_score}%</div>
        <div>{compliance_level}</div>
    </div>

    <h2>Recommandations</h2>
    {recommendations_html}

    <p><em>Dernière analyse: {last_analysis}</em></p>
</body>
</html>"""
        
        # Préparer les données pour le template
        overview = dashboard['overview']
        quality = dashboard['quality_metrics']
        compliance = dashboard['compliance_score']
        
        recommendations_html = ""
        for rec in dashboard['recommendations']:
            recommendations_html += f"""
            <div class="recommendation {rec['priority'].lower()}">
                <strong>{rec['title']}</strong><br>
                {rec['description']}<br>
                <em>Action: {rec['action']}</em>
            </div>
            """
        
        html_content = html_template.format(
            total_tests=overview['total_tests'],
            total_test_sets=overview['total_test_sets'],
            overall_health=overview['overall_health'],
            test_set_score=quality['test_set_alignment']['score'],
            test_set_status=quality['test_set_alignment']['status'],
            label_score=quality['label_completeness']['score'],
            label_status=quality['label_completeness']['status'],
            gpp_score=quality['gpp_coverage']['score'],
            gpp_status=quality['gpp_coverage']['status'],
            compliance_score=compliance['overall_score'],
            compliance_level=compliance['level'],
            compliance_color=compliance['color'],
            recommendations_html=recommendations_html,
            last_analysis=overview['last_analysis']
        )
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

def main():
    """
    Fonction principale pour générer le tableau de bord
    """
    dashboard = TestQualityDashboard()
    
    if not os.path.exists('example_tests_separate_labels.csv'):
        print("❌ Fichier example_tests_separate_labels.csv non trouvé")
        return
    
    print("📊 Génération du tableau de bord qualité...")
    
    # Générer le tableau de bord
    dashboard_data = dashboard.generate_dashboard('example_tests_separate_labels.csv')
    
    # Afficher les résultats
    print(f"\n📈 TABLEAU DE BORD QUALITÉ")
    print(f"   • Total tests: {dashboard_data['overview']['total_tests']}")
    print(f"   • Santé globale: {dashboard_data['overview']['overall_health']}")
    print(f"   • Score de conformité: {dashboard_data['compliance_score']['overall_score']}% ({dashboard_data['compliance_score']['level']})")
    
    print(f"\n🎯 MÉTRIQUES DÉTAILLÉES")
    for metric, data in dashboard_data['quality_metrics'].items():
        print(f"   • {metric.replace('_', ' ').title()}: {data['score']}% ({data['status']})")
    
    print(f"\n💡 RECOMMANDATIONS PRIORITAIRES")
    for rec in dashboard_data['recommendations']:
        print(f"   • [{rec['priority']}] {rec['title']}: {rec['description']}")
    
    # Exporter en HTML
    dashboard.export_dashboard_html(dashboard_data, 'quality_dashboard.html')
    print(f"\n✅ Tableau de bord HTML généré: quality_dashboard.html")
    
    # Sauvegarder les données JSON (convertir les types numpy)
    def convert_numpy(obj):
        if hasattr(obj, 'item'):
            return obj.item()
        elif hasattr(obj, 'tolist'):
            return obj.tolist()
        return obj

    # Convertir récursivement les objets numpy
    import json
    dashboard_json = json.loads(json.dumps(dashboard_data, default=convert_numpy))

    with open('dashboard_data.json', 'w', encoding='utf-8') as f:
        json.dump(dashboard_json, f, indent=2, ensure_ascii=False)
    print(f"📄 Données JSON sauvegardées: dashboard_data.json")

if __name__ == "__main__":
    import os
    main()
