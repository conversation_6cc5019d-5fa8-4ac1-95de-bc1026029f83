#!/usr/bin/env python3
"""
Test pour vérifier que toutes les colonnes sont nommées exactement "Labels"
"""

import pandas as pd
import os
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def test_format_labels_identiques():
    """
    Test que toutes les colonnes Labels sont nommées exactement "Labels"
    """
    print("🧪 TEST FORMAT LABELS IDENTIQUES")
    print("="*70)
    
    # Créer un fichier avec l'exemple de votre guide
    data = {
        'Summary': ['S6a ULR', 'VoLTE Call'],
        'Description': ['Test Update Location Request', 'Test VoLTE call setup'],
        'Component/s': ['EPC-HSS', 'IMS-HSS'],
        'Action': ['Send ULR', 'Setup call'],
        'Data': ['', ''],
        'Expected Result': ['HSS responds', 'Call established']
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_format_labels.csv', index=False)
    
    print(f"📁 Fichier créé avec votre exemple du guide")
    print(f"   Test 1: EPC-HSS → devrait avoir Functional, System, Manual, Regression, 4G, Diameter, S6a")
    print(f"   Test 2: IMS-HSS → devrait avoir Functional, System, Manual, Regression, 4G, IMS, VoLTE")
    
    # Test d'enrichissement
    try:
        tool = TestEnrichmentToolEnhanced()
        tool.enrich_csv_enhanced('test_format_labels.csv', 'test_format_enriched.csv', 'separate')
        
        print("✅ Enrichissement terminé")
        
        # Analyser le fichier enrichi
        if os.path.exists('test_format_enriched.csv'):
            with open('test_format_enriched.csv', 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
            
            if lines:
                header_line = lines[0]
                header_parts = header_line.split(',')
                
                print(f"\n📋 ANALYSE DU FORMAT GÉNÉRÉ :")
                print(f"   Lignes totales : {len(lines)}")
                print(f"   Colonnes totales : {len(header_parts)}")
                
                # Afficher l'en-tête complet
                print(f"\n📋 En-tête complet :")
                for i, col in enumerate(header_parts):
                    marker = " ← LABELS" if col == 'Labels' else ""
                    print(f"   {i+1:2d}. {col}{marker}")
                
                # Compter les colonnes Labels
                labels_count = header_parts.count('Labels')
                print(f"\n📊 Colonnes nommées 'Labels' : {labels_count}")
                
                # Vérifier qu'il n'y a pas de Labels_1, Labels_2, etc.
                problematic_labels = [col for col in header_parts if col.startswith('Labels_')]
                if problematic_labels:
                    print(f"❌ Colonnes problématiques trouvées : {problematic_labels}")
                else:
                    print(f"✅ Aucune colonne 'Labels_X' trouvée")
                
                # Afficher le format exact de l'en-tête Labels
                labels_positions = [i for i, col in enumerate(header_parts) if col == 'Labels']
                print(f"\n📋 Positions des colonnes 'Labels' : {labels_positions}")
                
                # Créer la représentation du format
                labels_header = ','.join(['Labels'] * labels_count)
                print(f"\n📋 Format de l'en-tête Labels :")
                print(f"   {labels_header}")
                
                # Afficher les données
                print(f"\n📋 Données des tests :")
                for i, line in enumerate(lines[1:], 1):
                    if line.strip():
                        data_parts = line.split(',')
                        
                        # Extraire les labels
                        test_labels = []
                        for pos in labels_positions:
                            if pos < len(data_parts):
                                label = data_parts[pos].strip()
                                test_labels.append(label if label else '(vide)')
                        
                        summary = data_parts[0] if len(data_parts) > 0 else f"Test {i}"
                        print(f"   Test {i} ({summary}): {','.join(test_labels)}")
                
                # Vérifier le format attendu
                expected_format = labels_count == len(problematic_labels) == 0 and labels_count >= 6
                
                print(f"\n🎯 VÉRIFICATION DU FORMAT :")
                print(f"✅ Toutes colonnes nommées 'Labels' : {'OUI' if len(problematic_labels) == 0 else 'NON'}")
                print(f"✅ Nombre de colonnes Labels : {labels_count}")
                print(f"✅ Format conforme : {'OUI' if expected_format else 'NON'}")
                
                if len(problematic_labels) == 0 and labels_count >= 6:
                    print(f"\n🎉 FORMAT PARFAIT !")
                    print(f"📊 Résumé :")
                    print(f"   {labels_count} colonnes toutes nommées 'Labels'")
                    print(f"   Format: {labels_header}")
                    print(f"   Aucune colonne 'Labels_X' indésirable")
                    print(f"🎯 Exactement comme demandé !")
                    success = True
                else:
                    print(f"\n⚠️  Format à ajuster")
                    success = False
            else:
                print("❌ Fichier enrichi vide")
                success = False
            
            # Nettoyer
            os.remove('test_format_enriched.csv')
        else:
            print("❌ Fichier enrichi non généré")
            success = False
    
    except Exception as e:
        print(f"❌ Erreur lors de l'enrichissement : {e}")
        success = False
    
    # Nettoyer
    os.remove('test_format_labels.csv')
    
    return success

def test_format_avec_votre_exemple():
    """
    Test avec l'exemple exact de votre guide
    """
    print("\n" + "="*70)
    print("📋 TEST AVEC VOTRE EXEMPLE EXACT DU GUIDE")
    print("="*70)
    
    # Créer un fichier CSV avec l'exemple exact de votre guide
    csv_content = '''Summary,Description,Component/s,Action,Data,Expected Result
S6a ULR,Test Update Location Request,EPC-HSS,Send ULR,,HSS responds
VoLTE Call,Test VoLTE call setup,IMS-HSS,Setup call,,Call established'''
    
    with open('test_guide_exemple.csv', 'w', encoding='utf-8') as f:
        f.write(csv_content)
    
    print(f"📁 Fichier créé avec votre exemple exact")
    print(f"   Format attendu après enrichissement :")
    print(f"   Labels,Labels,Labels,Labels,Labels,Labels,Labels")
    print(f"   Functional,System,Manual,Regression,4G,Diameter,S6a")
    print(f"   Functional,System,Manual,Regression,4G,IMS,VoLTE")
    
    # Test d'enrichissement
    try:
        tool = TestEnrichmentToolEnhanced()
        tool.enrich_csv_enhanced('test_guide_exemple.csv', 'test_guide_enriched.csv', 'separate')
        
        print("✅ Enrichissement terminé")
        
        # Analyser le résultat
        if os.path.exists('test_guide_enriched.csv'):
            with open('test_guide_enriched.csv', 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 CONTENU DU FICHIER ENRICHI :")
            lines = content.strip().split('\n')
            for i, line in enumerate(lines):
                print(f"   {i+1}. {line}")
            
            # Vérifier spécifiquement l'en-tête Labels
            if lines:
                header = lines[0]
                labels_count = header.count('Labels')
                
                # Extraire seulement la partie Labels de l'en-tête
                header_parts = header.split(',')
                labels_only = [col for col in header_parts if col == 'Labels']
                labels_header_format = ','.join(labels_only)
                
                print(f"\n📊 VÉRIFICATION SPÉCIFIQUE :")
                print(f"   Colonnes 'Labels' : {labels_count}")
                print(f"   Format Labels uniquement : {labels_header_format}")
                
                # Vérifier que c'est exactement ce que vous voulez
                is_perfect = labels_count >= 6 and all(col == 'Labels' for col in labels_only)
                
                print(f"✅ Format parfait : {'OUI' if is_perfect else 'NON'}")
                
                if is_perfect:
                    print(f"\n🎉 VOTRE DEMANDE EST PARFAITEMENT SATISFAITE !")
                    print(f"✅ Toutes les colonnes sont nommées exactement 'Labels'")
                    print(f"✅ Pas de 'Labels_1', 'Labels_2', etc.")
                    print(f"✅ Format: Labels,Labels,Labels,Labels,Labels,Labels,Labels")
                    success = True
                else:
                    success = False
            else:
                success = False
            
            # Nettoyer
            os.remove('test_guide_enriched.csv')
        else:
            success = False
    
    except Exception as e:
        print(f"❌ Erreur : {e}")
        success = False
    
    # Nettoyer
    os.remove('test_guide_exemple.csv')
    
    return success

def main():
    """
    Test complet du format Labels identiques
    """
    print("🧪 VALIDATION FORMAT LABELS IDENTIQUES")
    
    # Test 1 : Format général
    test1 = test_format_labels_identiques()
    
    # Test 2 : Exemple exact du guide
    test2 = test_format_avec_votre_exemple()
    
    print("\n" + "="*70)
    print("📊 RÉSULTATS FINAUX")
    print("="*70)
    
    print(f"✅ Format Labels identiques : {'OK' if test1 else 'ÉCHEC'}")
    print(f"✅ Exemple guide parfait : {'OK' if test2 else 'ÉCHEC'}")
    
    if test1 and test2:
        print(f"\n🎉 FORMAT LABELS PARFAITEMENT CONFORME !")
        print(f"✅ Toutes les colonnes nommées exactement 'Labels'")
        print(f"✅ Pas de suffixes '_1', '_2', etc.")
        print(f"✅ Format: Labels,Labels,Labels,Labels,Labels,Labels")
        print(f"✅ Compatible avec votre workflow")
        print(f"🎯 Votre demande est parfaitement implémentée !")
    else:
        print(f"\n⚠️  Des ajustements peuvent être nécessaires")

if __name__ == "__main__":
    main()
