Summary,Description,Component/s,Action,Expected Result,Test Set,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Labels
CFU Test,Test Call Forwarding Unconditional service in HLR,HLR,Activate CFU service for subscriber A,CFU is activated successfully and calls are forwarded,HLR Call Forwarding Services,Functional,System,Manual,Regression,2G,MAP,CallForwarding,
CFB Test,Test Call Forwarding Busy service,HLR,Activate CFB when subscriber is busy,Calls are forwarded when subscriber is busy,HLR Call Forwarding Services,Functional,System,Manual,Regression,2G,MAP,CallForwarding,
CFNR Test,Test Call Forwarding No Reply service,HLR,Configure CFNR with 20 seconds timeout,Calls are forwarded after 20 seconds if no reply,HLR Call Forwarding Services,Functional,System,Manual,Regression,2G,MAP,CallForwarding,
Call Barring Test,Test outgoing call barring supplementary service,HLR,Activate outgoing call barring for subscriber,Outgoing calls are blocked successfully,HLR Call Barring Services,Functional,System,Manual,Regression,2G,MAP,CallBarring,
CAMEL Service,Test CAMEL service for prepaid subscribers,HLR,Trigger CAMEL service during call setup,CAMEL service processes the call correctly,HLR CAMEL Services,Functional,System,Manual,Regression,2G,MAP,CAMEL,
S6a ULR,Test Update Location Request on S6a interface between MME and HSS,EPC-HSS,Send ULR from MME to HSS with subscriber IMSI,HSS responds with ULA containing subscriber profile,EPC HSS Location Management,Functional,System,Manual,Regression,4G,Diameter,S6a,UpdateLocation
S6a AIR,Test Authentication Information Request on S6a,Security,Send AIR from MME to HSS for authentication,HSS returns authentication vectors in AIA,Security General Tests,NonFunctional,System,Manual,Regression,Security,,,
S6a NOR,Test Notification Request on S6a interface,EPC-HSS,Send NOR from HSS to MME,MME acknowledges with NOA,EPC HSS General Services,Functional,System,Manual,Regression,4G,Diameter,S6a,
HSS Reset,Test HSS Reset procedure on S6a interface,EPC-HSS,Send Reset Request from HSS to MME,MME responds with Reset Answer,EPC HSS General Services,Functional,System,Manual,Regression,4G,Diameter,S6a,
EPC Attach,Test EPC attach procedure with authentication,Security,UE initiates attach procedure,UE successfully attaches to EPC network,Security General Tests,NonFunctional,System,Manual,Regression,Security,,,
VoLTE Call Setup,Test VoLTE call setup procedure in IMS,EPC-HSS,Initiate VoLTE call between two subscribers,Call is established successfully over IMS,EPC HSS General Services,Functional,System,Manual,Regression,4G,Diameter,S6a,
IMS Registration,Test IMS registration procedure,IMS-HSS,UE registers to IMS network,Registration is successful and subscriber is authenticated,IMS General Services,Functional,System,Manual,Regression,4G,IMS,,
SRVCC Handover,Test SRVCC handover from VoLTE to CS domain,EPC-HSS,Initiate SRVCC during active VoLTE call,Call continues seamlessly on CS domain,EPC HSS General Services,Functional,System,Manual,Regression,4G,Diameter,S6a,
T-ADS Service,Test Terminating Access Domain Selection,IMS-HSS,Incoming call triggers T-ADS procedure,Correct domain is selected for call termination,IMS T-ADS Services,Functional,System,Manual,Regression,4G,IMS,TADS,
SWx Authentication,Test SWx interface authentication for non-3GPP access,Security,UE authenticates via SWx interface,Authentication is successful,Security General Tests,NonFunctional,System,Manual,Regression,Security,,,
Vulnerability Scan,Security vulnerability scanning of the system,Security,Run vulnerability scan on all system components,No critical vulnerabilities are found,Security Vulnerability Assessment,NonFunctional,System,Manual,Regression,Security,Vulnerability,,
CIS Compliance,Test CIS benchmark compliance,Security,Verify system compliance with CIS benchmarks,System meets all CIS requirements,Security CIS Compliance,NonFunctional,System,Manual,Regression,Security,CIS,,
RBAC Test,Test Role-Based Access Control,Security,Verify user access permissions,Users can only access authorized resources,Security RBAC Management,NonFunctional,System,Manual,Regression,Security,RBAC,,
Security Hardening,Test system security hardening measures,Security,Apply security hardening configuration,System security is enhanced according to standards,Security General Tests,NonFunctional,System,Manual,Regression,Security,,,
Backup Operation,Test manual backup operation,COMMON,Execute manual backup of subscriber data,Backup is created successfully,Backup and Restore Operations,NonFunctional,System,Manual,Regression,BackupRestore,,,
Restore Operation,Test restore operation from backup,COMMON,Restore subscriber data from backup,Data is restored correctly and completely,Backup and Restore Operations,NonFunctional,System,Manual,Regression,BackupRestore,,,
Schedule Backup,Test scheduled automatic backup,COMMON,Configure automatic backup schedule,Backups are created automatically as scheduled,Backup and Restore Operations,NonFunctional,System,Manual,Regression,BackupRestore,,,
OAM Configuration,Test OAM configuration management,OAM,Configure network elements via OAM,Configuration is applied successfully,OAM and Provisioning,Functional,System,Manual,Regression,OAM,,,
Provisioning Test,Test subscriber provisioning via SOAP interface,OAM,Provision new subscriber via SOAP,Subscriber is created with correct profile,OAM and Provisioning,Functional,System,Manual,Regression,OAM,SOAP,,
Performance Test,Test system performance under load,COMMON,Apply high traffic load to the system,System maintains acceptable performance,Performance Testing,NonFunctional,System,Manual,Regression,Performance,,,
Load Balancing,Test load balancing between multiple nodes,COMMON,Distribute traffic across multiple nodes,Traffic is balanced correctly,Non-Functional Testing,NonFunctional,System,Manual,Regression,,,,
Geo Redundancy,Test geographical redundancy failover,COMMON,Simulate failure of primary site,Traffic fails over to secondary site successfully,Resiliency and Redundancy,NonFunctional,System,Manual,Regression,Resiliency,,,
