#!/usr/bin/env python3
"""
Démonstration complète de l'outil d'enrichissement
"""

import os
import sys
import time

def print_header(title):
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_step(step, description):
    print(f"\n🔸 Étape {step}: {description}")

def main():
    print_header("DÉMONSTRATION COMPLÈTE - ENRICHISSEMENT TESTS JIRA XRAY")
    
    print("""
🎯 Cette démonstration vous montre :
   1. L'enrichissement en ligne de commande
   2. L'application web interactive
   3. Les résultats obtenus
    """)
    
    # Étape 1: Vérification des prérequis
    print_step(1, "Vérification des prérequis")
    
    try:
        import pandas as pd
        import flask
        import openpyxl
        print("✅ Toutes les dépendances sont installées")
    except ImportError as e:
        print(f"❌ Dépendance manquante: {e}")
        print("Installez avec: pip install flask pandas openpyxl")
        return
    
    # Étape 2: Test de l'enrichissement en ligne de commande
    print_step(2, "Test de l'enrichissement en ligne de commande")
    
    if os.path.exists('example_tests.csv'):
        print("📄 Utilisation du fichier d'exemple: example_tests.csv")
        
        # Enrichissement
        from test_enrichment_tool import TestEnrichmentTool
        tool = TestEnrichmentTool()
        
        print("⚡ Enrichissement en cours...")
        tool.enrich_csv('example_tests.csv', 'demo_enriched.csv')
        
        # Création du fichier Excel
        from create_excel_output import create_excel_output
        print("📊 Création du fichier Excel...")
        create_excel_output('demo_enriched.csv', 'demo_enriched.xlsx')
        
        print("✅ Enrichissement terminé !")
        print("📁 Fichiers créés:")
        print("   - demo_enriched.csv")
        print("   - demo_enriched.xlsx")
        
        # Affichage des statistiques
        df = pd.read_csv('demo_enriched.csv')
        print(f"\n📊 Statistiques:")
        print(f"   - {len(df)} tests enrichis")
        print(f"   - {len(df['Component/s'].unique())} composants identifiés")
        print(f"   - {len(df['Test Set'].unique())} test sets créés")
        
        functional_count = df['Labels'].str.contains('Functional', na=False).sum()
        non_functional_count = df['Labels'].str.contains('NonFunctional', na=False).sum()
        print(f"   - {functional_count} tests fonctionnels")
        print(f"   - {non_functional_count} tests non-fonctionnels")
        
    else:
        print("❌ Fichier example_tests.csv non trouvé")
    
    # Étape 3: Lancement de l'application web
    print_step(3, "Application web interactive")
    
    print("""
🌐 L'application web offre une interface conviviale pour :
   ✅ Télécharger vos fichiers par glisser-déposer
   ✅ Valider automatiquement le format
   ✅ Enrichir avec suivi en temps réel
   ✅ Télécharger les résultats (CSV + Excel)
   ✅ Consulter les règles et l'aide
    """)
    
    response = input("\n❓ Voulez-vous lancer l'application web ? (o/n): ")
    
    if response.lower() in ['o', 'oui', 'y', 'yes']:
        print("\n🚀 Lancement de l'application web...")
        print("📱 L'application sera accessible à: http://127.0.0.1:5000")
        print("🛑 Pour arrêter, appuyez sur Ctrl+C dans le terminal")
        print("\n⏳ Lancement dans 3 secondes...")
        
        for i in range(3, 0, -1):
            print(f"   {i}...")
            time.sleep(1)
        
        try:
            from app import app
            app.run(debug=False, host='127.0.0.1', port=5000)
        except KeyboardInterrupt:
            print("\n👋 Application arrêtée")
        except Exception as e:
            print(f"\n❌ Erreur: {e}")
    
    # Étape 4: Résumé
    print_step(4, "Résumé et prochaines étapes")
    
    print("""
🎉 Démonstration terminée !

📁 Fichiers disponibles :
   - demo_enriched.csv      (Résultats CSV)
   - demo_enriched.xlsx     (Résultats Excel avec 4 feuilles)
   - example_tests.csv      (Fichier d'exemple pour vos tests)

🚀 Pour utiliser l'outil :
   1. Ligne de commande : python test_enrichment_tool.py input.csv output.csv
   2. Interface web     : python run_app.py

📚 Documentation :
   - GUIDE_DEMARRAGE.md     (Guide de démarrage rapide)
   - README.md              (Documentation complète)

🔧 Personnalisation :
   - Modifiez test_enrichment_tool.py pour adapter les règles
   - Ajoutez vos propres patterns et labels
    """)

if __name__ == "__main__":
    main()
