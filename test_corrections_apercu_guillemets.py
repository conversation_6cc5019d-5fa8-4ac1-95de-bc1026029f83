#!/usr/bin/env python3
"""
Test des corrections pour l'aperçu des multiples steps et suppression des guillemets
"""

import pandas as pd
import os
from json_steps_processor import JSONStepsProcessor
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def print_header(title):
    print("\n" + "="*70)
    print(f"  {title}")
    print("="*70)

def test_apercu_multiples_steps():
    """
    Test que l'aperçu montre tous les steps séparément
    """
    print_header("👁️ TEST APERÇU MULTIPLES STEPS")
    
    # Créer un fichier avec des multiples steps
    data = {
        'Summary': ['Test Simple', 'Test Multiple Steps'],
        'Description': ['Test simple', 'Test avec plusieurs étapes'],
        'Component/s': ['', ''],
        'Custom field (Manual Test Steps)': [
            '[{"id":1,"index":1,"fields":{"Action":"Action simple","Data":"Data simple","Expected Result":"Result simple"}}]',
            '[{"id":2,"index":1,"fields":{"Action":"Première action","Data":"Première data","Expected Result":"Premier résultat"}},{"id":3,"index":2,"fields":{"Action":"Deuxième action","Data":"Deuxième data","Expected Result":"Deuxième résultat"}}]'
        ]
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_apercu_multiple.csv', index=False)
    
    print("📁 Fichier créé avec 1 test simple + 1 test avec 2 steps")
    
    # Test de l'aperçu
    processor = JSONStepsProcessor()
    samples = processor.preview_json_extraction('test_apercu_multiple.csv', max_samples=10)
    
    print(f"📊 Nombre d'échantillons dans l'aperçu : {len(samples)}")
    
    # Afficher tous les échantillons
    for i, sample in enumerate(samples):
        print(f"\n{i+1}. {sample['summary']}")
        print(f"   Action : {sample['extracted']['Action']}")
        print(f"   Data : {sample['extracted']['Data']}")
        print(f"   Expected Result : {sample['extracted']['Expected Result']}")
    
    # Vérifier qu'on a bien 3 échantillons (1 + 2)
    expected_samples = 3  # 1 test simple + 2 steps du test multiple
    success = len(samples) == expected_samples
    
    print(f"\n✅ Aperçu correct : {'OUI' if success else 'NON'} ({len(samples)}/{expected_samples} échantillons)")
    
    # Vérifier qu'on a bien les différentes actions
    actions = [sample['extracted']['Action'] for sample in samples]
    unique_actions = set(actions)
    print(f"Actions uniques détectées : {len(unique_actions)}")
    for action in unique_actions:
        print(f"   - {action}")
    
    # Nettoyer
    os.remove('test_apercu_multiple.csv')
    
    return success and len(unique_actions) == 3

def test_csv_sans_guillemets():
    """
    Test que le CSV généré n'a pas de guillemets
    """
    print_header("📋 TEST CSV SANS GUILLEMETS")
    
    # Créer un fichier de test
    data = {
        'Summary': ['Test 1', 'Test 2'],
        'Description': ['Description 1', 'Description 2'],
        'Component/s': ['', ''],
        'Custom field (Manual Test Steps)': [
            '[{"id":1,"fields":{"Action":"Action 1","Data":"Data 1","Expected Result":"Result 1"}}]',
            '[{"id":2,"fields":{"Action":"Action 2","Data":"Data 2","Expected Result":"Result 2"}}]'
        ]
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_sans_guillemets.csv', index=False)
    
    print("📁 Fichier de test créé")
    
    # Extraction avec le nouveau processeur
    processor = JSONStepsProcessor()
    stats = processor.process_csv_file('test_sans_guillemets.csv', 'test_extracted_no_quotes.csv')
    
    print(f"📊 Extraction : {stats['processed']} tests → {stats['extracted']} étapes")
    
    # Vérifier le contenu du fichier généré
    if os.path.exists('test_extracted_no_quotes.csv'):
        with open('test_extracted_no_quotes.csv', 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        print(f"📁 Fichier généré : {len(lines)} lignes")
        
        # Compter les guillemets
        quote_count = content.count('"')
        print(f"Nombre de guillemets dans le fichier : {quote_count}")
        
        # Vérifier le séparateur
        semicolon_count = lines[0].count(';') if lines else 0
        print(f"Séparateurs point-virgule dans l'en-tête : {semicolon_count}")
        
        # Afficher quelques lignes pour vérification
        print(f"\nEn-tête : {lines[0]}")
        if len(lines) > 1:
            print(f"Première ligne : {lines[1]}")
        if len(lines) > 2:
            print(f"Deuxième ligne : {lines[2]}")
        
        # Le fichier ne devrait avoir aucun guillemet ou très peu
        success = quote_count == 0 and semicolon_count > 0
        
        print(f"\n✅ CSV sans guillemets : {'OUI' if quote_count == 0 else 'NON'}")
        print(f"✅ Séparateur point-virgule : {'OUI' if semicolon_count > 0 else 'NON'}")
        
        # Nettoyer
        os.remove('test_extracted_no_quotes.csv')
    else:
        print("❌ Fichier extrait non généré")
        success = False
    
    # Nettoyer
    os.remove('test_sans_guillemets.csv')
    
    return success

def test_enrichissement_apres_extraction():
    """
    Test que l'enrichissement fonctionne après extraction sans guillemets
    """
    print_header("🎯 TEST ENRICHISSEMENT APRÈS EXTRACTION")
    
    # Créer un fichier de test
    data = {
        'Summary': ['CFU Test', 'S6a Test'],
        'Description': ['Test CFU', 'Test S6a'],
        'Component/s': ['', ''],
        'Custom field (Manual Test Steps)': [
            '[{"id":1,"fields":{"Action":"Activate CFU","Data":"","Expected Result":"CFU activated"}}]',
            '[{"id":2,"fields":{"Action":"Send ULR","Data":"IMSI=123","Expected Result":"ULA received"}}]'
        ]
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_enrichissement_flow.csv', index=False)
    
    print("📁 Fichier de test créé")
    
    # Étape 1 : Extraction
    processor = JSONStepsProcessor()
    stats = processor.process_csv_file('test_enrichissement_flow.csv', 'test_flow_extracted.csv')
    
    print(f"🔧 Extraction : {stats['processed']} tests → {stats['extracted']} étapes")
    
    # Vérifier le fichier extrait
    if not os.path.exists('test_flow_extracted.csv'):
        print("❌ Fichier extrait non généré")
        return False
    
    # Vérifier le contenu du fichier extrait
    with open('test_flow_extracted.csv', 'r', encoding='utf-8') as f:
        extracted_content = f.read()
    
    quote_count = extracted_content.count('"')
    print(f"Guillemets dans le fichier extrait : {quote_count}")
    
    # Étape 2 : Enrichissement
    try:
        tool = TestEnrichmentToolEnhanced()
        tool.enrich_csv_enhanced('test_flow_extracted.csv', 'test_flow_enriched.csv', 'separate')
        
        print("🎯 Enrichissement réussi")
        
        # Vérifier le fichier enrichi
        if os.path.exists('test_flow_enriched.csv'):
            with open('test_flow_enriched.csv', 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"📁 Fichier enrichi : {len(lines)} lignes")
            
            # Vérifier les colonnes
            header = lines[0]
            essential_columns = ['Summary', 'Action', 'Data', 'Expected Result', 'Component/s', 'Test Set']
            labels_count = header.count('Labels')
            
            print(f"Colonnes Labels : {labels_count}")
            print(f"Colonnes essentielles présentes : {all(col in header for col in essential_columns)}")
            
            # Vérifier le contenu d'une ligne
            if len(lines) > 1:
                first_line_parts = lines[1].split(';')
                print(f"Première ligne : {len(first_line_parts)} colonnes")
                
                # Trouver les colonnes Action, Component, Test Set
                header_parts = header.split(';')
                for i, col in enumerate(header_parts):
                    if 'Action' in col and i < len(first_line_parts):
                        print(f"   Action : {first_line_parts[i]}")
                    elif 'Component' in col and i < len(first_line_parts):
                        print(f"   Component : {first_line_parts[i]}")
                    elif 'Test Set' in col and i < len(first_line_parts):
                        print(f"   Test Set : {first_line_parts[i]}")
            
            # Nettoyer
            os.remove('test_flow_enriched.csv')
            success = labels_count >= 8 and len(lines) >= 3
        else:
            print("❌ Fichier enrichi non généré")
            success = False
    
    except Exception as e:
        print(f"❌ Erreur lors de l'enrichissement : {e}")
        success = False
    
    # Nettoyer
    os.remove('test_enrichissement_flow.csv')
    if os.path.exists('test_flow_extracted.csv'):
        os.remove('test_flow_extracted.csv')
    
    return success

def main():
    """
    Test complet des corrections
    """
    print("🧪 TEST DES CORRECTIONS APERÇU + GUILLEMETS")
    
    # Test 1 : Aperçu multiples steps
    test1 = test_apercu_multiples_steps()
    
    # Test 2 : CSV sans guillemets
    test2 = test_csv_sans_guillemets()
    
    # Test 3 : Enrichissement après extraction
    test3 = test_enrichissement_apres_extraction()
    
    print_header("📊 RÉSULTATS DES CORRECTIONS")
    
    print(f"✅ Aperçu multiples steps : {'OK' if test1 else 'ÉCHEC'}")
    print(f"✅ CSV sans guillemets : {'OK' if test2 else 'ÉCHEC'}")
    print(f"✅ Enrichissement après extraction : {'OK' if test3 else 'ÉCHEC'}")
    
    if test1 and test2 and test3:
        print(f"\n🎉 TOUTES LES CORRECTIONS FONCTIONNENT !")
        print(f"✅ Aperçu montre tous les steps séparément")
        print(f"✅ CSV généré sans guillemets indésirables")
        print(f"✅ Enrichissement fonctionne avec les nouveaux CSV")
        print(f"✅ Workflow complet opérationnel")
        print(f"🎯 Les problèmes d'aperçu et de guillemets sont résolus")
    else:
        print(f"\n⚠️  CERTAINES CORRECTIONS NÉCESSITENT DES AJUSTEMENTS")

if __name__ == "__main__":
    main()
