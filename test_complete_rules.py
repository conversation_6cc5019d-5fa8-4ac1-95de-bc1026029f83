#!/usr/bin/env python3
"""
Test des règles complètes Core Network
"""

import pandas as pd
import os
import re
from core_network_rules_complete import CoreNetworkRulesComplete
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def print_header(title):
    print("\n" + "="*80)
    print(f"  {title}")
    print("="*80)

def test_complete_rules():
    """
    Test les nouvelles règles complètes
    """
    print_header("🧪 TEST DES RÈGLES COMPLÈTES CORE NETWORK")
    
    # Initialiser les règles
    rules = CoreNetworkRulesComplete()
    
    # Test des patterns de détection
    test_cases = [
        # HLR Tests
        ("CFU Test", "Test Call Forwarding Unconditional service", "Activate CFU", "CFU activated", ""),
        ("Call Barring Test", "Test Call Barring service", "Activate barring", "Barring activated", ""),
        ("CAMEL Test", "Test CAMEL intelligent network", "Execute CAMEL", "CAMEL executed", ""),
        ("HLR Authentication", "Test HLR authentication procedure", "Send SAI", "Triplet received", ""),
        
        # EPC-HSS Tests
        ("S6a ULR", "Test Update Location Request on S6a interface", "Send ULR", "ULA received", ""),
        ("HSS Authentication", "Test HSS authentication via S6a", "Send AIR", "AIA received", ""),
        ("EPC Attach", "Test EPC attach procedure", "Send attach", "Attach complete", ""),
        ("Diameter Routing", "Test Diameter routing performance", "Route message", "Message routed", ""),
        
        # IMS Tests
        ("VoLTE Call", "Test VoLTE call setup procedure", "Setup call", "Call established", ""),
        ("IMS Cx Test", "Test IMS Cx interface", "Send SAR", "SAA received", ""),
        ("SRVCC Test", "Test SRVCC handover", "Execute SRVCC", "Handover complete", ""),
        
        # Performance Tests
        ("Load Test", "Performance load testing", "Apply load", "Performance measured", ""),
        ("Stress Test", "Stress testing of system", "Apply stress", "System stable", ""),
        ("Capacity Test", "Test system capacity", "Increase capacity", "Capacity reached", ""),
        ("Scalability Test", "Test system scalability", "Scale up", "System scaled", ""),
        
        # Security Tests
        ("Vulnerability Scan", "Security vulnerability scanning", "Run scan", "No vulnerabilities", ""),
        ("CIS Compliance", "Test CIS compliance", "Check CIS", "Compliant", ""),
        ("RBAC Test", "Test role-based access control", "Check RBAC", "Access controlled", ""),
        ("Security Auth", "Test security authentication", "Authenticate", "Authenticated", ""),
        
        # OAM Tests
        ("SOAP Interface", "Test OAM SOAP interface", "Call SOAP", "Response received", ""),
        ("Provisioning", "Test provisioning interface", "Provision user", "User provisioned", ""),
        ("Configuration", "Test configuration management", "Configure", "Configured", ""),
        
        # Backup Tests
        ("Backup Operation", "Test backup operation", "Execute backup", "Backup created", ""),
        ("Restore Test", "Test restore operation", "Execute restore", "Data restored", ""),
        ("Recovery Test", "Test recovery procedure", "Execute recovery", "System recovered", ""),
    ]
    
    print(f"\n📊 Test de {len(test_cases)} cas de test...")
    
    # Tester avec l'outil amélioré
    tool = TestEnrichmentToolEnhanced()
    
    results = []
    for i, (summary, description, action, expected, path) in enumerate(test_cases):
        labels, component, test_set = tool.analyze_test_content_enhanced(
            summary, description, action, expected, path
        )
        
        results.append({
            'summary': summary,
            'labels': labels,
            'component': component,
            'test_set': test_set
        })
        
        print(f"✅ {i+1:2d}. {summary:20s} → {component:10s} → {test_set}")
    
    # Analyser les résultats
    print_header("📋 ANALYSE DES RÉSULTATS")
    
    # Compter par composant
    component_counts = {}
    for result in results:
        comp = result['component']
        component_counts[comp] = component_counts.get(comp, 0) + 1
    
    print(f"\n📊 RÉPARTITION PAR COMPOSANT :")
    for comp, count in sorted(component_counts.items()):
        print(f"   {comp:15s} : {count:2d} tests")
    
    # Compter les labels techniques
    all_labels = []
    for result in results:
        all_labels.extend(result['labels'])
    
    from collections import Counter
    label_counts = Counter(all_labels)
    
    print(f"\n🏷️  TOP 10 LABELS LES PLUS UTILISÉS :")
    for label, count in label_counts.most_common(10):
        print(f"   {label:20s} : {count:2d} fois")
    
    # Vérifier les références 3GPP
    gpp_refs = [label for label in all_labels if '3GPP' in label]
    print(f"\n📚 RÉFÉRENCES 3GPP AJOUTÉES :")
    for ref in set(gpp_refs):
        print(f"   {ref}")
    
    # Vérifier les domaines spécialisés
    specialized_domains = [
        'Performance', 'Security', 'Authentication', 'CallForwarding', 
        'VoLTE', 'Diameter', 'S6a', 'IMS', 'CAMEL'
    ]
    
    specialized_count = sum(1 for label in all_labels if label in specialized_domains)
    print(f"\n🎯 LABELS SPÉCIALISÉS DÉTECTÉS : {specialized_count}")
    
    return len(results), len(set(gpp_refs)), specialized_count

def test_specific_patterns():
    """
    Test des patterns spécifiques selon l'analyse fournie
    """
    print_header("🔍 TEST DES PATTERNS SPÉCIFIQUES")
    
    # Tests basés sur l'analyse fournie
    specific_tests = [
        # Test CORE-001 équivalent
        ("Authentication - HSS Functional", "Test for HSS auth procedures", "Authenticate", "Auth success", ""),
        # Test CORE-002 équivalent  
        ("Performance of Diameter routing", "Validate routing perf metrics", "Route", "Metrics validated", ""),
        # Tests supplémentaires pour couvrir tous les domaines
        ("Call Forwarding CFU", "Test CFU service activation", "Activate CFU", "CFU active", ""),
        ("S6a Update Location", "Test S6a ULR procedure", "Send ULR", "ULA received", ""),
        ("VoLTE SRVCC", "Test VoLTE SRVCC handover", "Execute SRVCC", "Handover OK", ""),
        ("Security Vulnerability", "Vulnerability assessment scan", "Scan system", "No vulns found", ""),
        ("OAM SOAP Config", "SOAP interface configuration", "Configure via SOAP", "Config applied", ""),
        ("Backup Scheduled", "Scheduled backup operation", "Execute backup", "Backup completed", ""),
    ]
    
    tool = TestEnrichmentToolEnhanced()
    
    print(f"\n📋 RÉSULTATS DÉTAILLÉS :")
    
    for i, (summary, description, action, expected, path) in enumerate(specific_tests):
        labels, component, test_set = tool.analyze_test_content_enhanced(
            summary, description, action, expected, path
        )
        
        print(f"\n{i+1}. {summary}")
        print(f"   Composant    : {component}")
        print(f"   Test Set     : {test_set}")
        print(f"   Labels       : {', '.join(labels[:5])}{'...' if len(labels) > 5 else ''}")
        print(f"   Total labels : {len(labels)}")
        
        # Vérifier les améliorations suggérées dans l'analyse
        if 'Authentication' in summary and 'authentication' not in [l.lower() for l in labels]:
            print(f"   ⚠️  Suggestion : Ajouter label 'Authentication'")
        
        if 'Diameter' in summary and 'diameter' not in [l.lower() for l in labels]:
            print(f"   ⚠️  Suggestion : Ajouter label 'Diameter'")
        
        # Vérifier les références 3GPP
        gpp_refs = [l for l in labels if '3GPP' in l]
        if gpp_refs:
            print(f"   ✅ Références 3GPP : {', '.join(gpp_refs)}")
        else:
            print(f"   ⚠️  Aucune référence 3GPP détectée")

def test_rules_completeness():
    """
    Test la complétude des règles
    """
    print_header("📊 TEST DE COMPLÉTUDE DES RÈGLES")
    
    rules = CoreNetworkRulesComplete()
    
    # Compter les éléments
    all_test_sets = rules.get_all_test_sets()
    
    print(f"📁 Test Sets définis        : {len(all_test_sets)}")
    print(f"📚 Références 3GPP          : {len(rules.gpp_references)}")
    print(f"🏷️  Labels techniques        : {sum(len(v) for v in rules.technical_labels.values())}")
    print(f"🎯 Domaines couverts        : {len(rules.test_sets_complete)}")
    
    # Lister les domaines
    print(f"\n🎯 DOMAINES COUVERTS :")
    for domain in rules.test_sets_complete.keys():
        test_sets_count = len(rules.test_sets_complete[domain])
        print(f"   {domain:20s} : {test_sets_count:2d} Test Sets")
    
    # Lister quelques Test Sets par domaine
    print(f"\n📁 EXEMPLES DE TEST SETS :")
    for domain, sets in list(rules.test_sets_complete.items())[:3]:
        print(f"\n   {domain.upper()} :")
        for test_set_name in list(sets.keys())[:3]:
            print(f"     • {test_set_name}")

if __name__ == "__main__":
    print("🧪 TEST COMPLET DES NOUVELLES RÈGLES CORE NETWORK")
    
    # Test 1 : Règles complètes
    total_tests, gpp_refs, specialized = test_complete_rules()
    
    # Test 2 : Patterns spécifiques
    test_specific_patterns()
    
    # Test 3 : Complétude
    test_rules_completeness()
    
    print_header("🎊 RÉSULTATS FINAUX")
    
    print(f"✅ Tests traités              : {total_tests}")
    print(f"✅ Références 3GPP ajoutées   : {gpp_refs}")
    print(f"✅ Labels spécialisés         : {specialized}")
    
    if total_tests > 20 and gpp_refs > 5 and specialized > 10:
        print(f"\n🎉 TOUTES LES RÈGLES FONCTIONNENT PARFAITEMENT !")
        print(f"🎯 Le système de règles complètes est opérationnel")
        print(f"📚 Les références 3GPP sont intégrées automatiquement")
        print(f"🏷️  Les labels techniques sont appliqués correctement")
    else:
        print(f"\n⚠️  CERTAINES RÈGLES NÉCESSITENT DES AJUSTEMENTS")
        print(f"💡 Vérifiez les patterns et les configurations")
