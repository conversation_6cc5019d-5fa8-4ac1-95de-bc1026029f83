Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project description,Project url,Priority,Resolution,Assignee,Reporter,Creator,Created,Updated,Last Viewed,Resolved,Affects Version/s,Fix Version/s,Component/s,Due Date,Votes,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Description,Environment,Watchers,Log Work,Original Estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Attachment,Custom field (Automation status),Custom field (Branch),Custom field (Business Gain),Custom field (CDR/MC),Custom field (Category List),Custom field (Change completion date),Custom field (Change start date),Custom field (Change type),Custom field (Cost),Custom field (Cucumber Scenario),Custom field (Cucumber Test Type),Customer Request Type,Custom field (Dataset values),Custom field (Dataset values),Custom field (Date MEP),Custom field (Date UAT),Demande SLA 16H,Demande SLA 16H simplified,Custom field (Domain List),<PERSON><PERSON><PERSON> de traitement,Du<PERSON>e de traitement simplified,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> simplified,<PERSON><PERSON><PERSON> de prise en charge,<PERSON><PERSON>lai de prise en charge simplified,Custom field (Effects),Custom field (Entity List),Custom field (Environment),Custom field (Epic Link),Custom field (External Contributor/s),Custom field (External issue ID),Fermeture apres x jours,Fermeture apres x jours simplified,Custom field (First Backlog Transition),Custom field (First key),Custom field (First-Name),Custom field (Flagged),Custom field (Generic Test Definition),Custom field (Groups),Custom field (Groups),Custom field (Impact),Custom field (Impacted Entity),Custom field (Jira Project Type),Custom field (Last-Name),Custom field (Linked major incidents),Custom field (List Entity),Custom field (MVP Macro Budget (K€)),Custom field (Mail),Custom field (Manual Test Steps),Custom field (Operational categorization),Custom field (Organizations),Custom field (Original story points),Custom field (Overcoast),Custom field (Parent Key),Custom field (Parent Link),Custom field (Penalties),Custom field (Platform),Custom field (Pre-Conditions association with a Test),Prise en compte,Prise en compte simplified,Custom field (Product categorization),Custom field (QC),Custom field (Qualification Date),Custom field (Rank),Custom field (Ref. Project CARTO),Custom field (Reference Code),Custom field (Request participants),Resolution Time SLA,Resolution Time SLA simplified,Response Time SLA,Response Time SLA simplified,Custom field (Result),Custom field (Review date),Custom field (Revision),Satisfaction score (out of 5),Custom field (Scoring),Sprint,Custom field (Steps Count),Custom field (Structure Index Monitor),Custom field (Support),Custom field (Target end),Custom field (Target start),Custom field (Team),Custom field (Team List),"Temps d&#39;attribution","Temps d&#39;attribution simplified",Temps première réponse,Temps première réponse simplified,Custom field (Test Execution Status),Custom field (Test Plans associated with a Test),Custom field (Test Repository Path),Custom field (Test Sets association with a Test),Custom field (Test Sets association with a Test),Custom field (Test Type),Custom field (TestRunStatus),Time to close after resolution,Time to close after resolution simplified,Time to first response,Time to first response simplified,Time to resolution,Time to resolution simplified,Custom field (TimeRecup (deprecated)),Custom field (User Activity),Custom field (Workaround (deprecated)),Comment
test File Storage DB,SANTSDM-1896,8939088,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,12/Jul/25 3:45 PM,17/Jul/25 9:59 AM,,,,ZTE-SPECIFIC,,0,Functional,Manual,Regression,System,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1896,,,,,,,,software,,,,,,[],,,,,,,,,,,,,,,"0|j1qd1k:",,,,,,,,,,,,,,0.0,,,,,,,,,,,"{""issueId"":8939088,""testStatuses"":[]}",,,,,Manual,TODO,,,,,,,,,,
SOAP provisionning,SANTSDM-1895,8939087,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:34 PM,15/Jul/25 12:34 PM,,,,,,0,Functional,Manual,OAM,Regression,SOAP,System,,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1895,,,,,,,,software,,,,,,"[{""id"":3844657,""index"":1,""fields"":{""Action"":""\""- Verify that user provisionning can be executed through SOAP command\n\"""",""Data"":"""",""Expected Result"":""\""- User provisionning operate with success with SOAP command\n\""""},""attachments"":[],""testVersionId"":1102789}]",,,,,,,,,,,,,,,"0|j1qd1c:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939087,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/COMMON Non Regression/Subscriber management,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
MML provisionning,SANTSDM-1894,8939086,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:34 PM,17/Jul/25 10:39 AM,,,,,,0,Functional,Manual,MML,OAM,Regression,System,,,"SUMMARY : None
PRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1894,,,,,,,,software,,,,,,"[{""id"":3844656,""index"":1,""fields"":{""Action"":""\""- Verify that user provisionning can be executed through MMLcommand\n\"""",""Data"":"""",""Expected Result"":""\""- User provisionning operate with success with MML command\n\""""},""attachments"":[],""testVersionId"":1102788}]",,,,,,,,,,,,,,,"0|j1qd14:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939086,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/COMMON Non Regression/Subscriber management,SANTSDM-1908,,Manual,TODO,,,,,,,,,,
CFU,SANTSDM-1893,8939085,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,11/Jul/25 6:48 PM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Forward service

CFU

The purpose of the test is to verify that it is possible to provision a subscriber with CFU.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 1.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFU provisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1893,,,,,,,,software,,,,,,"[{""id"":3844654,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFUservice in the HLR.\n\tRegister and Activate CFU service\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFU provision data.\n\n\""""},""attachments"":[],""testVersionId"":1102787},{""id"":3844655,""index"":2,""fields"":{""Action"":""\""Messages fows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102787}]",,,,,,,,,,,,,,,"0|j1qd0w:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939085,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
CFB,SANTSDM-1892,8939084,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,11/Jul/25 6:44 PM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Forward service

CFB

The purpose of the test is to verify that it is possible to provision a subscriber with CFB.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 2.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFB provisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1892,,,,,,,,software,,,,,,"[{""id"":3844652,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFBservice in the HLR.\n\tRegister and Activate CFBservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFBprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102786},{""id"":3844653,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102786}]",,,,,,,,,,,,,,,"0|j1qd0o:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939084,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
CFNRy,SANTSDM-1891,8939083,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,17/Jul/25 10:39 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Forward service

CFNRy

The purpose of the test is to verify that it is possible to provision a subscriber with CFNRy.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 3.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFNRy provisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1891,,,,,,,,software,,,,,,"[{""id"":3844650,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFNRyservice in the HLR.\n\tRegister and Activate CFNRyservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFRyprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102785},{""id"":3844651,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102785}]",,,,,,,,,,,,,,,"0|j1qd0g:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939083,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
CFNRc,SANTSDM-1890,8939082,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,17/Jul/25 10:39 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Forward service

CFNRc

The purpose of the test is to verify that it is possible to provision a subscriber with CFNRc.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 4.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFNRc provisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1890,,,,,,,,software,,,,,,"[{""id"":3844648,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFNRcservice in the HLR.\n\tRegister and Activate CFNRcservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFNRcprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102784},{""id"":3844649,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102784}]",,,,,,,,,,,,,,,"0|j1qd08:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939082,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
CFD,SANTSDM-1889,8939081,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,11/Jul/25 6:45 PM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Forward service

CFD

The purpose of the test is to verify that it is possible to provision a subscriber with CFD.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.082 sections 2.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have CFDprovisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1889,,,,,,,,software,,,,,,"[{""id"":3844646,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add CFDservice in the HLR.\n\tRegister and Activate CFDservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing CFDprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102783},{""id"":3844647,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR \n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\t HLR\n\t\t\t\n\t\t\t\n\t\t\t Insert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102783}]",,,,,,,,,,,,,,,"0|j1qd00:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939081,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Forwarding Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
SS Call Barred-BAOC,SANTSDM-1888,8939080,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,17/Jul/25 10:39 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Barring service

SS Call Barring-BAOC

The purpose of the test is to verify that it is possible to provision a subscriber with BAOC.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.088 section 1.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have BAOC provisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1888,,,,,,,,software,,,,,,"[{""id"":3844644,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add BAOCservice in the HLR.\n\tRegister and Activate BAOCservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing BAOCprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102782},{""id"":3844645,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102782}]",,,,,,,,,,,,,,,"0|j1qczs:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939080,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Barring Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
SS Call Barring-BOIC,SANTSDM-1887,8939079,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,17/Jul/25 10:39 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Barring service

SS Call Barring-BOIC

The purpose of the test is to verify that it is possible to provision a subscriber with BOIC.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.088 section 1.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have BOIC provisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1887,,,,,,,,software,,,,,,"[{""id"":3844642,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add BOICservice in the HLR.\n\tRegister and Activate BOICservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing BOICprovision data.\n\n\""""},""attachments"":[],""testVersionId"":1102781},{""id"":3844643,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102781}]",,,,,,,,,,,,,,,"0|j1qczk:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939079,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Barring Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
SS Call Barring- BOIC-exHC,SANTSDM-1886,8939078,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,15/Jul/25 4:41 PM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call barring service

SS Call Barring- BOIC-exHC

The purpose of the test is to verify that it is possible to provision a subscriber with BOIC -exHC.

Reference: 3GPP TS 29.002 section 20.3  3GPP TS 22.088 section 1.3.1

PRECONDITIONS : 
	Configurations: CS Networks
	MS does not have BOIC -exHC provisioned.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1886,,,,,,,,software,,,,,,"[{""id"":3844640,""index"":1,""fields"":{""Action"":""\""\n\tModify the subscriber subscription information to add BOIC-EXCH service in the HLR.\n\tRegister and Activate BOIC-EXCHservice\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the provision is completed successfully.\n\tVerify that the ISD message is sent by the HLR containing BOIC-EXCH provision data.\n\n\""""},""attachments"":[],""testVersionId"":1102780},{""id"":3844641,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tInsert subscriber data acknowledge\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102780}]",,,,,,,,,,,,,,,"0|j1qczc:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939078,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Barring Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
SS Call Barring-BAIC,SANTSDM-1885,8939077,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,17/Jul/25 10:39 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Barring Service

SSCall Berried-BAIC

The purpose of the test is to verify that it is possible to provision a subscriber with BAIC.

Reference: 3GPP TS 29.002 sections 11.3  22.4, and 3GPP TS 23.088 section 7.1.2

PRECONDITIONS : 
	
	Configurations: A
	
	
	MS does not have BAIC provisioned.
	
	
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1885,,,,,,,,software,,,,,,"[{""id"":3844638,""index"":1,""fields"":{""Action"":""\""\n\t\n\tProvision the MS with BAIC.\n\t\n\t\n\tActivate BAIC.\n\t\n\t\n\tAttempt a mobile terminate call.\n\t\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\tVerify that the provision is completed successfully.\n\t\n\t\n\tVerify that the call is rejected ( Test is done via OTIP Map SRI Message)\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102779},{""id"":3844639,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\t\n\t\t\t\t\n\t\t\t\tGMSCHLRSend Routing Information Request\n\t\t\t\t\n\t\t\t\t\n\t\t\t\tGMSCHLRMAP ERROR\n\t\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102779}]",,,,,,,,,,,,,,,"0|j1qcz4:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939077,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Barring Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
SS Call Barring- BIC-ROAM,SANTSDM-1884,8939076,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,17/Jul/25 10:39 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Barring Service

SS Call Berried- BIC-ROAM

The purpose of the test is to verify that it is possible to provision a subscriber with BIC-ROAM.

Reference: 3GPP TS 29.002 sections 11.3  22.4, and 3GPP TS 23.088 section 7.1.2



PRECONDITIONS : 
	
	Configurations: A
	
	
	MS does not have BIC-ROAM provisioned.
	
	
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1884,,,,,,,,software,,,,,,"[{""id"":3844636,""index"":1,""fields"":{""Action"":""\""\n\t\n\tProvision the MS with BIC-ROAM.\n\t\n\t\n\tActivate BIC-ROAM.\n\t\n\t\n\tAttempt a mobile terminate call.\n\t\n\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\tVerify that the provision is completed successfully.\n\t\n\t\n\tVerify that the call is rejected when MS roaming outside the Home PLMN Country ( via OTIP Map Message SRI)\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102778},{""id"":3844637,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\t\n\t\t\t\t\n\t\t\t\tGMSC  HLRSend Routing Information Request\n\t\t\t\t\n\t\t\t\t\n\t\t\t\tGMSC  HLRMAP ERROR\n\t\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102778}]",,,,,,,,,,,,,,,"0|j1qcyw:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939076,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Barring Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
Barring Controlled by subscriber,SANTSDM-1883,8939075,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,17/Jul/25 10:39 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Barring service

Barring Controlled by subscriber

The purpose of this test is to perform a successful activation of the call barring protected supplementary service.

Reference: 3GPP TS 29.002 sections 11.8, 22.4  3GPP TS 23.011 section 3.4

PRECONDITIONS : 
	Configurations: CS Networks
	The subscription option is provisioned with ""Control of a Supplementary Service by the subscriber using a password"" and a password is provided at provision time.
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1883,,,,,,,,software,,,,,,"[{""id"":3844634,""index"":1,""fields"":{""Action"":""\""Activate ss BAOC in UE with correct password\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the activation is successful.\n\n\""""},""attachments"":[],""testVersionId"":1102777},{""id"":3844635,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tActivate SS\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tGet Password\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tGet Password Ack\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tActivate SS Ack\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102777}]",,,,,,,,,,,,,,,"0|j1qcyo:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939075,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Barring Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,
Barring Controlled by service provider,SANTSDM-1882,8939074,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 3:08 PM,17/Jul/25 10:39 AM,,,,HLR,,0,4G,AttachDetach,Diameter,Functional,Manual,Regression,S6a,System,"SUMMARY : Call Barring service

Barring Controlled by service provider

The purpose of this test is to verify that it is not possible to active ss BAOC service when the control of the Supplementary Service is reserved to the service provider.

PRECONDITIONS : 
	Configurations: CS Networks
	The subscription option is provisioned with ""Control of a Supplementary Service by the service provider""
	MS is attached.",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1882,,,,,,,,software,,,,,,"[{""id"":3844632,""index"":1,""fields"":{""Action"":""\""Activate call barring in UE with correct password\n\"""",""Data"":"""",""Expected Result"":""\""\n\tVerify that the monitored message sequence is correct.\n\tVerify that the activation is unsuccessful.\n\n\""""},""attachments"":[],""testVersionId"":1102776},{""id"":3844633,""index"":2,""fields"":{""Action"":""\""Messages flows :\n\"""",""Data"":"""",""Expected Result"":""\""\n\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tActivate SS\n\t\t\t\n\t\t\n\t\t\n\t\t\t\n\t\t\tVLR\n\t\t\t\n\t\t\t\n\t\t\t--\n\t\t\t\n\t\t\t\n\t\t\tHLR\n\t\t\t\n\t\t\t\n\t\t\tMap Error\n\t\t\t\n\t\t\n\t\n\n\""""},""attachments"":[],""testVersionId"":1102776}]",,,,,,,,,,,,,,,"0|j1qcyg:",,,,,,,,,,,,,,2.0,,,,,,,,,,,"{""issueId"":8939074,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Call Barring Service,SANTSDM-1899,,Manual,TODO,,,,,,,,,,