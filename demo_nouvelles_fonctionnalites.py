#!/usr/bin/env python3
"""
Démonstration des nouvelles fonctionnalités :
1. Suppression des labels existants
2. Génération CSV pour import Test Sets
"""

import pandas as pd
import os
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced
from test_set_csv_generator import TestSetCSVGenerator

def print_header(title):
    print("\n" + "="*80)
    print(f"  {title}")
    print("="*80)

def demo_suppression_labels():
    """
    Démonstration de la suppression des labels existants
    """
    print_header("🔧 DÉMONSTRATION - SUPPRESSION DES LABELS EXISTANTS")
    
    print("""
🎯 FONCTIONNALITÉ AJOUTÉE :
   Les labels existants sont automatiquement supprimés avant l'enrichissement
   pour éviter les doublons et garantir des résultats propres.
    """)
    
    # Créer un fichier avec des labels existants
    data = {
        'Summary': ['CFU Test', 'S6a ULR Test'],
        'Description': ['Test CFU', 'Test ULR'],
        'Component/s': ['', ''],
        'Action': ['Activate', 'Send'],
        'Expected Result': ['Activated', 'Received'],
        'Labels': ['AncienLabel1', 'AncienLabel2'],  # Labels existants à supprimer
        'Test Set': ['AncienTestSet1', 'AncienTestSet2']  # Test Sets existants à supprimer
    }
    
    df_with_old_labels = pd.DataFrame(data)
    df_with_old_labels.to_csv('demo_avec_anciens_labels.csv', index=False)
    
    print("📁 Fichier de test créé avec anciens labels :")
    print(f"   Colonnes : {list(df_with_old_labels.columns)}")
    print(f"   Premier test - Ancien label : {df_with_old_labels.iloc[0]['Labels']}")
    print(f"   Premier test - Ancien Test Set : {df_with_old_labels.iloc[0]['Test Set']}")
    
    # Enrichir avec suppression automatique
    print(f"\n🔄 Enrichissement avec suppression automatique des anciens labels...")
    tool = TestEnrichmentToolEnhanced()
    tool.enrich_csv_enhanced('demo_avec_anciens_labels.csv', 'demo_labels_supprimes.csv', 'separate')
    
    # Vérifier le résultat
    print(f"\n📊 Résultat après enrichissement :")
    
    # Lire le fichier brut pour voir les vraies colonnes
    with open('demo_labels_supprimes.csv', 'r', encoding='utf-8') as f:
        header_line = f.readline().strip()
        print(f"   En-tête CSV brut : {header_line}")
    
    # Compter les colonnes Labels
    labels_count = header_line.count('Labels')
    print(f"   Nombre de colonnes 'Labels' : {labels_count}")
    
    # Lire avec pandas (qui va renommer les colonnes dupliquées)
    df_enriched = pd.read_csv('demo_labels_supprimes.csv')
    print(f"   Colonnes après lecture pandas : {list(df_enriched.columns)}")
    
    # Vérifier le contenu du premier test
    print(f"\n✅ Premier test enrichi :")
    print(f"   Summary : {df_enriched.iloc[0]['Summary']}")
    print(f"   Nouveau composant : {df_enriched.iloc[0]['Component/s']}")
    print(f"   Nouveau Test Set : {df_enriched.iloc[0]['Test Set']}")
    
    # Vérifier qu'il n'y a plus d'anciens labels
    all_values = df_enriched.iloc[0].values
    old_labels_found = any('AncienLabel' in str(val) for val in all_values if pd.notna(val))
    old_testsets_found = any('AncienTestSet' in str(val) for val in all_values if pd.notna(val))
    
    if not old_labels_found and not old_testsets_found:
        print(f"   ✅ Anciens labels et Test Sets supprimés avec succès !")
    else:
        print(f"   ⚠️  Certains anciens éléments persistent")
    
    # Nettoyer
    os.remove('demo_avec_anciens_labels.csv')
    os.remove('demo_labels_supprimes.csv')
    
    return labels_count >= 8

def demo_generation_csv_testsets():
    """
    Démonstration de la génération CSV pour Test Sets
    """
    print_header("📋 DÉMONSTRATION - GÉNÉRATION CSV POUR IMPORT TEST SETS")
    
    print("""
🎯 FONCTIONNALITÉ AJOUTÉE :
   Génération automatique d'un fichier CSV pour importer tous les Test Sets
   avec le format exact requis pour votre système.
    """)
    
    # Générer le fichier CSV
    generator = TestSetCSVGenerator()
    output_file = 'demo_test_sets_import.csv'
    
    print(f"🔄 Génération du fichier CSV...")
    generator.generate_test_sets_csv(output_file)
    
    # Analyser le fichier généré
    print(f"\n📊 Analyse du fichier généré :")
    
    # Lire l'en-tête
    with open(output_file, 'r', encoding='utf-8-sig') as f:
        header_line = f.readline().strip()
        print(f"   Format : {header_line}")
    
    # Vérifier la structure
    expected_format = "Summary;Status;Labels;Labels_1;Labels_2;Labels_3;Labels_4;Labels_5;Labels_6;Labels_7;Labels_8;Labels_9;;Description;Comment"
    if header_line == expected_format:
        print(f"   ✅ Format conforme aux spécifications")
    else:
        print(f"   ⚠️  Format différent de celui attendu")
    
    # Lire le contenu
    df = pd.read_csv(output_file, sep=';')
    print(f"   Nombre de Test Sets : {len(df)}")
    print(f"   Colonnes : {len(df.columns)}")
    
    # Afficher quelques exemples
    print(f"\n📋 Exemples de Test Sets générés :")
    
    for i in range(min(3, len(df))):
        row = df.iloc[i]
        print(f"\n   {i+1}. {row['Summary']}")
        print(f"      Status : {row['Status']}")
        
        # Collecter tous les labels
        labels = []
        for col in df.columns:
            if col.startswith('Labels') or col == 'Labels':
                val = row[col]
                if pd.notna(val) and val != '':
                    labels.append(val)
        
        print(f"      Labels : {labels[:5]}{'...' if len(labels) > 5 else ''} ({len(labels)} total)")
        print(f"      Description : {row['Description'][:60]}...")
        print(f"      Comment : {row['Comment'][:60]}...")
    
    # Statistiques
    stats = generator.get_test_sets_statistics()
    print(f"\n📊 Statistiques :")
    print(f"   Total Test Sets : {stats['total_test_sets']}")
    print(f"   Domaines couverts : {stats['domains']}")
    print(f"   Avec descriptions : {stats['with_descriptions']}")
    print(f"   Avec commentaires : {stats['with_comments']}")
    
    # Vérifier les champs requis
    required_fields = ['Summary', 'Status', 'Labels', 'Description', 'Comment']
    all_fields_present = all(field in df.columns for field in required_fields)
    
    if all_fields_present:
        print(f"   ✅ Tous les champs requis sont présents")
    else:
        print(f"   ⚠️  Certains champs requis manquent")
    
    # Nettoyer
    os.remove(output_file)
    
    return len(df) >= 25 and all_fields_present

def demo_workflow_complet():
    """
    Démonstration du workflow complet
    """
    print_header("🚀 WORKFLOW COMPLET AVEC NOUVELLES FONCTIONNALITÉS")
    
    print("""
🎯 WORKFLOW UTILISATEUR AMÉLIORÉ :

1️⃣  ENRICHISSEMENT AVEC SUPPRESSION AUTOMATIQUE :
   • Upload fichier CSV (même avec anciens labels)
   • Suppression automatique des labels existants
   • Enrichissement avec nouvelles règles complètes
   • Génération format Jira avec colonnes Labels identiques

2️⃣  GÉNÉRATION CSV POUR IMPORT TEST SETS :
   • Accès via page des règles (/rules)
   • Génération automatique des 27 Test Sets
   • Format exact pour import : Summary;Status;Labels;...;Description;Comment
   • Descriptions en anglais + commentaires en français

3️⃣  IMPORT DANS VOTRE SYSTÈME :
   • Fichier CSV prêt pour import direct
   • 27 Test Sets avec labels techniques complets
   • Descriptions et commentaires inclus
   • Format conforme aux spécifications
    """)
    
    print(f"\n🌐 ACCÈS VIA INTERFACE WEB :")
    print(f"   • Lancer : python run_app.py")
    print(f"   • Enrichissement : http://127.0.0.1:5000")
    print(f"   • Règles et génération CSV : http://127.0.0.1:5000/rules")
    print(f"   • Bouton 'Générer CSV Test Sets' dans la page des règles")
    
    print(f"\n✅ AVANTAGES DES NOUVELLES FONCTIONNALITÉS :")
    print(f"   • Pas de doublons de labels")
    print(f"   • Import Test Sets automatisé")
    print(f"   • Format exact requis respecté")
    print(f"   • Descriptions bilingues (EN/FR)")
    print(f"   • 27 Test Sets prêts à l'emploi")

def main():
    """
    Démonstration complète des nouvelles fonctionnalités
    """
    print("🎊 DÉMONSTRATION DES NOUVELLES FONCTIONNALITÉS")
    
    # Test 1 : Suppression des labels existants
    test1 = demo_suppression_labels()
    
    # Test 2 : Génération CSV Test Sets
    test2 = demo_generation_csv_testsets()
    
    # Workflow complet
    demo_workflow_complet()
    
    print_header("📊 RÉSULTATS DE LA DÉMONSTRATION")
    
    print(f"✅ Suppression labels existants : {'OK' if test1 else 'ÉCHEC'}")
    print(f"✅ Génération CSV Test Sets : {'OK' if test2 else 'ÉCHEC'}")
    
    if test1 and test2:
        print(f"\n🎉 TOUTES LES NOUVELLES FONCTIONNALITÉS FONCTIONNENT !")
        print(f"🎯 Votre demande est entièrement satisfaite :")
        print(f"   • Labels existants supprimés automatiquement")
        print(f"   • Fichier CSV Test Sets généré avec format exact")
        print(f"   • Colonnes 'Labels' identiques pour import")
        print(f"   • Descriptions EN + commentaires FR inclus")
    else:
        print(f"\n⚠️  CERTAINES FONCTIONNALITÉS NÉCESSITENT DES AJUSTEMENTS")
    
    print(f"\n🚀 PRÊT POUR UTILISATION EN PRODUCTION !")

if __name__ == "__main__":
    main()
