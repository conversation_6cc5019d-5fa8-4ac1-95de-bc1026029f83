#!/usr/bin/env python3
"""
Script pour afficher un échantillon des résultats d'enrichissement
"""

import pandas as pd
import sys

def show_sample_results(csv_file, num_samples=20):
    """
    Affiche un échantillon des résultats d'enrichissement
    """
    try:
        df = pd.read_csv(csv_file)
        
        print("=== ÉCHANTILLON DES RÉSULTATS D'ENRICHISSEMENT ===\n")
        
        # Sélectionner quelques exemples représentatifs
        samples = []
        
        # Exemples de différents types
        security_tests = df[df['Labels'].str.contains('Security', na=False)].head(2)
        backup_tests = df[df['Labels'].str.contains('BackupRestore', na=False)].head(2)
        hlr_tests = df[df['Component/s'] == 'HLR'].head(3)
        epc_tests = df[df['Component/s'] == 'EPC-HSS'].head(3)
        ims_tests = df[df['Component/s'] == 'IMS-HSS'].head(3)
        oam_tests = df[df['Component/s'] == 'OAM'].head(2)
        
        all_samples = pd.concat([security_tests, backup_tests, hlr_tests, epc_tests, ims_tests, oam_tests])
        
        for index, row in all_samples.iterrows():
            print(f"--- Test {index + 1} ---")
            print(f"Summary: {row['Summary']}")
            print(f"Component: {row['Component/s']}")
            print(f"Labels: {row['Labels']}")
            print(f"Test Set: {row['Test Set']}")
            print(f"Description: {str(row['Description'])[:100]}...")
            print()
        
        # Statistiques par composant
        print("\n=== STATISTIQUES PAR COMPOSANT ===")
        component_stats = df['Component/s'].value_counts()
        for component, count in component_stats.items():
            print(f"{component}: {count} tests")
        
        # Statistiques par type de test
        print("\n=== STATISTIQUES PAR TYPE ===")
        functional_count = df['Labels'].str.contains('Functional', na=False).sum()
        non_functional_count = df['Labels'].str.contains('NonFunctional', na=False).sum()
        print(f"Functional: {functional_count}")
        print(f"NonFunctional: {non_functional_count}")
        
        # Exemples de Test Sets
        print("\n=== EXEMPLES DE TEST SETS ===")
        test_sets = df['Test Set'].value_counts().head(10)
        for test_set, count in test_sets.items():
            print(f"{test_set}: {count} tests")
            
    except Exception as e:
        print(f"Erreur: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python sample_results.py <enriched_file.csv>")
        sys.exit(1)
    
    show_sample_results(sys.argv[1])
