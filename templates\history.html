{% extends "base.html" %}

{% block title %}Historique - Enrichissement Tests Jira Xray{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-history"></i> Historique des Enrichissements
                </h3>
            </div>
            <div class="card-body">
                {% if files %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>{{ files|length }}</strong> fichier(s) traité(s) trouvé(s).
                        Les fichiers sont triés par date de traitement (plus récent en premier).
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th><i class="fas fa-file-csv"></i> Fichier</th>
                                    <th><i class="fas fa-chart-bar"></i> Tests</th>
                                    <th><i class="fas fa-sitemap"></i> Composants</th>
                                    <th><i class="fas fa-weight"></i> Taille</th>
                                    <th><i class="fas fa-clock"></i> Traité le</th>
                                    <th><i class="fas fa-tools"></i> Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for file in files %}
                                <tr>
                                    <td>
                                        <strong>{{ file.filename }}</strong>
                                        {% if file.filename.startswith('enriched_') %}
                                            <span class="badge bg-success ms-2">Enrichi</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ file.test_count }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ file.components }}</span>
                                    </td>
                                    <td>
                                        {% if file.size < 1024 %}
                                            {{ file.size }} B
                                        {% elif file.size < 1024*1024 %}
                                            {{ "%.1f"|format(file.size/1024) }} KB
                                        {% else %}
                                            {{ "%.1f"|format(file.size/1024/1024) }} MB
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ moment(file.modified).format('DD/MM/YYYY HH:mm') if moment else file.modified|int|timestamp }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('download_file', filename=file.filename) }}"
                                               class="btn btn-outline-primary" title="Télécharger">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            {% if file.filename.startswith('enriched_') %}
                                                {% set file_id = file.filename.replace('enriched_', '').replace('.csv', '') %}
                                                <a href="{{ url_for('compare_results', file_id=file_id) }}"
                                                   class="btn btn-outline-info" title="Comparer">
                                                    <i class="fas fa-exchange-alt"></i>
                                                </a>
                                                <a href="{{ url_for('preview_file', file_id=file_id) }}"
                                                   class="btn btn-outline-secondary" title="Aperçu">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ url_for('analyze_test_sets', file_id=file_id) }}"
                                                   class="btn btn-outline-success" title="Analyser Test Sets">
                                                    <i class="fas fa-chart-line"></i>
                                                </a>
                                            {% endif %}
                                            <button onclick="deleteFile('{{ file.filename }}')"
                                                    class="btn btn-outline-danger" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Statistiques rapides -->
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-primary">{{ files|length }}</h4>
                                    <p class="card-text">Fichiers traités</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-success">{{ files|sum(attribute='test_count') }}</h4>
                                    <p class="card-text">Tests enrichis</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-info">{{ files|sum(attribute='components') }}</h4>
                                    <p class="card-text">Composants identifiés</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-warning">
                                        {% set total_size = files|sum(attribute='size') %}
                                        {% if total_size < 1024*1024 %}
                                            {{ "%.1f"|format(total_size/1024) }} KB
                                        {% else %}
                                            {{ "%.1f"|format(total_size/1024/1024) }} MB
                                        {% endif %}
                                    </h4>
                                    <p class="card-text">Taille totale</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions de gestion -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-tools"></i> Gestion des Fichiers
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <button onclick="cleanupOldFiles()" class="btn btn-warning w-100">
                                        <i class="fas fa-broom"></i> Nettoyer les anciens fichiers (>7 jours)
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button onclick="location.reload()" class="btn btn-info w-100">
                                        <i class="fas fa-sync-alt"></i> Actualiser la liste
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Graphique des traitements récents -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line"></i> Activité Récente
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="activityChart" width="400" height="100"></canvas>
                        </div>
                    </div>

                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Aucun fichier traité</h4>
                        <p class="text-muted">
                            Vous n'avez encore traité aucun fichier. 
                            Commencez par télécharger un fichier CSV sur la page d'accueil.
                        </p>
                        <a href="{{ url_for('index') }}" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Télécharger un fichier
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{% if files %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Graphique d'activité
const ctx = document.getElementById('activityChart').getContext('2d');
const files = {{ files|tojson }};

// Préparer les données pour le graphique
const labels = files.slice(0, 10).reverse().map(f => {
    const date = new Date(f.modified * 1000);
    return date.toLocaleDateString('fr-FR', { month: 'short', day: 'numeric' });
});

const data = files.slice(0, 10).reverse().map(f => f.test_count);

new Chart(ctx, {
    type: 'line',
    data: {
        labels: labels,
        datasets: [{
            label: 'Tests enrichis',
            data: data,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Fonction pour supprimer un fichier
async function deleteFile(filename) {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer le fichier "${filename}" ?`)) {
        return;
    }

    try {
        const response = await fetch(`/delete/${filename}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            // Afficher un message de succès
            showAlert('success', result.message);
            // Recharger la page après un délai
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('danger', result.message);
        }
    } catch (error) {
        showAlert('danger', 'Erreur lors de la suppression du fichier');
    }
}

// Fonction pour nettoyer les anciens fichiers
async function cleanupOldFiles() {
    if (!confirm('Supprimer tous les fichiers de plus de 7 jours ?')) {
        return;
    }

    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Nettoyage...';
    button.disabled = true;

    try {
        const response = await fetch('/cleanup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            showAlert('success', result.message);
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('danger', result.message);
        }
    } catch (error) {
        showAlert('danger', 'Erreur lors du nettoyage');
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Fonction pour afficher des alertes
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insérer l'alerte en haut de la page
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);
}
</script>
{% endif %}
{% endblock %}
