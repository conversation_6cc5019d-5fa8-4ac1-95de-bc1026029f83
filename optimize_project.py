#!/usr/bin/env python3
"""
Script d'optimisation du projet - Suppression des fichiers inutiles
"""

import os
import shutil
import glob
from pathlib import Path

def print_header(title):
    print("\n" + "="*70)
    print(f"  {title}")
    print("="*70)

def optimize_project():
    """
    Optimise le projet en supprimant les fichiers inutiles
    """
    print_header("🧹 OPTIMISATION DU PROJET")
    
    # Fichiers à supprimer (inutiles)
    files_to_remove = [
        # Anciens fichiers de test
        "test_ameliorations_finales.py",
        "test_complete_rules.py", 
        "test_corrections_apercu_guillemets.py",
        "test_corrections_finales.py",
        "test_corrections_format.py",
        "test_corrections_problemes.py",
        "test_detection_format_automatique.py",
        "test_enhanced_version.py",
        "test_enrichissement_nombreuses_colonnes.py",
        "test_enrichment_tool.py",  # Ancienne version
        "test_enrichment_tool_v2.py",  # Ancienne version
        "test_final_validation.py",
        "test_format_fix.py",
        "test_format_labels_identiques.py",
        "test_json_workflow_complet.py",
        "test_labels_removal.py",
        "test_labels_uniques.py",
        "test_separate_labels.py",
        "test_simple_v2.py",
        "test_virgules_uniquement.py",
        "test_votre_fichier_exact.py",
        "test_web_separate_labels.py",
        "test_workflow_complet.py",
        "test_app_web_format.py",
        
        # Anciens demos
        "demo_advanced_features.py",
        "demo_avant_apres.py",
        "demo_complete.py",
        "demo_examples.py",
        "demo_final_separate_labels.py",
        "demo_final_simplifie.py",
        "demo_integration_complete.py",
        "demo_nouvelles_fonctionnalites.py",
        "demo_test_set_analysis.py",
        
        # Anciens fichiers CSV de test
        "demo_enriched.csv",
        "example_multiple_steps.csv",
        "example_tests.csv",
        "example_tests_corrected.csv",
        "example_tests_separate_labels.csv",
        "example_with_json_steps.csv",
        "test_multiple_steps_output.csv",
        "test_new_processor.csv",
        "test_output_with_steps.csv",
        "test_quick.csv",
        "test_quick2.csv",
        "test_sep_enriched.csv",
        "test_sep_json.csv",
        "test_separate_labels_input.csv",
        "test_separate_labels_output.csv",
        "test_simple.csv",
        "test_simple_enriched.csv",
        "test_simple_enrichissement.csv",
        "test_simple_output.csv",
        "test_simple_virgules.csv",
        "test_with_steps.csv",
        "test_workflow_final_new.csv",
        "test_workflow_new.csv",
        "test_sets_import.csv",
        "test_app_web.csv",
        
        # Anciens fichiers Excel
        "demo_enriched.xlsx",
        "JIRA_Tests_Enriched.xlsx",
        "JIRA_Tests_Enriched_Final.xlsx",
        
        # Anciens fichiers de résultats
        "JIRA_enriched_output.csv",
        "JIRA_enriched_v2.csv",
        "JIRA for Orange 2025-07-07T10_15_34+0200_steps_extracted.csv",
        
        # Anciens modules inutiles
        "async_processor.py",
        "export_formats.py",
        "sample_results.py",
        "simple_app.py",
        "start_web_app.py",
        "test_set_corrector.py",
        "test_set_csv_generator.py",
        
        # Anciens rapports
        "corrections_report.txt",
        "test_set_analysis_report.txt",
        "dashboard_data.json",
        "quality_dashboard.html",
        
        # Documentation excessive
        "ANALYSE_COMPLETE_REALISEE.md",
        "DOCUMENTATION_FINALE.md",
        "GUIDE_ENRICHISSEMENT_PARFAIT.md",
        "GUIDE_TEST_NOUVELLES_FONCTIONNALITES.md",
        "INTEGRATION_COMPLETE.md",
        "RESUME_COMPLET.md",
    ]
    
    # Fichiers à garder (essentiels)
    essential_files = [
        "app.py",  # Application principale
        "test_enrichment_tool_enhanced.py",  # Module d'enrichissement principal
        "json_steps_processor.py",  # Extraction JSON
        "core_network_rules_complete.py",  # Règles d'enrichissement
        "create_excel_output.py",  # Sera modifié pour supprimer Excel
        "test_quality_dashboard.py",  # Dashboard
        "test_set_analyzer.py",  # Analyseur
        "run_app.py",  # Lanceur
        "start_app.py",  # Lanceur optimisé
        "README.md",  # Documentation
        "GUIDE_DEMARRAGE.md",  # Guide utilisateur
    ]
    
    # Fichiers de test à garder (utiles)
    test_files_to_keep = [
        "test_app_web.csv",  # Fichier de test simple
    ]
    
    print("🗑️ Suppression des fichiers inutiles...")
    removed_count = 0
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"   ✅ Supprimé: {file_path}")
                removed_count += 1
            except Exception as e:
                print(f"   ❌ Erreur suppression {file_path}: {e}")
        else:
            print(f"   ⚠️ Déjà absent: {file_path}")
    
    print(f"\n📊 Fichiers supprimés: {removed_count}")
    
    # Nettoyer les dossiers uploads et processed (garder seulement les plus récents)
    print("\n🧹 Nettoyage des dossiers uploads et processed...")
    
    # Nettoyer uploads (garder seulement 5 fichiers les plus récents)
    if os.path.exists("uploads"):
        upload_files = glob.glob("uploads/*")
        upload_files.sort(key=os.path.getmtime, reverse=True)
        
        files_to_keep = upload_files[:5]  # Garder les 5 plus récents
        files_to_remove = upload_files[5:]  # Supprimer les autres
        
        for file_path in files_to_remove:
            try:
                os.remove(file_path)
                print(f"   ✅ Supprimé upload: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"   ❌ Erreur: {e}")
        
        print(f"   📁 Uploads: {len(files_to_keep)} fichiers gardés, {len(files_to_remove)} supprimés")
    
    # Nettoyer processed (garder seulement 5 fichiers les plus récents)
    if os.path.exists("processed"):
        processed_files = glob.glob("processed/*")
        processed_files.sort(key=os.path.getmtime, reverse=True)
        
        files_to_keep = processed_files[:5]  # Garder les 5 plus récents
        files_to_remove = processed_files[5:]  # Supprimer les autres
        
        for file_path in files_to_remove:
            try:
                os.remove(file_path)
                print(f"   ✅ Supprimé processed: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"   ❌ Erreur: {e}")
        
        print(f"   📁 Processed: {len(files_to_keep)} fichiers gardés, {len(files_to_remove)} supprimés")
    
    # Nettoyer __pycache__
    if os.path.exists("__pycache__"):
        try:
            shutil.rmtree("__pycache__")
            print("   ✅ Supprimé: __pycache__")
        except Exception as e:
            print(f"   ❌ Erreur suppression __pycache__: {e}")
    
    print_header("📋 FICHIERS CONSERVÉS")
    
    print("🔧 Fichiers essentiels:")
    for file_path in essential_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ MANQUANT: {file_path}")
    
    print("\n🧪 Fichiers de test conservés:")
    for file_path in test_files_to_keep:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
    
    print("\n📁 Templates conservés:")
    if os.path.exists("templates"):
        templates = os.listdir("templates")
        for template in templates:
            print(f"   ✅ templates/{template}")
    
    return True

def main():
    """
    Fonction principale
    """
    print("🚀 OPTIMISATION DU PROJET D'ENRICHISSEMENT")
    
    # Demander confirmation
    print("\n⚠️ ATTENTION: Cette opération va supprimer de nombreux fichiers!")
    print("📋 Fichiers qui seront supprimés:")
    print("   - Anciens fichiers de test")
    print("   - Anciens demos")
    print("   - Fichiers CSV de test obsolètes")
    print("   - Fichiers Excel")
    print("   - Documentation excessive")
    print("   - Cache Python")
    print("   - Anciens uploads/processed (garde les 5 plus récents)")
    
    response = input("\n❓ Continuer l'optimisation ? (oui/non): ").lower().strip()
    
    if response in ['oui', 'o', 'yes', 'y']:
        success = optimize_project()
        
        if success:
            print_header("🎉 OPTIMISATION TERMINÉE")
            print("✅ Projet optimisé avec succès!")
            print("📊 Espace disque libéré")
            print("🧹 Fichiers inutiles supprimés")
            print("💾 Fichiers essentiels conservés")
            
            print("\n📋 PROCHAINES ÉTAPES:")
            print("1. Supprimer la génération Excel de l'application")
            print("2. Optimiser le dashboard pour le dernier fichier")
            print("3. Tester l'application optimisée")
        else:
            print("❌ Erreur lors de l'optimisation")
    else:
        print("❌ Optimisation annulée")

if __name__ == "__main__":
    main()
