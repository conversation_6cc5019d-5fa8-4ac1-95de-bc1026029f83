Summary,Description,Component/s,Custom field (Manual Test Steps)
CFU Test,Test Call Forwarding Unconditional service in HLR,,"[{""id"": 3763673, ""fields"": {""Action"": ""change HLR/EPC HSS profile for subscriber A"", ""Data"": ""Subscriber A profile"", ""Expected Result"": ""CFU service activated successfully""}}]"
S6a ULR Test,Test Update Location Request in EPC-HSS,,"[{""id"": 3763674, ""fields"": {""Action"": ""Send ULR message to HSS"", ""Data"": ""IMSI=123456789012345"", ""Expected Result"": ""ULA received with success result""}}]"
VoLTE Call Test,Test VoLTE call setup in IMS,,"[{""id"": 3763675, ""fields"": {""Action"": ""Initiate VoLTE call"", ""Data"": ""From: +33123456789 To: +33987654321"", ""Expected Result"": ""Call established successfully""}}]"
Authentication Test,Test authentication procedure,,"[{""id"": 3763676, ""fields"": {""Action"": ""Send authentication request"", ""Data"": """", ""Expected Result"": ""Authentication vector generated""}}]"
Backup Test,Test backup procedure,,"[{""id"": 3763677, ""fields"": {""Action"": ""Execute database backup"", ""Data"": ""Full backup mode"", ""Expected Result"": ""Backup completed without errors""}}]"
