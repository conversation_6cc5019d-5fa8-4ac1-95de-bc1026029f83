{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Indicateur d'étapes -->
        <div class="step-indicator">
            <div class="step active">
                <div class="step-number">1</div>
                <span>Télécharger</span>
            </div>
            <div class="step">
                <div class="step-number">2</div>
                <span>Valider</span>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <span>Enrichir</span>
            </div>
            <div class="step">
                <div class="step-number">4</div>
                <span>Télécharger</span>
            </div>
        </div>

        <div class="card shadow">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-upload"></i> Télécharger votre fichier CSV
                </h3>
            </div>
            <div class="card-body">
                <form action="{{ url_for('upload_file') }}" method="post" enctype="multipart/form-data" id="uploadForm">
                    <div class="upload-area" onclick="document.getElementById('file').click()">
                        <div class="upload-text">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                            <h5>Cliquez ici ou glissez-déposez votre fichier CSV</h5>
                            <p class="text-muted">
                                Formats acceptés : .csv<br>
                                Taille maximale : 16 MB
                            </p>
                        </div>
                        <input type="file" id="file" name="file" accept=".csv" style="display: none;" required>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-check"></i> Valider le fichier
                        </button>
                    </div>
                </form>

                <div class="progress-container">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <p class="text-center mt-2">Téléchargement en cours...</p>
                </div>
            </div>
        </div>

        <!-- Informations sur les colonnes requises -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Format de fichier requis
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-check-circle text-success"></i> Colonnes obligatoires :</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-dot-circle text-primary"></i> <strong>Summary</strong> - Nom du test</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-plus-circle text-info"></i> Colonnes optionnelles (recommandées) :</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-dot-circle text-secondary"></i> Description</li>
                            <li><i class="fas fa-dot-circle text-secondary"></i> Component/s</li>
                            <li><i class="fas fa-dot-circle text-secondary"></i> Action</li>
                            <li><i class="fas fa-dot-circle text-secondary"></i> Expected Result</li>
                            <li><i class="fas fa-dot-circle text-secondary"></i> Test Repository Path</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-lightbulb"></i>
                    <strong>Conseil :</strong> Plus vous avez de colonnes avec du contenu, 
                    plus l'enrichissement automatique sera précis et pertinent.
                </div>
            </div>
        </div>

        <!-- Aperçu des fonctionnalités -->
        <div class="card mt-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-magic"></i> Ce que fait l'enrichissement automatique
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-tags fa-2x text-primary mb-2"></i>
                            <h6>Labels ISTQB</h6>
                            <p class="small text-muted">
                                Applique automatiquement les labels selon les standards ISTQB
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-sitemap fa-2x text-success mb-2"></i>
                            <h6>Composants</h6>
                            <p class="small text-muted">
                                Identifie et assigne les composants techniques (HLR, EPC-HSS, IMS, etc.)
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-layer-group fa-2x text-info mb-2"></i>
                            <h6>Test Sets</h6>
                            <p class="small text-muted">
                                Organise les tests en groupes fonctionnels cohérents
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        document.querySelector('.upload-text').innerHTML = 
            `<i class="fas fa-file-csv text-success fa-2x mb-2"></i><br>
             <strong>${file.name}</strong><br>
             <small class="text-muted">${(file.size / 1024 / 1024).toFixed(2)} MB</small>`;
    }
});

document.getElementById('uploadForm').addEventListener('submit', function(e) {
    const progressContainer = document.querySelector('.progress-container');
    const progressBar = document.querySelector('.progress-bar');
    
    progressContainer.style.display = 'block';
    
    // Simulation de progression
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        progressBar.style.width = progress + '%';
    }, 200);
    
    // Arrêter la simulation après 5 secondes
    setTimeout(() => {
        clearInterval(interval);
        progressBar.style.width = '100%';
    }, 5000);
});
</script>
{% endblock %}
