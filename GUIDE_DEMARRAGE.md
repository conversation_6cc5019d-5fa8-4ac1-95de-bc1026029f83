# 🚀 Guide de Démarrage Rapide - Portail Web d'Enrichissement (Version Optimisée)

## ✨ Nouveautés Version Optimisée

### 🎯 **Améliorations Majeures :**
- ✅ **CSV uniquement** : Plus de génération Excel (plus rapide et plus léger)
- ✅ **Projet allégé** : 81 fichiers inutiles supprimés (demos, tests obsolètes, etc.)
- ✅ **Dashboard automatique** : Se concentre automatiquement sur le dernier fichier enrichi
- ✅ **Format Labels cohérent** : Toujours des colonnes Labels multiples identiques
- ✅ **Espace disque libéré** : 166 fichiers supprimés (uploads/processed nettoyés)

### 📊 **Résultats Garantis :**
```csv
Summary,Component/s,Action,Data,Expected Result,Test Set,Labels,Labels,Labels,Labels,Labels,Labels
S6a ULR,EPC-HSS,Send ULR,IMSI=123,HSS responds,HLR Location Management,Functional,System,Manual,Regression,4G,Diameter
```

## 📋 Prérequis

Assurez-vous d'avoir Python 3.7+ installé sur votre système.

## ⚡ Installation Express

1. **Installer les dépendances :**
```bash
pip install flask pandas openpyxl
```

2. **Lancer l'application :**
```bash
python run_app.py
```

3. **Ouvrir votre navigateur :**
```
http://127.0.0.1:5000
```

### ✅ **Scripts de Lancement Disponibles**
Tous les scripts ont été corrigés et testés :
- ✅ `python app.py` - Lancement direct
- ✅ `python start_app.py` - Avec vérifications des modules
- ✅ `python run_app.py` - Avec debug et vérifications complètes (Recommandé)

## 🎯 Test Rapide avec l'Exemple

1. **Utilisez le fichier d'exemple fourni :**
   - Fichier : `example_tests.csv`
   - Contient 25 tests représentatifs de différents domaines

2. **Processus de test :**
   - Téléchargez `example_tests.csv` via l'interface web
   - Validez le format (automatique)
   - Lancez l'enrichissement
   - Téléchargez les résultats

## 📁 Structure des Fichiers

```
📦 Projet
├── 🌐 app.py                     # Application Flask principale
├── 🔧 test_enrichment_tool.py    # Moteur d'enrichissement
├── 📊 create_excel_output.py     # Générateur Excel
├── 🚀 run_app.py                 # Script de lancement
├── 📄 example_tests.csv          # Fichier d'exemple
├── 📖 GUIDE_DEMARRAGE.md         # Ce guide
├── 📂 templates/                 # Templates HTML
│   ├── base.html
│   ├── index.html
│   ├── validate.html
│   ├── enrich.html
│   ├── rules.html
│   └── help.html
├── 📂 uploads/                   # Fichiers téléchargés
└── 📂 processed/                 # Fichiers enrichis
```

## 🎨 Interface Web - Fonctionnalités

### 🏠 Page d'Accueil
- **Téléchargement par glisser-déposer** ou sélection de fichier
- **Validation automatique** du format CSV
- **Indicateur de progression** visuel
- **Conseils** pour optimiser l'enrichissement

### ✅ Page de Validation
- **Analyse détaillée** du fichier téléchargé
- **Statistiques** : nombre de tests, colonnes détectées
- **Prédiction de qualité** de l'enrichissement
- **Aperçu** des colonnes qui seront ajoutées/modifiées

### ⚡ Page d'Enrichissement
- **Processus en temps réel** avec barre de progression
- **Étapes visuelles** : lecture → analyse → labellisation → génération
- **Aperçu des résultats** avec statistiques
- **Téléchargement** CSV et Excel

### 📚 Pages d'Information
- **Règles de Labellisation** : documentation complète des règles
- **Aide** : guide d'utilisation détaillé avec FAQ

## 🏷️ Exemples de Résultats

### Avant Enrichissement :
```csv
Summary,Description,Component/s,Action,Expected Result
CFU Test,Test Call Forwarding Unconditional,,Activate CFU,CFU activated
S6a ULR,Test Update Location Request,,Send ULR,HSS responds
VoLTE Call,Test VoLTE call setup,,Setup call,Call established
```

### Après Enrichissement :
```csv
Summary,Description,Component/s,Test Set,Labels,Action,Expected Result
CFU Test,Test Call Forwarding Unconditional,HLR,HLR Call Forwarding Services,"Functional, System, Manual, Regression, 2G, MAP, CallForwarding",Activate CFU,CFU activated
S6a ULR,Test Update Location Request,EPC-HSS,EPC HSS Location Management,"Functional, System, Manual, Regression, 4G, Diameter, S6a, UpdateLocation",Send ULR,HSS responds
VoLTE Call,Test VoLTE call setup,IMS-HSS,IMS VoLTE Services,"Functional, System, Manual, Regression, 4G, IMS, VoLTE",Setup call,Call established
```

## 📊 Statistiques Typiques

Avec le fichier d'exemple (`example_tests.csv`) :
- **25 tests** traités
- **6 composants** identifiés (HLR, EPC-HSS, IMS-HSS, Security, OAM, COMMON)
- **12 test sets** créés
- **20 tests fonctionnels**, **5 tests non-fonctionnels**

## 🔧 Personnalisation

### Modifier les Règles de Labellisation
Éditez le fichier `test_enrichment_tool.py` :
```python
# Ajouter de nouveaux patterns
self.custom_patterns = [
    r'votre_pattern', r'autre_pattern'
]
```

### Ajouter de Nouveaux Composants
```python
# Dans la méthode analyze_test_content
elif any(re.search(pattern, full_content) for pattern in self.custom_patterns):
    component = "VOTRE_COMPOSANT"
    labels.extend(["VosLabels"])
    test_set = "Votre Test Set"
```

## 🚨 Dépannage

### Problème : L'application ne se lance pas
```bash
# Vérifier Python
python --version

# Réinstaller les dépendances
pip install --upgrade flask pandas openpyxl
```

### Problème : Fichier CSV non reconnu
- Vérifiez l'encodage (UTF-8 recommandé)
- Assurez-vous que la colonne "Summary" existe
- Vérifiez le séparateur (virgule)

### Problème : Enrichissement imprécis
- Ajoutez plus de détails dans les colonnes Description, Action, Expected Result
- Utilisez des termes techniques précis (HLR, HSS, VoLTE, S6a, etc.)
- Mentionnez les protocoles et interfaces

## 📞 Support

- **Documentation** : Consultez la page "Aide" dans l'interface web
- **Règles** : Voir la page "Règles de Labellisation"
- **Exemples** : Utilisez `example_tests.csv` comme référence

## 🎉 Prêt à Commencer !

1. Lancez `python run_app.py`
2. Ouvrez http://127.0.0.1:5000
3. Testez avec `example_tests.csv`
4. Importez vos propres fichiers
5. Téléchargez les résultats enrichis

**Bon enrichissement ! 🚀**
