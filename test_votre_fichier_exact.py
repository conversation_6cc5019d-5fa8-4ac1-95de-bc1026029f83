#!/usr/bin/env python3
"""
Test avec votre fichier exact pour vérifier que l'enrichissement fonctionne
"""

import os
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def test_fichier_exact():
    """
    Test avec votre fichier exact
    """
    print("🧪 TEST AVEC VOTRE FICHIER EXACT")
    print("="*70)
    
    # Créer le fichier avec votre contenu exact
    csv_content = '''Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project description,Project url,Priority,Resolution,Assignee,Reporter,Creator,Created,Updated,Last Viewed,Resolved,Affects Version/s,Fix Version/s,Component/s,Due Date,Votes,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Description,Environment,Watchers,Log Work,Original Estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Attachment,Custom field (Automation status),Custom field (Branch),Custom field (Business Gain),Custom field (CDR/MC),Custom field (Category List),Custom field (Change completion date),Custom field (Change start date),Custom field (Change type),Custom field (Cost),Custom field (Cucumber Scenario),Custom field (Cucumber Test Type),Customer Request Type,Custom field (Dataset values),Custom field (Dataset values),Custom field (Date MEP),Custom field (Date UAT),Demande SLA 16H,Demande SLA 16H simplified,Custom field (Domain List),Durée de traitement,Durée de traitement simplified,Délai de Qualification,Délai de Qualification simplified,Délai de prise en charge,Délai de prise en charge simplified,Custom field (Effects),Custom field (Entity List),Custom field (Environment),Custom field (Epic Link),Custom field (External Contributor/s),Custom field (External issue ID),Fermeture apres x jours,Fermeture apres x jours simplified,Custom field (First Backlog Transition),Custom field (First key),Custom field (First-Name),Custom field (Flagged),Custom field (Generic Test Definition),Custom field (Groups),Custom field (Groups),Custom field (Impact),Custom field (Impacted Entity),Custom field (Jira Project Type),Custom field (Last-Name),Custom field (Linked major incidents),Custom field (List Entity),Custom field (MVP Macro Budget (K€)),Custom field (Mail),Custom field (Manual Test Steps),Custom field (Operational categorization),Custom field (Organizations),Custom field (Original story points),Custom field (Overcoast),Custom field (Parent Key),Custom field (Parent Link),Custom field (Penalties),Custom field (Platform),Custom field (Pre-Conditions association with a Test),Prise en compte,Prise en compte simplified,Custom field (Product categorization),Custom field (QC),Custom field (Qualification Date),Custom field (Rank),Custom field (Ref. Project CARTO),Custom field (Reference Code),Custom field (Request participants),Resolution Time SLA,Resolution Time SLA simplified,Response Time SLA,Response Time SLA simplified,Custom field (Result),Custom field (Review date),Custom field (Revision),Satisfaction score (out of 5),Custom field (Scoring),Sprint,Custom field (Steps Count),Custom field (Structure Index Monitor),Custom field (Support),Custom field (Target end),Custom field (Target start),Custom field (Team),Custom field (Team List),"Temps d&#39;attribution","Temps d&#39;attribution simplified",Temps première réponse,Temps première réponse simplified,Custom field (Test Execution Status),Custom field (Test Plans associated with a Test),Custom field (Test Repository Path),Custom field (Test Sets association with a Test),Custom field (Test Sets association with a Test),Custom field (Test Type),Custom field (TestRunStatus),Time to close after resolution,Time to close after resolution simplified,Time to first response,Time to first response simplified,Time to resolution,Time to resolution simplified,Custom field (TimeRecup (deprecated)),Custom field (User Activity),Custom field (Workaround (deprecated)),Comment,Action,Data,Expected Result
test File Storage DB,SANTSDM-1896,8939088,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,12/Jul/25 3:45 PM,17/Jul/25 9:59 AM,,,,ZTE-SPECIFIC,,0,Functional,Manual,Regression,System,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1896,,,,,,,,software,,,,,,[],,,,,,,,,,,,,,,"0|j1qd1k:",,,,,,,,,,,,,,0.0,,,,,,,,,,,"{""issueId"":8939088,""testStatuses"":[]}",,,,,Manual,TODO,,,,,,,,,,,,
SOAP provisionning,SANTSDM-1895,8939087,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:34 PM,15/Jul/25 12:34 PM,,,,,,0,Functional,Manual,OAM,Regression,SOAP,System,,,"SUMMARY : None\nPRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1895,,,,,,,,software,,,,,,"[{""id"":3844657,""index"":1,""fields"":{""Action"":""\""- Verify that user provisionning can be executed through SOAP command\n\"""",""Data"":"""",""Expected Result"":""\""- User provisionning operate with success with SOAP command\n\""""},""attachments"":[],""testVersionId"":1102789}]",,,,,,,,,,,,,,,"0|j1qd1c:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939087,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/COMMON Non Regression/Subscriber management,SANTSDM-1908,,Manual,TODO,,,,,,,,,,"- Verify that user provisionning can be executed through SOAP command",,"- User provisionning operate with success with SOAP command"
MML provisionning,SANTSDM-1894,8939086,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:34 PM,17/Jul/25 10:39 AM,,,,,,0,Functional,Manual,MML,OAM,Regression,System,,,"SUMMARY : None\nPRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1894,,,,,,,,software,,,,,,"[{""id"":3844656,""index"":1,""fields"":{""Action"":""\""- Verify that user provisionning can be executed through MMLcommand\n\"""",""Data"":"""",""Expected Result"":""\""- User provisionning operate with success with MML command\n\""""},""attachments"":[],""testVersionId"":1102788}]",,,,,,,,,,,,,,,"0|j1qd14:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939086,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/COMMON Non Regression/Subscriber management,SANTSDM-1908,,Manual,TODO,,,,,,,,,,"- Verify that user provisionning can be executed through MMLcommand",,"- User provisionning operate with success with MML command"'''
    
    with open('votre_fichier_exact.csv', 'w', encoding='utf-8') as f:
        f.write(csv_content)
    
    print("📁 Fichier créé avec votre contenu exact")
    
    # Analyser le fichier
    lines = csv_content.strip().split('\n')
    header = lines[0]
    data_lines = lines[1:]
    
    columns_count = header.count(',') + 1
    print(f"📊 Analyse du fichier :")
    print(f"   Lignes de données : {len(data_lines)}")
    print(f"   Colonnes : {columns_count}")
    print(f"   Colonnes Action/Data/Expected Result présentes : {'Action' in header and 'Data' in header and 'Expected Result' in header}")
    
    # Test d'enrichissement
    try:
        print(f"\n🎯 Lancement de l'enrichissement...")
        tool = TestEnrichmentToolEnhanced()
        tool.enrich_csv_enhanced('votre_fichier_exact.csv', 'votre_fichier_enrichi.csv', 'separate')
        
        print("✅ Enrichissement terminé avec succès")
        
        # Analyser le résultat
        if os.path.exists('votre_fichier_enrichi.csv'):
            with open('votre_fichier_enrichi.csv', 'r', encoding='utf-8') as f:
                result_content = f.read()
                result_lines = result_content.strip().split('\n')
            
            print(f"\n📋 Résultat de l'enrichissement :")
            print(f"   Lignes finales : {len(result_lines)}")
            
            if result_lines:
                result_header = result_lines[0]
                
                # Détecter le séparateur utilisé
                if result_header.count(',') > result_header.count(';'):
                    separator = ','
                    final_columns = result_header.count(',') + 1
                else:
                    separator = ';'
                    final_columns = result_header.count(';') + 1
                
                print(f"   Séparateur utilisé : '{separator}'")
                print(f"   Colonnes finales : {final_columns}")
                
                # Compter les Labels
                labels_count = result_header.count('Labels')
                print(f"   Colonnes Labels : {labels_count}")
                
                # Vérifier les colonnes essentielles
                essential_columns = ['Summary', 'Action', 'Data', 'Expected Result', 'Component/s', 'Test Set']
                missing_essential = []
                for col in essential_columns:
                    if col not in result_header:
                        missing_essential.append(col)
                
                print(f"   Colonnes essentielles manquantes : {len(missing_essential)}")
                if missing_essential:
                    for col in missing_essential:
                        print(f"     - {col}")
                
                # Afficher un échantillon de la première ligne de données
                if len(result_lines) > 1:
                    first_data_line = result_lines[1]
                    data_parts = first_data_line.split(separator)
                    
                    print(f"\n📋 Échantillon de la première ligne enrichie :")
                    header_parts = result_header.split(separator)
                    
                    # Afficher les 10 premières colonnes
                    for i in range(min(10, len(header_parts), len(data_parts))):
                        col_name = header_parts[i].strip()
                        col_value = data_parts[i].strip()[:50] + ('...' if len(data_parts[i]) > 50 else '')
                        print(f"   {col_name} : {col_value}")
                    
                    if len(header_parts) > 10:
                        print(f"   ... et {len(header_parts) - 10} autres colonnes")
                
                success = labels_count >= 8 and len(missing_essential) == 0 and final_columns > columns_count
                
                print(f"\n🎯 RÉSULTAT FINAL :")
                print(f"✅ Enrichissement réussi : {'OUI' if success else 'NON'}")
                print(f"✅ Labels en colonnes séparées : {'OUI' if labels_count >= 8 else 'NON'}")
                print(f"✅ Colonnes essentielles présentes : {'OUI' if len(missing_essential) == 0 else 'NON'}")
                print(f"✅ Colonnes ajoutées : {'OUI' if final_columns > columns_count else 'NON'}")
                
                if success:
                    print(f"\n🎉 VOTRE FICHIER PEUT MAINTENANT ÊTRE ENRICHI !")
                    print(f"📊 Résumé :")
                    print(f"   {columns_count} colonnes → {final_columns} colonnes")
                    print(f"   {len(data_lines)} tests → {len(result_lines)-1} lignes finales")
                    print(f"   {labels_count} colonnes Labels créées")
                    print(f"   Format : {separator} (séparateur)")
                else:
                    print(f"\n⚠️  L'enrichissement fonctionne mais nécessite des ajustements")
            else:
                print("❌ Fichier enrichi vide")
                success = False
            
            # Nettoyer
            os.remove('votre_fichier_enrichi.csv')
        else:
            print("❌ Fichier enrichi non généré")
            success = False
    
    except Exception as e:
        print(f"❌ Erreur lors de l'enrichissement : {e}")
        print(f"   Type d'erreur : {type(e).__name__}")
        import traceback
        print(f"   Détails complets :")
        print(traceback.format_exc())
        success = False
    
    # Nettoyer
    os.remove('votre_fichier_exact.csv')
    
    return success

if __name__ == "__main__":
    success = test_fichier_exact()
    
    if success:
        print(f"\n🎊 SUCCÈS TOTAL !")
        print(f"Votre fichier avec les colonnes Action, Data, Expected Result")
        print(f"peut maintenant être enrichi sans problème !")
    else:
        print(f"\n⚠️  Des ajustements sont encore nécessaires")
