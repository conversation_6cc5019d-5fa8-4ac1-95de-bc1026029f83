#!/usr/bin/env python3
"""
Test de l'enrichissement avec de nombreuses colonnes comme dans votre exemple
"""

import pandas as pd
import os
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def print_header(title):
    print("\n" + "="*70)
    print(f"  {title}")
    print("="*70)

def test_enrichissement_nombreuses_colonnes():
    """
    Test avec un fichier ayant de nombreuses colonnes comme votre exemple
    """
    print_header("🔧 TEST ENRICHISSEMENT NOMBREUSES COLONNES")
    
    # Créer un fichier avec de nombreuses colonnes comme votre exemple
    data = {
        'Summary': ['test File Storage DB', 'SOAP provisionning'],
        'Issue key': ['SANTSDM-1896', 'SANTSDM-1895'],
        'Issue id': ['8939088', '8939087'],
        'Issue Type': ['Test', 'Test'],
        'Status': ['To Do', 'To Do'],
        'Project key': ['SANTSDM', 'SANTSDM'],
        'Project name': ['SANDBOX_TEST_SDM', 'SANDBOX_TEST_SDM'],
        'Project type': ['software', 'software'],
        'Project lead': ['<EMAIL>', '<EMAIL>'],
        'Project description': ['', ''],
        'Project url': ['', ''],
        'Priority': ['Lowest', 'Lowest'],
        'Resolution': ['', ''],
        'Assignee': ['', ''],
        'Reporter': ['<EMAIL>', '<EMAIL>'],
        'Creator': ['<EMAIL>', '<EMAIL>'],
        'Created': ['11/Jul/25 6:44 PM', '11/Jul/25 6:44 PM'],
        'Updated': ['12/Jul/25 3:45 PM', '15/Jul/25 5:34 PM'],
        'Last Viewed': ['17/Jul/25 9:59 AM', '15/Jul/25 12:34 PM'],
        'Resolved': ['', ''],
        'Affects Version/s': ['', ''],
        'Fix Version/s': ['', ''],
        'Component/s': ['ZTE-SPECIFIC', ''],
        'Due Date': ['', ''],
        'Votes': ['0', '0'],
        'Labels': ['Functional', 'Functional'],
        'Labels.1': ['Manual', 'Manual'],
        'Labels.2': ['Regression', 'OAM'],
        'Labels.3': ['System', 'Regression'],
        'Labels.4': ['', 'SOAP'],
        'Labels.5': ['', 'System'],
        'Labels.6': ['', ''],
        'Labels.7': ['', ''],
        'Description': ['', 'SUMMARY : None\nPRECONDITIONS : None'],
        'Environment': ['', ''],
        'Watchers': ['', ''],
        'Log Work': ['', ''],
        'Original Estimate': ['', ''],
        'Remaining Estimate': ['', ''],
        'Time Spent': ['', ''],
        'Work Ratio': ['', ''],
        'Σ Original Estimate': ['', ''],
        'Σ Remaining Estimate': ['', ''],
        'Σ Time Spent': ['', ''],
        'Security Level': ['', ''],
        'Attachment': ['', ''],
        'Custom field (Manual Test Steps)': [
            '[]',
            '[{"id":3844657,"index":1,"fields":{"Action":"- Verify that user provisionning can be executed through SOAP command","Data":"","Expected Result":"- User provisionning operate with success with SOAP command"},"attachments":[],"testVersionId":1102789}]'
        ],
        'Custom field (Test Repository Path)': ['', '/SDM COMMON TESTCASES/NON REGRESSION Testcases/COMMON Non Regression/Subscriber management'],
        'Custom field (Test Type)': ['Manual', 'Manual'],
        'Custom field (TestRunStatus)': ['TODO', 'TODO'],
        # Ajouter les colonnes Action, Data, Expected Result après extraction JSON
        'Action': ['', 'Verify that user provisionning can be executed through SOAP command'],
        'Data': ['', ''],
        'Expected Result': ['', 'User provisionning operate with success with SOAP command']
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_nombreuses_colonnes.csv', index=False)
    
    print(f"📁 Fichier créé avec {len(data)} colonnes (similaire à votre exemple)")
    print(f"   Lignes : {len(df)}")
    print(f"   Colonnes : {len(df.columns)}")
    
    # Test d'enrichissement
    try:
        tool = TestEnrichmentToolEnhanced()
        tool.enrich_csv_enhanced('test_nombreuses_colonnes.csv', 'test_nombreuses_enriched.csv', 'separate')
        
        print("🎯 Enrichissement réussi")
        
        # Vérifier le fichier enrichi
        if os.path.exists('test_nombreuses_enriched.csv'):
            with open('test_nombreuses_enriched.csv', 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if lines:
                header = lines[0].strip()
                columns = header.split(',')  # Utiliser virgule car c'est le séparateur original
                
                print(f"📋 Fichier enrichi :")
                print(f"   Lignes : {len(lines)}")
                print(f"   Colonnes : {len(columns)}")
                
                # Compter les colonnes Labels
                labels_columns = [col for col in columns if 'Labels' in col]
                labels_count = len(labels_columns)
                
                print(f"📊 Colonnes Labels créées : {labels_count}")
                
                # Vérifier les colonnes essentielles
                essential_columns = ['Summary', 'Action', 'Data', 'Expected Result', 'Component/s', 'Test Set']
                missing_essential = []
                for col in essential_columns:
                    if col not in header:
                        missing_essential.append(col)
                
                print(f"📊 Colonnes essentielles manquantes : {len(missing_essential)}")
                if missing_essential:
                    for col in missing_essential:
                        print(f"     - {col}")
                
                # Afficher un échantillon des colonnes
                print(f"\n📋 Échantillon des colonnes finales :")
                for i, col in enumerate(columns[:15]):
                    print(f"   {i+1}. {col}")
                if len(columns) > 15:
                    print(f"   ... et {len(columns) - 15} autres colonnes")
                
                success = labels_count >= 8 and len(missing_essential) == 0
                print(f"\n✅ Enrichissement réussi : {'OUI' if success else 'NON'}")
                print(f"✅ Labels en colonnes séparées : {'OUI' if labels_count >= 8 else 'NON'}")
                print(f"✅ Colonnes essentielles présentes : {'OUI' if len(missing_essential) == 0 else 'NON'}")
            else:
                print("❌ Fichier enrichi vide")
                success = False
            
            # Nettoyer
            os.remove('test_nombreuses_enriched.csv')
        else:
            print("❌ Fichier enrichi non généré")
            success = False
    
    except Exception as e:
        print(f"❌ Erreur lors de l'enrichissement : {e}")
        print(f"   Type d'erreur : {type(e).__name__}")
        import traceback
        print(f"   Détails : {traceback.format_exc()}")
        success = False
    
    # Nettoyer
    os.remove('test_nombreuses_colonnes.csv')
    
    return success

def test_enrichissement_fichier_reel():
    """
    Test avec un extrait de votre fichier réel
    """
    print_header("📋 TEST AVEC EXTRAIT FICHIER RÉEL")
    
    # Créer un fichier avec l'extrait exact de votre exemple
    csv_content = '''Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project description,Project url,Priority,Resolution,Assignee,Reporter,Creator,Created,Updated,Last Viewed,Resolved,Affects Version/s,Fix Version/s,Component/s,Due Date,Votes,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Description,Environment,Watchers,Log Work,Original Estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Attachment,Custom field (Automation status),Custom field (Branch),Custom field (Business Gain),Custom field (CDR/MC),Custom field (Category List),Custom field (Change completion date),Custom field (Change start date),Custom field (Change type),Custom field (Cost),Custom field (Cucumber Scenario),Custom field (Cucumber Test Type),Customer Request Type,Custom field (Dataset values),Custom field (Dataset values),Custom field (Date MEP),Custom field (Date UAT),Demande SLA 16H,Demande SLA 16H simplified,Custom field (Domain List),Durée de traitement,Durée de traitement simplified,Délai de Qualification,Délai de Qualification simplified,Délai de prise en charge,Délai de prise en charge simplified,Custom field (Effects),Custom field (Entity List),Custom field (Environment),Custom field (Epic Link),Custom field (External Contributor/s),Custom field (External issue ID),Fermeture apres x jours,Fermeture apres x jours simplified,Custom field (First Backlog Transition),Custom field (First key),Custom field (First-Name),Custom field (Flagged),Custom field (Generic Test Definition),Custom field (Groups),Custom field (Groups),Custom field (Impact),Custom field (Impacted Entity),Custom field (Jira Project Type),Custom field (Last-Name),Custom field (Linked major incidents),Custom field (List Entity),Custom field (MVP Macro Budget (K€)),Custom field (Mail),Custom field (Manual Test Steps),Custom field (Operational categorization),Custom field (Organizations),Custom field (Original story points),Custom field (Overcoast),Custom field (Parent Key),Custom field (Parent Link),Custom field (Penalties),Custom field (Platform),Custom field (Pre-Conditions association with a Test),Prise en compte,Prise en compte simplified,Custom field (Product categorization),Custom field (QC),Custom field (Qualification Date),Custom field (Rank),Custom field (Ref. Project CARTO),Custom field (Reference Code),Custom field (Request participants),Resolution Time SLA,Resolution Time SLA simplified,Response Time SLA,Response Time SLA simplified,Custom field (Result),Custom field (Review date),Custom field (Revision),Satisfaction score (out of 5),Custom field (Scoring),Sprint,Custom field (Steps Count),Custom field (Structure Index Monitor),Custom field (Support),Custom field (Target end),Custom field (Target start),Custom field (Team),Custom field (Team List),"Temps d&#39;attribution","Temps d&#39;attribution simplified",Temps première réponse,Temps première réponse simplified,Custom field (Test Execution Status),Custom field (Test Plans associated with a Test),Custom field (Test Repository Path),Custom field (Test Sets association with a Test),Custom field (Test Sets association with a Test),Custom field (Test Type),Custom field (TestRunStatus),Time to close after resolution,Time to close after resolution simplified,Time to first response,Time to first response simplified,Time to resolution,Time to resolution simplified,Custom field (TimeRecup (deprecated)),Custom field (User Activity),Custom field (Workaround (deprecated)),Comment,Action,Data,Expected Result
test File Storage DB,SANTSDM-1896,8939088,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,12/Jul/25 3:45 PM,17/Jul/25 9:59 AM,,,,ZTE-SPECIFIC,,0,Functional,Manual,Regression,System,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1896,,,,,,,,software,,,,,,[],,,,,,,,,,,,,,,"0|j1qd1k:",,,,,,,,,,,,,,0.0,,,,,,,,,,,"{""issueId"":8939088,""testStatuses"":[]}",,,,,Manual,TODO,,,,,,,,,,,,
SOAP provisionning,SANTSDM-1895,8939087,Test,To Do,SANTSDM,SANDBOX_TEST_SDM,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,11/Jul/25 6:44 PM,15/Jul/25 5:34 PM,15/Jul/25 12:34 PM,,,,,,0,Functional,Manual,OAM,Regression,SOAP,System,,,"SUMMARY : None\nPRECONDITIONS : None",,,,,,,,,,,,,,,,,,,,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,SANTSDM-1895,,,,,,,,software,,,,,,"[{""id"":3844657,""index"":1,""fields"":{""Action"":""\""- Verify that user provisionning can be executed through SOAP command\n\"""",""Data"":"""",""Expected Result"":""\""- User provisionning operate with success with SOAP command\n\""""},""attachments"":[],""testVersionId"":1102789}]",,,,,,,,,,,,,,,"0|j1qd1c:",,,,,,,,,,,,,,1.0,,,,,,,,,,,"{""issueId"":8939087,""testStatuses"":[]}",,/SDM COMMON TESTCASES/NON REGRESSION Testcases/COMMON Non Regression/Subscriber management,SANTSDM-1908,,Manual,TODO,,,,,,,,,,"- Verify that user provisionning can be executed through SOAP command",,"- User provisionning operate with success with SOAP command"'''
    
    with open('test_fichier_reel.csv', 'w', encoding='utf-8') as f:
        f.write(csv_content)
    
    print("📁 Fichier créé avec l'extrait exact de votre exemple")
    
    # Compter les colonnes
    header_line = csv_content.split('\n')[0]
    columns_count = header_line.count(',') + 1
    print(f"   Colonnes détectées : {columns_count}")
    
    # Test d'enrichissement
    try:
        tool = TestEnrichmentToolEnhanced()
        tool.enrich_csv_enhanced('test_fichier_reel.csv', 'test_fichier_reel_enriched.csv', 'separate')
        
        print("🎯 Enrichissement réussi avec fichier réel")
        
        # Vérifier le résultat
        if os.path.exists('test_fichier_reel_enriched.csv'):
            with open('test_fichier_reel_enriched.csv', 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"📋 Résultat :")
            print(f"   Lignes : {len(lines)}")
            if lines:
                header = lines[0].strip()
                final_columns = header.count(',') + 1
                print(f"   Colonnes finales : {final_columns}")
                
                # Compter les Labels
                labels_count = header.count('Labels')
                print(f"   Colonnes Labels : {labels_count}")
            
            success = True
            print("✅ Enrichissement du fichier réel réussi")
            
            # Nettoyer
            os.remove('test_fichier_reel_enriched.csv')
        else:
            print("❌ Fichier enrichi non généré")
            success = False
    
    except Exception as e:
        print(f"❌ Erreur lors de l'enrichissement du fichier réel : {e}")
        success = False
    
    # Nettoyer
    os.remove('test_fichier_reel.csv')
    
    return success

def main():
    """
    Test complet de l'enrichissement avec nombreuses colonnes
    """
    print("🧪 TEST ENRICHISSEMENT AVEC NOMBREUSES COLONNES")
    
    # Test 1 : Nombreuses colonnes simulées
    test1 = test_enrichissement_nombreuses_colonnes()
    
    # Test 2 : Fichier réel
    test2 = test_enrichissement_fichier_reel()
    
    print_header("📊 RÉSULTATS DES TESTS")
    
    print(f"✅ Test nombreuses colonnes simulées : {'OK' if test1 else 'ÉCHEC'}")
    print(f"✅ Test fichier réel : {'OK' if test2 else 'ÉCHEC'}")
    
    if test1 and test2:
        print(f"\n🎉 ENRICHISSEMENT AVEC NOMBREUSES COLONNES FONCTIONNE !")
        print(f"✅ Lecture robuste des fichiers CSV complexes")
        print(f"✅ Adaptation automatique au nombre de colonnes")
        print(f"✅ Enrichissement avec colonnes Labels séparées")
        print(f"✅ Compatible avec vos fichiers d'export Jira")
        print(f"🎯 L'enrichissement est maintenant robuste")
    else:
        print(f"\n⚠️  CERTAINES AMÉLIORATIONS SONT NÉCESSAIRES")

if __name__ == "__main__":
    main()
