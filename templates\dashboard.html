{% extends "base.html" %}

{% block title %}Dashboard - Enrichissement Tests Jira Xray{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-tachometer-alt"></i> Dashboard - Dernier Fichier Traité
                </h3>
                <small class="text-muted" id="current-file-info">Chargement...</small>
            </div>
            <div class="card-body">
                
                <!-- Statistiques principales -->
                <div class="row mb-4" id="main-stats">
                    <div class="col-md-3">
                        <div class="card text-center border-primary">
                            <div class="card-body">
                                <i class="fas fa-file-csv fa-2x text-primary mb-2"></i>
                                <h3 class="text-primary" id="current-file-tests">-</h3>
                                <p class="card-text">Tests dans ce Fichier</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center border-success">
                            <div class="card-body">
                                <i class="fas fa-tags fa-2x text-success mb-2"></i>
                                <h3 class="text-success" id="current-file-labels">-</h3>
                                <p class="card-text">Colonnes Labels</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center border-info">
                            <div class="card-body">
                                <i class="fas fa-sitemap fa-2x text-info mb-2"></i>
                                <h3 class="text-info" id="current-file-components">-</h3>
                                <p class="card-text">Composants Uniques</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center border-warning">
                            <div class="card-body">
                                <i class="fas fa-layer-group fa-2x text-warning mb-2"></i>
                                <h3 class="text-warning" id="current-file-testsets">-</h3>
                                <p class="card-text">Test Sets</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Graphiques -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie"></i> Répartition des Composants
                                </h5>
                                <small class="text-muted">Dans le dernier fichier traité</small>
                            </div>
                            <div class="card-body">
                                <canvas id="componentsChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-doughnut"></i> Répartition des Test Sets
                                </h5>
                                <small class="text-muted">Dans le dernier fichier traité</small>
                            </div>
                            <div class="card-body">
                                <canvas id="testSetsChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Détails du fichier actuel -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i> Détails du Dernier Fichier Traité
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-sitemap"></i> Top 5 Composants</h6>
                                <div id="top-components" class="mb-3">
                                    <small class="text-muted">Chargement...</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-layer-group"></i> Top 5 Test Sets</h6>
                                <div id="top-testsets" class="mb-3">
                                    <small class="text-muted">Chargement...</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fichiers récents -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clock"></i> Fichiers Récemment Traités
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="recent-files-table">
                                <thead>
                                    <tr>
                                        <th>Fichier</th>
                                        <th>Tests</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="recent-files-body">
                                    <tr>
                                        <td colspan="4" class="text-center">
                                            <i class="fas fa-spinner fa-spin"></i> Chargement...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Actions rapides -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-bolt"></i> Actions Rapides
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <a href="{{ url_for('index') }}" class="btn btn-primary btn-lg w-100 mb-2">
                                            <i class="fas fa-upload"></i><br>
                                            Nouveau Fichier
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="{{ url_for('show_history') }}" class="btn btn-info btn-lg w-100 mb-2">
                                            <i class="fas fa-history"></i><br>
                                            Historique
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="{{ url_for('show_rules') }}" class="btn btn-warning btn-lg w-100 mb-2">
                                            <i class="fas fa-list-ul"></i><br>
                                            Règles
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <button onclick="refreshDashboard()" class="btn btn-success btn-lg w-100 mb-2">
                                            <i class="fas fa-sync-alt"></i><br>
                                            Actualiser
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let componentsChart, testSetsChart;

// Charger les données du dashboard
async function loadDashboardData() {
    try {
        const response = await fetch('/api/latest-file-stats');
        const data = await response.json();

        if (data.error) {
            document.getElementById('current-file-info').textContent = 'Aucun fichier traité';
            return;
        }

        // Mettre à jour les informations du fichier
        document.getElementById('current-file-info').textContent = `Fichier: ${data.filename} - Traité le ${data.processed_date}`;

        // Mettre à jour les statistiques du fichier actuel
        document.getElementById('current-file-tests').textContent = data.total_tests || 0;
        document.getElementById('current-file-labels').textContent = data.labels_columns || 0;
        document.getElementById('current-file-components').textContent = Object.keys(data.components_distribution || {}).length;
        document.getElementById('current-file-testsets').textContent = Object.keys(data.testsets_distribution || {}).length;

        // Mettre à jour les graphiques
        updateComponentsChart(data.components_distribution);
        updateTestSetsChart(data.testsets_distribution);
        updateRecentFiles(data.recent_files);
        updateTopLists(data.components_distribution, data.testsets_distribution);

    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        document.getElementById('current-file-info').textContent = 'Erreur de chargement';
    }
}

// Graphique des composants
function updateComponentsChart(components) {
    const ctx = document.getElementById('componentsChart').getContext('2d');
    
    if (componentsChart) {
        componentsChart.destroy();
    }
    
    const labels = Object.keys(components);
    const data = Object.values(components);
    const colors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
    ];
    
    componentsChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, labels.length),
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Graphique des Test Sets
function updateTestSetsChart(testSets) {
    const ctx = document.getElementById('testSetsChart').getContext('2d');

    if (testSetsChart) {
        testSetsChart.destroy();
    }

    if (!testSets || Object.keys(testSets).length === 0) {
        ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
        ctx.font = '16px Arial';
        ctx.fillStyle = '#666';
        ctx.textAlign = 'center';
        ctx.fillText('Aucun Test Set trouvé', ctx.canvas.width / 2, ctx.canvas.height / 2);
        return;
    }

    const labels = Object.keys(testSets);
    const data = Object.values(testSets);
    const colors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF',
        '#4BC0C0', '#FFCE56', '#FF9F40', '#9966FF'
    ];

    testSetsChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, labels.length),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

// Mettre à jour la liste des fichiers récents
function updateRecentFiles(history) {
    const tbody = document.getElementById('recent-files-body');
    
    if (history.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center text-muted">
                    Aucun fichier traité récemment
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = history.slice(0, 5).map(item => {
        const date = new Date(item.timestamp * 1000);
        const dateStr = date.toLocaleDateString('fr-FR') + ' ' + date.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});
        
        return `
            <tr>
                <td>
                    <i class="fas fa-file-csv text-success"></i>
                    ${item.filename}
                </td>
                <td>
                    <span class="badge bg-primary">${item.test_count}</span>
                </td>
                <td>
                    <small class="text-muted">${dateStr}</small>
                </td>
                <td>
                    <a href="/download/${item.filename}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-download"></i>
                    </a>
                </td>
            </tr>
        `;
    }).join('');
}

// Mettre à jour les listes top 5
function updateTopLists(components, testSets) {
    // Top 5 Composants
    const topComponentsDiv = document.getElementById('top-components');
    if (components && Object.keys(components).length > 0) {
        const sortedComponents = Object.entries(components)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);

        topComponentsDiv.innerHTML = sortedComponents.map(([name, count]) => `
            <div class="d-flex justify-content-between align-items-center mb-1">
                <span class="badge bg-primary">${name}</span>
                <small class="text-muted">${count} tests</small>
            </div>
        `).join('');
    } else {
        topComponentsDiv.innerHTML = '<small class="text-muted">Aucun composant trouvé</small>';
    }

    // Top 5 Test Sets
    const topTestSetsDiv = document.getElementById('top-testsets');
    if (testSets && Object.keys(testSets).length > 0) {
        const sortedTestSets = Object.entries(testSets)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);

        topTestSetsDiv.innerHTML = sortedTestSets.map(([name, count]) => `
            <div class="d-flex justify-content-between align-items-center mb-1">
                <span class="badge bg-success">${name}</span>
                <small class="text-muted">${count} tests</small>
            </div>
        `).join('');
    } else {
        topTestSetsDiv.innerHTML = '<small class="text-muted">Aucun Test Set trouvé</small>';
    }
}

// Actualiser le dashboard
function refreshDashboard() {
    const button = event.target;
    const icon = button.querySelector('i');

    icon.classList.add('fa-spin');
    button.disabled = true;

    loadDashboardData().finally(() => {
        icon.classList.remove('fa-spin');
        button.disabled = false;
    });
}

// Charger les données au chargement de la page
document.addEventListener('DOMContentLoaded', loadDashboardData);

// Actualiser automatiquement toutes les 30 secondes
setInterval(loadDashboardData, 30000);
</script>
{% endblock %}
