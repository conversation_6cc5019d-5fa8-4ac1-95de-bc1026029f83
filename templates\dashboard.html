{% extends "base.html" %}

{% block title %}Dashboard - Enrichissement Tests Jira Xray{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-tachometer-alt"></i> Dashboard - Vue d'Ensemble
                </h3>
            </div>
            <div class="card-body">
                
                <!-- Statistiques principales -->
                <div class="row mb-4" id="main-stats">
                    <div class="col-md-3">
                        <div class="card text-center border-primary">
                            <div class="card-body">
                                <i class="fas fa-file-csv fa-2x text-primary mb-2"></i>
                                <h3 class="text-primary" id="total-files">-</h3>
                                <p class="card-text">Fichiers Traités</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center border-success">
                            <div class="card-body">
                                <i class="fas fa-list-check fa-2x text-success mb-2"></i>
                                <h3 class="text-success" id="total-tests">-</h3>
                                <p class="card-text">Tests Enrichis</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center border-info">
                            <div class="card-body">
                                <i class="fas fa-sitemap fa-2x text-info mb-2"></i>
                                <h3 class="text-info" id="total-components">-</h3>
                                <p class="card-text">Composants</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center border-warning">
                            <div class="card-body">
                                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                <h3 class="text-warning" id="last-activity">-</h3>
                                <p class="card-text">Dernière Activité</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Graphiques -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie"></i> Répartition par Composants
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="componentsChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-line"></i> Activité de Traitement
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="activityChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fichiers récents -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clock"></i> Fichiers Récemment Traités
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="recent-files-table">
                                <thead>
                                    <tr>
                                        <th>Fichier</th>
                                        <th>Tests</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="recent-files-body">
                                    <tr>
                                        <td colspan="4" class="text-center">
                                            <i class="fas fa-spinner fa-spin"></i> Chargement...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Actions rapides -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-bolt"></i> Actions Rapides
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <a href="{{ url_for('index') }}" class="btn btn-primary btn-lg w-100 mb-2">
                                            <i class="fas fa-upload"></i><br>
                                            Nouveau Fichier
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="{{ url_for('show_history') }}" class="btn btn-info btn-lg w-100 mb-2">
                                            <i class="fas fa-history"></i><br>
                                            Historique
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="{{ url_for('show_rules') }}" class="btn btn-warning btn-lg w-100 mb-2">
                                            <i class="fas fa-list-ul"></i><br>
                                            Règles
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <button onclick="refreshDashboard()" class="btn btn-success btn-lg w-100 mb-2">
                                            <i class="fas fa-sync-alt"></i><br>
                                            Actualiser
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let componentsChart, activityChart;

// Charger les données du dashboard
async function loadDashboardData() {
    try {
        const response = await fetch('/api/stats');
        const data = await response.json();
        
        // Mettre à jour les statistiques principales
        document.getElementById('total-files').textContent = data.total_files_processed;
        document.getElementById('total-tests').textContent = data.total_tests_enriched;
        document.getElementById('total-components').textContent = Object.keys(data.most_common_components).length;
        
        // Dernière activité
        if (data.processing_history.length > 0) {
            const lastActivity = new Date(data.processing_history[0].timestamp * 1000);
            const now = new Date();
            const diffHours = Math.floor((now - lastActivity) / (1000 * 60 * 60));
            
            if (diffHours < 1) {
                document.getElementById('last-activity').textContent = 'Maintenant';
            } else if (diffHours < 24) {
                document.getElementById('last-activity').textContent = diffHours + 'h';
            } else {
                document.getElementById('last-activity').textContent = Math.floor(diffHours / 24) + 'j';
            }
        } else {
            document.getElementById('last-activity').textContent = 'Jamais';
        }
        
        // Mettre à jour les graphiques
        updateComponentsChart(data.most_common_components);
        updateActivityChart(data.processing_history);
        updateRecentFiles(data.processing_history);
        
    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
    }
}

// Graphique des composants
function updateComponentsChart(components) {
    const ctx = document.getElementById('componentsChart').getContext('2d');
    
    if (componentsChart) {
        componentsChart.destroy();
    }
    
    const labels = Object.keys(components);
    const data = Object.values(components);
    const colors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
    ];
    
    componentsChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, labels.length),
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Graphique d'activité
function updateActivityChart(history) {
    const ctx = document.getElementById('activityChart').getContext('2d');
    
    if (activityChart) {
        activityChart.destroy();
    }
    
    const labels = history.slice(0, 10).reverse().map(item => {
        const date = new Date(item.timestamp * 1000);
        return date.toLocaleDateString('fr-FR', { month: 'short', day: 'numeric' });
    });
    
    const data = history.slice(0, 10).reverse().map(item => item.test_count);
    
    activityChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Tests enrichis',
                data: data,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// Mettre à jour la liste des fichiers récents
function updateRecentFiles(history) {
    const tbody = document.getElementById('recent-files-body');
    
    if (history.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center text-muted">
                    Aucun fichier traité récemment
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = history.slice(0, 5).map(item => {
        const date = new Date(item.timestamp * 1000);
        const dateStr = date.toLocaleDateString('fr-FR') + ' ' + date.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});
        
        return `
            <tr>
                <td>
                    <i class="fas fa-file-csv text-success"></i>
                    ${item.filename}
                </td>
                <td>
                    <span class="badge bg-primary">${item.test_count}</span>
                </td>
                <td>
                    <small class="text-muted">${dateStr}</small>
                </td>
                <td>
                    <a href="/download/${item.filename}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-download"></i>
                    </a>
                </td>
            </tr>
        `;
    }).join('');
}

// Actualiser le dashboard
function refreshDashboard() {
    const button = event.target;
    const icon = button.querySelector('i');
    
    icon.classList.add('fa-spin');
    button.disabled = true;
    
    loadDashboardData().finally(() => {
        icon.classList.remove('fa-spin');
        button.disabled = false;
    });
}

// Charger les données au chargement de la page
document.addEventListener('DOMContentLoaded', loadDashboardData);

// Actualiser automatiquement toutes les 30 secondes
setInterval(loadDashboardData, 30000);
</script>
{% endblock %}
