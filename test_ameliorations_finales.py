#!/usr/bin/env python3
"""
Test des améliorations finales :
1. Séparateur point-virgule (;) dans les CSV
2. Support des multiples steps avec duplication des lignes
"""

import pandas as pd
import os
from json_steps_processor import JSONStepsProcessor
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced

def print_header(title):
    print("\n" + "="*70)
    print(f"  {title}")
    print("="*70)

def test_separateur_point_virgule():
    """
    Test que les fichiers CSV utilisent le séparateur point-virgule
    """
    print_header("🔧 TEST SÉPARATEUR POINT-VIRGULE")
    
    # Test 1 : Extraction JSON
    processor = JSONStepsProcessor()
    processor.process_csv_file('example_multiple_steps.csv', 'test_sep_json.csv')
    
    # Vérifier le séparateur dans le fichier JSON
    with open('test_sep_json.csv', 'r', encoding='utf-8') as f:
        content = f.read()
        header_line = f.readline() if f.tell() == 0 else content.split('\n')[0]
    
    with open('test_sep_json.csv', 'r', encoding='utf-8') as f:
        header_line = f.readline()
    
    semicolon_count = header_line.count(';')
    comma_count = header_line.count(',')
    
    print(f"📁 Fichier extraction JSON :")
    print(f"   Point-virgules dans l'en-tête : {semicolon_count}")
    print(f"   Virgules dans l'en-tête : {comma_count}")
    print(f"   Séparateur principal : {'✅ Point-virgule' if semicolon_count > comma_count else '❌ Virgule'}")
    
    # Test 2 : Enrichissement
    tool = TestEnrichmentToolEnhanced()
    tool.enrich_csv_enhanced('test_sep_json.csv', 'test_sep_enriched.csv', 'separate')
    
    # Vérifier le séparateur dans le fichier enrichi
    with open('test_sep_enriched.csv', 'r', encoding='utf-8') as f:
        header_line_enriched = f.readline()
    
    semicolon_count_enriched = header_line_enriched.count(';')
    comma_count_enriched = header_line_enriched.count(',')
    
    print(f"\n📁 Fichier enrichi :")
    print(f"   Point-virgules dans l'en-tête : {semicolon_count_enriched}")
    print(f"   Virgules dans l'en-tête : {comma_count_enriched}")
    print(f"   Séparateur principal : {'✅ Point-virgule' if semicolon_count_enriched > comma_count_enriched else '❌ Virgule'}")
    
    # Nettoyer
    os.remove('test_sep_json.csv')
    os.remove('test_sep_enriched.csv')
    
    return semicolon_count > comma_count and semicolon_count_enriched > comma_count_enriched

def test_multiples_steps():
    """
    Test du support des multiples steps avec duplication des lignes
    """
    print_header("📋 TEST MULTIPLES STEPS")
    
    # Lire le fichier original
    df_original = pd.read_csv('example_multiple_steps.csv')
    original_count = len(df_original)
    
    print(f"📊 Fichier original :")
    print(f"   Lignes : {original_count}")
    
    # Analyser le JSON pour compter les steps attendus
    expected_total_steps = 0
    for index, row in df_original.iterrows():
        json_content = row['Custom field (Manual Test Steps)']
        if pd.notna(json_content):
            # Compter les occurrences de "index" dans le JSON
            step_count = json_content.count('"index":')
            expected_total_steps += step_count
            print(f"   Test '{row['Summary']}' : {step_count} steps")
    
    print(f"   Total steps attendus : {expected_total_steps}")
    
    # Traitement avec extraction JSON
    processor = JSONStepsProcessor()
    stats = processor.process_csv_file('example_multiple_steps.csv', 'test_multiples_output.csv')
    
    print(f"\n🔧 Résultats extraction :")
    print(f"   Tests traités : {stats['processed']}")
    print(f"   Steps extraits : {stats['extracted']}")
    print(f"   Lignes dupliquées : {stats['duplicated_rows']}")
    
    # Lire le fichier de sortie
    df_output = pd.read_csv('test_multiples_output.csv', sep=';', quoting=1, engine='python')
    output_count = len(df_output)
    
    print(f"\n📊 Fichier de sortie :")
    print(f"   Lignes finales : {output_count}")
    print(f"   Duplication réussie : {'✅ OUI' if output_count == expected_total_steps else '❌ NON'}")
    
    # Vérifier que chaque ligne a bien ses colonnes Action, Data, Expected Result
    print(f"\n🔍 Vérification des colonnes extraites :")
    required_columns = ['Action', 'Data', 'Expected Result']
    for col in required_columns:
        if col in df_output.columns:
            non_empty_count = df_output[col].notna().sum()
            print(f"   {col} : ✅ présente ({non_empty_count} valeurs non vides)")
        else:
            print(f"   {col} : ❌ manquante")
    
    # Afficher quelques exemples
    print(f"\n📋 Exemples de lignes dupliquées :")
    for i in range(min(4, len(df_output))):
        row = df_output.iloc[i]
        print(f"   {i+1}. {row['Summary']} - {row['Action']}")
    
    # Nettoyer
    os.remove('test_multiples_output.csv')
    
    return output_count == expected_total_steps and all(col in df_output.columns for col in required_columns)

def test_workflow_complet():
    """
    Test du workflow complet avec les deux améliorations
    """
    print_header("🚀 TEST WORKFLOW COMPLET")
    
    print("📁 Étape 1 : Extraction JSON avec multiples steps")
    processor = JSONStepsProcessor()
    stats = processor.process_csv_file('example_multiple_steps.csv', 'test_workflow_steps.csv')
    
    print(f"   Lignes originales : 4")
    print(f"   Lignes après extraction : {stats['extracted']}")
    print(f"   Lignes dupliquées : {stats['duplicated_rows']}")
    
    print(f"\n🎯 Étape 2 : Enrichissement avec séparateur point-virgule")
    tool = TestEnrichmentToolEnhanced()
    tool.enrich_csv_enhanced('test_workflow_steps.csv', 'test_workflow_final.csv', 'separate')
    
    # Vérifier le fichier final
    print(f"\n📊 Vérification du fichier final :")
    
    # Vérifier le séparateur
    with open('test_workflow_final.csv', 'r', encoding='utf-8') as f:
        header_line = f.readline()
    
    semicolon_count = header_line.count(';')
    print(f"   Séparateur point-virgule : {'✅ OUI' if semicolon_count > 5 else '❌ NON'}")
    
    # Lire le contenu
    try:
        df_final = pd.read_csv('test_workflow_final.csv', sep=';', quoting=1, engine='python')
        print(f"   Lignes finales : {len(df_final)}")
        print(f"   Colonnes totales : {len(df_final.columns)}")
        
        # Vérifier les colonnes essentielles
        essential_columns = ['Summary', 'Action', 'Data', 'Expected Result', 'Component/s', 'Test Set']
        missing_columns = [col for col in essential_columns if col not in df_final.columns]
        
        if not missing_columns:
            print(f"   Colonnes essentielles : ✅ Toutes présentes")
        else:
            print(f"   Colonnes manquantes : ❌ {missing_columns}")
        
        # Compter les colonnes Labels
        labels_columns = [col for col in df_final.columns if col == 'Labels']
        print(f"   Colonnes Labels identiques : {len(labels_columns)}")
        
        # Vérifier qu'il n'y a pas de valeurs "nan"
        with open('test_workflow_final.csv', 'r', encoding='utf-8') as f:
            content = f.read()
        
        nan_count = content.lower().count('nan')
        print(f"   Valeurs 'nan' indésirables : {nan_count}")
        
        success = (
            semicolon_count > 5 and
            len(missing_columns) == 0 and
            len(labels_columns) >= 8 and
            nan_count == 0
        )
        
    except Exception as e:
        print(f"   ❌ Erreur de lecture : {e}")
        success = False
    
    # Nettoyer
    os.remove('test_workflow_steps.csv')
    os.remove('test_workflow_final.csv')
    
    return success

def main():
    """
    Test complet des améliorations finales
    """
    print("🧪 TEST DES AMÉLIORATIONS FINALES")
    
    # Test 1 : Séparateur point-virgule
    test1 = test_separateur_point_virgule()
    
    # Test 2 : Multiples steps
    test2 = test_multiples_steps()
    
    # Test 3 : Workflow complet
    test3 = test_workflow_complet()
    
    print_header("📊 RÉSULTATS FINAUX")
    
    print(f"✅ Séparateur point-virgule : {'OK' if test1 else 'ÉCHEC'}")
    print(f"✅ Multiples steps : {'OK' if test2 else 'ÉCHEC'}")
    print(f"✅ Workflow complet : {'OK' if test3 else 'ÉCHEC'}")
    
    if test1 and test2 and test3:
        print(f"\n🎉 TOUTES LES AMÉLIORATIONS FONCTIONNENT !")
        print(f"✅ Séparateur point-virgule (;) dans tous les CSV")
        print(f"✅ Support des multiples steps avec duplication")
        print(f"✅ Workflow complet fonctionnel")
        print(f"✅ Format final parfait pour import")
        print(f"🎯 Prêt pour utilisation en production")
    else:
        print(f"\n⚠️  CERTAINES AMÉLIORATIONS NÉCESSITENT DES AJUSTEMENTS")
        print(f"💡 Vérifiez les erreurs ci-dessus")

if __name__ == "__main__":
    main()
