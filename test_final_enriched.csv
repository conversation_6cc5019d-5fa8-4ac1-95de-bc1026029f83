Summary;Description;Component/s;Custom field (Manual Test Steps);Action;Data;Expected Result;Test Set;Labels;Labels;Labels;Labels;Labels;Labels;Labels;Labels
Subscriber Query/Export;Test subscriber export functionality;COMMON;"[{""id"":3665376,""index"":1,""fields"":{""Action"":""Export/Query Subscribers"",""Data"":"""",""Expected Result"":""Verify if subscribers are recorded correctly""}},{""id"":3665377,""index"":2,""fields"":{""Action"":""Verify if can be exported automatically to an external SFTP server"",""Data"":"""",""Expected Result"":""The subscribers are on the external server""}}]";Export/Query Subscribers;;Verify if subscribers are recorded correctly;General Tests;Functional;System;Manual;Regression;;;;
Subscriber Query/Export;Test subscriber export functionality;HLR;"[{""id"":3665376,""index"":1,""fields"":{""Action"":""Export/Query Subscribers"",""Data"":"""",""Expected Result"":""Verify if subscribers are recorded correctly""}},{""id"":3665377,""index"":2,""fields"":{""Action"":""Verify if can be exported automatically to an external SFTP server"",""Data"":"""",""Expected Result"":""The subscribers are on the external server""}}]";Verify if can be exported automatically to an external SFTP server;;The subscribers are on the external server;HLR Location Management;Functional;System;Manual;Regression;2G;MAP;LocationUpdate;3GPP_TS_23.012
Schedule Backup;Test backup scheduling;HLR;"[{""id"":3665370,""index"":1,""fields"":{""Action"":""Schedule an automatic backup and configure to export it to an external server"",""Data"":"""",""Expected Result"":""Verify if backup are on the external server""}}]";Schedule an automatic backup and configure to export it to an external server;;Verify if backup are on the external server;HLR Location Management;Functional;System;Manual;Regression;2G;MAP;LocationUpdate;3GPP_TS_23.012
Trace function;Test trace functionality;COMMON;"[{""id"":3665362,""index"":1,""fields"":{""Action"":""Verify that a trace task can be created, stopped and deleted for protocol"",""Data"":"""",""Expected Result"":""Trace task are created, can be stopped and deleted""}},{""id"":3665363,""index"":2,""fields"":{""Action"":""Verify that a trace task can be created, stopped and deleted for mobile user"",""Data"":"""",""Expected Result"":""Trace task are created, can be stopped and deleted""}}]";Verify that a trace task can be created, stopped and deleted for protocol;;Trace task are created, can be stopped and deleted;High Availability;NonFunctional;System;Manual;Regression;Availability;3GPP_TS_23.007;;
Trace function;Test trace functionality;COMMON;"[{""id"":3665362,""index"":1,""fields"":{""Action"":""Verify that a trace task can be created, stopped and deleted for protocol"",""Data"":"""",""Expected Result"":""Trace task are created, can be stopped and deleted""}},{""id"":3665363,""index"":2,""fields"":{""Action"":""Verify that a trace task can be created, stopped and deleted for mobile user"",""Data"":"""",""Expected Result"":""Trace task are created, can be stopped and deleted""}}]";Verify that a trace task can be created, stopped and deleted for mobile user;;Trace task are created, can be stopped and deleted;High Availability;NonFunctional;System;Manual;Regression;Availability;3GPP_TS_23.007;;
Online loading;Test online loading;COMMON;"[{""id"":3665359,""index"":1,""fields"":{""Action"":""Verify that the upgrade package can be loaded into the SDM without SDM system interruption"",""Data"":"""",""Expected Result"":""The upgrade package is loaded with success""}}]";Verify that the upgrade package can be loaded into the SDM without SDM system interruption;;The upgrade package is loaded with success;Performance Load Testing;NonFunctional;System;Manual;Regression;Performance;Load;3GPP_TS_32.401;
