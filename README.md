# Outil d'Enrichissement Automatique pour Tests Jira Xray

Cet outil applique automatiquement les règles de labellisation selon les standards ISTQB et les spécificités télécoms pour enrichir vos tests Jira Xray.

## Fonctionnalités

L'outil analyse automatiquement le contenu de vos tests (Summary, Description, Action, Expected Result) et applique :

- **Labels appropriés** selon le domaine technique
- **Composants** corrects (HLR, EPC-HSS, IMS-HSS, OAM, Security, etc.)
- **Test Sets** organisés par fonctionnalité

## Règles de Labellisation

### HLR (2G/3G)
- **Patterns détectés** : call forwarding, call barring, supplementary, camel, hlr, map, sri, ati
- **Labels** : Functional, System, Manual, Regression, 2G, MAP
- **Test Sets** : HLR Call Forwarding Services, HLR Call Barring Services, HLR Supplementary Services, HLR CAMEL Services

### EPC-HSS (4G)
- **Patterns détectés** : attach, detach, update location, authentication, hss, epc, s6a, diameter, ulr, air
- **Labels** : Functional, System, Manual, Regression, 4G, Diameter, S6a
- **Test Sets** : EPC HSS Attach/Detach Procedures, EPC HSS Location Management, EPC HSS Authentication Services

### IMS (VoLTE)
- **Patterns détectés** : volte, ims, t-ads, srvcc, swx, sh, cx, scc-as
- **Labels** : Functional, System, Manual, Regression, 4G, IMS
- **Test Sets** : IMS VoLTE Services, IMS T-ADS Services, IMS SRVCC Services

### OAM/Provisioning
- **Patterns détectés** : provisioning, oam, soap, mml, configuration, management
- **Labels** : Functional, System, Manual, Regression, OAM
- **Test Sets** : OAM and Provisioning

### Sécurité
- **Patterns détectés** : cis, vulnerability, rbac, security, hardening
- **Labels** : NonFunctional, System, Manual, Regression, Security
- **Test Sets** : Security CIS Compliance, Security Vulnerability Assessment, Security RBAC Management

### Backup/Restore
- **Patterns détectés** : backup, restore, recovery
- **Labels** : NonFunctional, System, Manual, Regression, BackupRestore
- **Test Sets** : Backup and Restore Operations

### Performance/Résilience
- **Patterns détectés** : performance, resiliency, redundancy, vnf, lcm, scaling
- **Labels** : NonFunctional, System, Manual, Regression, Performance/Resiliency/VNF
- **Test Sets** : Performance Testing, Resiliency and Redundancy, VNF Lifecycle Management

## Utilisation

### 1. Enrichissement automatique

```bash
python test_enrichment_tool.py input_file.csv output_file.csv
```

### 2. Création d'un fichier Excel

```bash
python create_excel_output.py output_file.csv JIRA_Tests_Enriched.xlsx
```

### 3. Affichage d'un échantillon des résultats

```bash
python sample_results.py output_file.csv
```

## Exemple de Résultat

### Avant enrichissement :
```
Summary: CFU
Component/s: 
Test Set: 
Labels: 
```

### Après enrichissement :
```
Summary: CFU
Component/s: HLR
Test Set: HLR Call Forwarding Services
Labels: Functional, System, Manual, Regression, 2G, MAP, CallForwarding
```

## Structure des Fichiers de Sortie

### CSV enrichi
- Toutes les colonnes originales
- Nouvelle colonne **Labels** avec les labels appropriés
- Nouvelle colonne **Test Set** avec le regroupement fonctionnel
- Colonne **Component/s** mise à jour si nécessaire

### Fichier Excel (4 feuilles)
1. **Tests Enrichis** : Tous les tests avec les nouvelles colonnes
2. **Exemples par Composant** : Exemples représentatifs par domaine
3. **Statistiques** : Répartition par composant, type et test set
4. **Règles de Labellisation** : Documentation des règles appliquées

## Statistiques d'Enrichissement

L'outil fournit automatiquement :
- Nombre total de tests traités
- Répartition par composant (HLR, EPC-HSS, IMS-HSS, etc.)
- Répartition par type (Functional vs NonFunctional)
- Top 10 des Test Sets les plus utilisés

## Prérequis

```bash
pip install pandas openpyxl
```

## Personnalisation

Pour adapter l'outil à vos besoins spécifiques, modifiez les patterns dans `test_enrichment_tool.py` :

```python
self.hlr_patterns = [
    r'call\s*forward', r'call\s*barr', r'supplementary', 
    # Ajoutez vos patterns personnalisés ici
]
```

## Support

L'outil supporte :
- ✅ Fichiers CSV avec séparateur virgule
- ✅ Encodage UTF-8
- ✅ Gestion des caractères spéciaux
- ✅ Analyse multi-critères (Summary + Description + Action + Expected Result)
- ✅ Préservation de toutes les colonnes originales
