#!/usr/bin/env python3
"""
Test rapide pour vérifier que le format séparé fonctionne
"""

import pandas as pd
import os
from test_enrichment_tool_v2 import TestEnrichmentToolV2

def test_separate_format():
    """
    Test le format avec colonnes séparées
    """
    print("🔧 Test du format avec colonnes séparées...")
    
    # Utiliser le fichier d'exemple existant
    if not os.path.exists('example_tests.csv'):
        print("❌ Fichier example_tests.csv non trouvé")
        return False
    
    # Enrichir avec la version V2 (colonnes séparées)
    tool = TestEnrichmentToolV2()
    output_file = 'test_format_separate.csv'
    
    try:
        tool.enrich_csv_with_separate_labels('example_tests.csv', output_file)
        
        # Vérifier le résultat
        df = pd.read_csv(output_file)
        
        # Compter les colonnes Labels
        label_columns = [col for col in df.columns if col == 'Labels']
        print(f"✅ Colonnes Labels créées : {len(label_columns)}")
        
        # Vérifier le contenu du premier test
        first_test = df.iloc[0]
        labels_found = []
        for i, col in enumerate(df.columns):
            if col == 'Labels':
                value = df.iloc[0, i]
                if pd.notna(value) and value != '':
                    labels_found.append(value)
        
        print(f"✅ Premier test - Labels trouvés : {labels_found}")
        print(f"✅ Composant : {first_test.get('Component/s', 'N/A')}")
        print(f"✅ Test Set : {first_test.get('Test Set', 'N/A')}")
        
        # Nettoyer
        os.remove(output_file)
        
        return len(label_columns) > 1 and len(labels_found) > 0
        
    except Exception as e:
        print(f"❌ Erreur : {e}")
        return False

def test_analyzer():
    """
    Test l'analyseur de Test Sets
    """
    print("\n🔍 Test de l'analyseur de Test Sets...")
    
    if not os.path.exists('example_tests_separate_labels.csv'):
        print("❌ Fichier example_tests_separate_labels.csv non trouvé")
        return False
    
    try:
        from test_set_analyzer import TestSetAnalyzer
        
        analyzer = TestSetAnalyzer()
        analysis = analyzer.analyze_test_file('example_tests_separate_labels.csv')
        
        print(f"✅ Tests analysés : {analysis['total_tests']}")
        print(f"✅ Tests mal classés : {len(analysis['misclassified_tests'])}")
        print(f"✅ Labels manquants : {len(analysis['missing_labels'])}")
        print(f"✅ Références 3GPP manquantes : {len(analysis['missing_3gpp_refs'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur : {e}")
        return False

def test_corrector():
    """
    Test le correcteur automatique
    """
    print("\n🔧 Test du correcteur automatique...")
    
    if not os.path.exists('example_tests_separate_labels.csv'):
        print("❌ Fichier example_tests_separate_labels.csv non trouvé")
        return False
    
    try:
        from test_set_corrector import TestSetCorrector
        
        corrector = TestSetCorrector()
        corrections = corrector.correct_test_file(
            'example_tests_separate_labels.csv',
            'test_corrected.csv',
            apply_corrections=True
        )
        
        print(f"✅ Test Sets modifiés : {corrections['test_set_changes']}")
        print(f"✅ Labels ajoutés : {corrections['labels_added']}")
        print(f"✅ Références 3GPP ajoutées : {corrections['gpp_refs_added']}")
        
        # Vérifier le fichier corrigé
        if os.path.exists('test_corrected.csv'):
            df = pd.read_csv('test_corrected.csv')
            print(f"✅ Fichier corrigé créé avec {len(df)} tests")
            
            # Nettoyer
            os.remove('test_corrected.csv')
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur : {e}")
        return False

if __name__ == "__main__":
    print("🧪 TEST DES CORRECTIONS ET NOUVELLES FONCTIONNALITÉS")
    
    # Test 1 : Format séparé
    test1 = test_separate_format()
    
    # Test 2 : Analyseur
    test2 = test_analyzer()
    
    # Test 3 : Correcteur
    test3 = test_corrector()
    
    print(f"\n📊 RÉSULTATS :")
    print(f"   Format séparé : {'✅ OK' if test1 else '❌ ÉCHEC'}")
    print(f"   Analyseur : {'✅ OK' if test2 else '❌ ÉCHEC'}")
    print(f"   Correcteur : {'✅ OK' if test3 else '❌ ÉCHEC'}")
    
    if all([test1, test2, test3]):
        print(f"\n🎉 TOUS LES TESTS PASSENT !")
        print(f"💡 Les nouvelles fonctionnalités sont opérationnelles")
    else:
        print(f"\n⚠️  CERTAINS TESTS ÉCHOUENT")
        print(f"💡 Vérifiez les erreurs ci-dessus")
