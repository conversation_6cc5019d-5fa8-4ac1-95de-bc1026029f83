#!/usr/bin/env python3
"""
Version améliorée de l'outil d'enrichissement avec colonnes de labels séparées
"""

import pandas as pd
import re
import sys
from typing import List, Tuple
from test_enrichment_tool import TestEnrichmentTool

class TestEnrichmentToolV2(TestEnrichmentTool):
    """
    Version améliorée avec colonnes de labels séparées
    """
    
    def enrich_csv_with_separate_labels(self, input_file: str, output_file: str, progress_callback=None):
        """
        Enrichit le fichier CSV avec des colonnes de labels séparées
        """
        try:
            # Lecture du fichier CSV
            df = pd.read_csv(input_file)
            
            print(f"Lecture de {len(df)} lignes depuis {input_file}")
            
            # Première passe : collecter tous les labels pour déterminer le nombre de colonnes
            all_labels_per_test = []
            total_rows = len(df)
            
            print("Première passe : analyse des labels...")
            for index, row in df.iterrows():
                summary = str(row.get('Summary', ''))
                description = str(row.get('Description', ''))
                action = str(row.get('Action', ''))
                expected_result = str(row.get('Expected Result', ''))
                test_path = str(row.get('Custom field (Test Repository Path)', ''))
                
                # Analyse et enrichissement
                labels, component, test_set = self.analyze_test_content(
                    summary, description, action, expected_result, test_path
                )
                
                all_labels_per_test.append({
                    'labels': labels,
                    'component': component,
                    'test_set': test_set
                })
            
            # Déterminer le nombre maximum de labels
            max_labels = max(len(item['labels']) for item in all_labels_per_test) if all_labels_per_test else 0
            print(f"Nombre maximum de labels par test : {max_labels}")
            
            # Créer les nouvelles colonnes
            # D'abord, ajouter la colonne Test Set
            df['Test Set'] = ''
            
            # Ensuite, créer les colonnes de labels
            # On va créer un DataFrame temporaire avec les bonnes colonnes
            new_columns = list(df.columns)
            
            # Ajouter les colonnes Labels (toutes avec le même nom)
            for i in range(max_labels):
                new_columns.append('Labels')
            
            # Créer un nouveau DataFrame avec les bonnes colonnes
            df_new = pd.DataFrame(columns=new_columns)
            
            # Copier les données existantes
            for col in df.columns:
                df_new[col] = df[col]
            
            # Remplir les nouvelles colonnes Labels avec des valeurs vides
            label_col_indices = [i for i, col in enumerate(new_columns) if col == 'Labels']
            for col_idx in label_col_indices:
                df_new.iloc[:, col_idx] = ''
            
            print("Deuxième passe : enrichissement des données...")
            
            # Remplir les données
            for index, test_data in enumerate(all_labels_per_test):
                # Mettre à jour le composant si nécessaire
                current_component = str(df_new.at[index, 'Component/s'])
                if not current_component or current_component in ['', 'nan', 'COMMON']:
                    df_new.at[index, 'Component/s'] = test_data['component']
                
                # Mettre à jour le Test Set
                df_new.at[index, 'Test Set'] = test_data['test_set']
                
                # Remplir les colonnes de labels
                labels = test_data['labels']
                for i, label in enumerate(labels):
                    if i < len(label_col_indices):
                        col_idx = label_col_indices[i]
                        df_new.iloc[index, col_idx] = label
                
                # Callback de progression
                if progress_callback:
                    progress = int((index + 1) / total_rows * 100)
                    progress_callback(progress, index + 1, total_rows)
                
                # Affichage du progrès
                if (index + 1) % 50 == 0 or (index + 1) == total_rows:
                    print(f"Traité {index + 1}/{total_rows} lignes ({int((index + 1) / total_rows * 100)}%)")
            
            # Sauvegarde du fichier enrichi
            df_new.to_csv(output_file, index=False)
            print(f"Fichier enrichi sauvegardé : {output_file}")
            print(f"Colonnes Labels créées : {max_labels}")
            
            # Statistiques
            self.print_statistics_v2(df_new, max_labels)
            
        except Exception as e:
            print(f"Erreur lors du traitement : {e}")
            import traceback
            traceback.print_exc()
            if progress_callback:
                progress_callback(-1, 0, 0)  # Signal d'erreur
            sys.exit(1)
    
    def print_statistics_v2(self, df: pd.DataFrame, max_labels: int):
        """
        Affiche des statistiques sur l'enrichissement avec colonnes séparées
        """
        print("\n=== STATISTIQUES D'ENRICHISSEMENT ===")
        print(f"Total de tests traités : {len(df)}")
        print(f"Nombre de colonnes Labels : {max_labels}")
        
        # Répartition par composant
        print("\nRépartition par composant :")
        component_counts = df['Component/s'].value_counts()
        for component, count in component_counts.items():
            print(f"  {component}: {count}")
        
        # Collecter tous les labels de toutes les colonnes Labels
        all_labels = []

        # Parcourir toutes les colonnes pour trouver celles nommées 'Labels'
        for col_idx, col_name in enumerate(df.columns):
            if col_name == 'Labels':
                labels_in_col = df.iloc[:, col_idx].dropna()
                labels_in_col = labels_in_col[labels_in_col != '']
                all_labels.extend(labels_in_col.tolist())
        
        # Compter les labels
        from collections import Counter
        label_counts = Counter(all_labels)
        
        print("\nTop 10 des labels les plus utilisés :")
        for label, count in label_counts.most_common(10):
            print(f"  {label}: {count}")
        
        # Statistiques sur la distribution des labels
        labels_per_test = []
        for index, row in df.iterrows():
            test_labels = 0
            for col_idx, col_name in enumerate(df.columns):
                if col_name == 'Labels':
                    if pd.notna(row.iloc[col_idx]) and row.iloc[col_idx] != '':
                        test_labels += 1
            labels_per_test.append(test_labels)
        
        print(f"\nDistribution des labels par test :")
        print(f"  Moyenne: {sum(labels_per_test)/len(labels_per_test):.1f} labels par test")
        print(f"  Minimum: {min(labels_per_test)} labels")
        print(f"  Maximum: {max(labels_per_test)} labels")

def main():
    """
    Fonction principale pour tester la nouvelle version
    """
    if len(sys.argv) != 3:
        print("Usage: python test_enrichment_tool_v2.py <input_file.csv> <output_file.csv>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    print("=== OUTIL D'ENRICHISSEMENT V2 - COLONNES LABELS SÉPARÉES ===")
    
    tool = TestEnrichmentToolV2()
    tool.enrich_csv_with_separate_labels(input_file, output_file)
    
    print("\n✅ Enrichissement terminé avec succès !")
    print(f"📁 Fichier de sortie : {output_file}")

if __name__ == "__main__":
    main()
