#!/usr/bin/env python3
"""
Application web Flask pour l'enrichissement automatique de tests Jira Xray
"""

from flask import Flask, render_template, request, redirect, url_for, flash, send_file, jsonify
import pandas as pd
import os
import tempfile
from datetime import datetime
from werkzeug.utils import secure_filename
import uuid
from test_enrichment_tool import TestEnrichmentTool
from test_enrichment_tool_v2 import TestEnrichmentToolV2
from test_enrichment_tool_enhanced import TestEnrichmentToolEnhanced
from test_set_csv_generator import TestSetCSVGenerator
from json_steps_processor import JSONStepsProcessor
from async_processor import async_processor, optimize_processing_strategy
from export_formats import export_manager
import traceback

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Configuration des dossiers
UPLOAD_FOLDER = 'uploads'
PROCESSED_FOLDER = 'processed'
ALLOWED_EXTENSIONS = {'csv'}

# Créer les dossiers s'ils n'existent pas
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(PROCESSED_FOLDER, exist_ok=True)

def allowed_file(filename):
    """Vérifie si le fichier a une extension autorisée"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_csv_format(filepath):
    """
    Valide le format du fichier CSV
    Retourne (is_valid, message, column_info)
    """
    try:
        # Lecture du fichier CSV
        df = pd.read_csv(filepath)
        
        # Vérifications de base
        if df.empty:
            return False, "Le fichier CSV est vide", None
        
        # Colonnes requises minimales
        required_columns = ['Summary']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            return False, f"Colonnes manquantes: {', '.join(missing_columns)}", None
        
        # Informations sur les colonnes
        column_info = {
            'total_rows': len(df),
            'total_columns': len(df.columns),
            'columns': list(df.columns),
            'has_summary': 'Summary' in df.columns,
            'has_description': 'Description' in df.columns,
            'has_component': 'Component/s' in df.columns,
            'has_action': 'Action' in df.columns,
            'has_expected_result': 'Expected Result' in df.columns
        }
        
        return True, "Format CSV valide", column_info
        
    except Exception as e:
        return False, f"Erreur lors de la lecture du fichier: {str(e)}", None

@app.route('/')
def index():
    """Page d'accueil"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Gestion du téléchargement de fichier"""
    if 'file' not in request.files:
        flash('Aucun fichier sélectionné', 'error')
        return redirect(url_for('index'))
    
    file = request.files['file']
    if file.filename == '':
        flash('Aucun fichier sélectionné', 'error')
        return redirect(url_for('index'))
    
    if file and allowed_file(file.filename):
        # Générer un nom de fichier unique
        file_id = str(uuid.uuid4())
        filename = secure_filename(file.filename)
        filepath = os.path.join(UPLOAD_FOLDER, f"{file_id}_{filename}")
        file.save(filepath)
        
        # Valider le format
        is_valid, message, column_info = validate_csv_format(filepath)
        
        if is_valid:
            return render_template('validate.html', 
                                 file_id=file_id, 
                                 filename=filename,
                                 message=message,
                                 column_info=column_info)
        else:
            # Supprimer le fichier invalide
            os.remove(filepath)
            flash(f'Format de fichier invalide: {message}', 'error')
            return redirect(url_for('index'))
    else:
        flash('Type de fichier non autorisé. Seuls les fichiers CSV sont acceptés.', 'error')
        return redirect(url_for('index'))

@app.route('/enrich/<file_id>')
def enrich_file(file_id):
    """Page d'enrichissement"""
    # Vérifier si on vient du traitement JSON
    source = request.args.get('source', 'original')

    if source == 'with_steps':
        # Chercher le fichier avec étapes JSON extraites
        target_filename = f"with_steps_{file_id}.csv"
        if os.path.exists(os.path.join(UPLOAD_FOLDER, target_filename)):
            filename = target_filename
        else:
            flash('Fichier avec étapes JSON non trouvé', 'error')
            return redirect(url_for('index'))
    else:
        # Trouver le fichier original
        uploaded_files = [f for f in os.listdir(UPLOAD_FOLDER) if f.startswith(file_id)]
        if not uploaded_files:
            flash('Fichier non trouvé', 'error')
            return redirect(url_for('index'))
        filename = uploaded_files[0].replace(f"{file_id}_", "")

    format_type = request.args.get('format', 'standard')  # standard ou separate
    return render_template('enrich.html', file_id=file_id, filename=filename, format_type=format_type, source=source)

@app.route('/process_json_steps/<file_id>')
def process_json_steps_page(file_id):
    """Page de traitement des étapes JSON"""
    # Trouver le fichier correspondant
    uploaded_files = [f for f in os.listdir(UPLOAD_FOLDER) if f.startswith(file_id)]
    if not uploaded_files:
        flash('Fichier non trouvé', 'error')
        return redirect(url_for('index'))

    filename = uploaded_files[0].replace(f"{file_id}_", "")
    return render_template('process_json_steps.html', file_id=file_id, filename=filename)

@app.route('/extract_json_steps/<file_id>', methods=['POST'])
def extract_json_steps(file_id):
    """Extrait les colonnes Action, Data, Expected Result depuis le JSON"""
    try:
        # Trouver le fichier original
        uploaded_files = [f for f in os.listdir(UPLOAD_FOLDER) if f.startswith(file_id)]
        if not uploaded_files:
            return jsonify({'success': False, 'message': 'Fichier non trouvé'})

        input_filepath = os.path.join(UPLOAD_FOLDER, uploaded_files[0])

        # Fichier de sortie avec étapes JSON extraites
        output_filename = f"with_steps_{file_id}.csv"
        output_filepath = os.path.join(UPLOAD_FOLDER, output_filename)

        # Traiter le fichier
        processor = JSONStepsProcessor()
        stats = processor.process_csv_file(input_filepath, output_filepath)

        return jsonify({
            'success': True,
            'output_file': output_filename,
            'download_path': f'/download/{output_filename}',
            'stats': stats,
            'message': f'Extraction terminée : {stats["extracted"]} tests avec étapes extraites'
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'Erreur lors de l\'extraction: {str(e)}'})

@app.route('/preview_json_steps/<file_id>')
def preview_json_steps(file_id):
    """Aperçu de l'extraction JSON"""
    try:
        # Trouver le fichier original
        uploaded_files = [f for f in os.listdir(UPLOAD_FOLDER) if f.startswith(file_id)]
        if not uploaded_files:
            return jsonify({'success': False, 'message': 'Fichier non trouvé'})

        input_filepath = os.path.join(UPLOAD_FOLDER, uploaded_files[0])

        # Générer l'aperçu
        processor = JSONStepsProcessor()
        samples = processor.preview_json_extraction(input_filepath, max_samples=5)

        return jsonify({
            'success': True,
            'samples': samples
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'Erreur lors de l\'aperçu: {str(e)}'})

@app.route('/process/<file_id>', methods=['POST'])
def process_file(file_id):
    """Traitement et enrichissement du fichier"""
    try:
        # Récupérer le format demandé
        format_type = 'standard'
        if request.is_json and request.json:
            format_type = request.json.get('format', 'standard')

        # Trouver le fichier d'entrée (priorité au fichier avec étapes JSON si disponible)
        with_steps_file = f"with_steps_{file_id}.csv"
        with_steps_path = os.path.join(UPLOAD_FOLDER, with_steps_file)

        if os.path.exists(with_steps_path):
            input_filepath = with_steps_path
        else:
            uploaded_files = [f for f in os.listdir(UPLOAD_FOLDER) if f.startswith(file_id)]
            if not uploaded_files:
                return jsonify({'success': False, 'message': 'Fichier non trouvé'})
            input_filepath = os.path.join(UPLOAD_FOLDER, uploaded_files[0])

        # Analyser la stratégie de traitement optimale
        strategy = optimize_processing_strategy(input_filepath)

        # Créer le fichier de sortie
        output_filename = f"enriched_{file_id}.csv"
        output_filepath = os.path.join(PROCESSED_FOLDER, output_filename)

        # Démarrer le traitement asynchrone avec le format
        job_id = async_processor.start_enrichment(file_id, input_filepath, output_filepath, format_type)

        return jsonify({
            'success': True,
            'message': f'Enrichissement démarré (format {format_type})',
            'job_id': job_id,
            'strategy': strategy['strategy'],
            'estimated_time': strategy['estimated_time'],
            'format': format_type
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erreur lors du démarrage de l\'enrichissement: {str(e)}'
        })

@app.route('/job_status/<file_id>')
def get_job_status(file_id):
    """Récupère le statut d'une tâche d'enrichissement"""
    try:
        status = async_processor.get_job_status(file_id)
        if status is None:
            return jsonify({'success': False, 'message': 'Tâche non trouvée'})

        return jsonify({
            'success': True,
            'status': status
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'Erreur: {str(e)}'})

@app.route('/process_sync/<file_id>', methods=['POST'])
def process_file_sync(file_id):
    """Version synchrone pour les petits fichiers"""
    try:
        # Forcer le format 'separate' pour TOUS les enrichissements
        # Cela garantit des colonnes Labels séparées dans tous les cas
        format_type = 'separate'
        print(f"🏷️ [DEBUG] Format 'separate' forcé pour colonnes Labels séparées identiques")
        print(f"🏷️ [DEBUG] File ID reçu: {file_id}")
        print(f"🏷️ [DEBUG] Request method: {request.method}")
        print(f"🏷️ [DEBUG] Request data: {request.get_json() if request.is_json else 'No JSON'}")

        # Trouver le fichier d'entrée (priorité au fichier avec étapes JSON si disponible)
        with_steps_file = f"with_steps_{file_id}.csv"
        with_steps_path = os.path.join(UPLOAD_FOLDER, with_steps_file)

        # Trouver le fichier d'entrée (priorité au fichier avec étapes JSON si disponible)
        print(f"🏷️ [DEBUG] Recherche fichier with_steps: {with_steps_path}")
        print(f"🏷️ [DEBUG] Fichier with_steps existe: {os.path.exists(with_steps_path)}")

        if os.path.exists(with_steps_path):
            input_filepath = with_steps_path
            print(f"📋 [DEBUG] Fichier avec étapes JSON détecté : {with_steps_file}")
            print(f"📋 [DEBUG] Chemin complet: {input_filepath}")
        else:
            # Chercher le fichier original
            original_file = f"{file_id}.csv"
            original_path = os.path.join(UPLOAD_FOLDER, original_file)
            print(f"🏷️ [DEBUG] Recherche fichier original: {original_path}")
            print(f"🏷️ [DEBUG] Fichier original existe: {os.path.exists(original_path)}")

            if os.path.exists(original_path):
                input_filepath = original_path
                print(f"📋 [DEBUG] Fichier original détecté : {original_file}")
            else:
                # Chercher n'importe quel fichier avec cet ID
                uploaded_files = [f for f in os.listdir(UPLOAD_FOLDER) if f.startswith(file_id)]
                print(f"🏷️ [DEBUG] Fichiers trouvés avec ID {file_id}: {uploaded_files}")
                if uploaded_files:
                    input_filepath = os.path.join(UPLOAD_FOLDER, uploaded_files[0])
                    print(f"📋 [DEBUG] Fichier trouvé : {uploaded_files[0]}")
                else:
                    print(f"❌ [DEBUG] Aucun fichier trouvé pour {file_id}")
                    return jsonify({'success': False, 'error': 'Fichier non trouvé'}), 404

        # Créer le fichier de sortie
        output_filename = f"enriched_{file_id}.csv"
        output_filepath = os.path.join(PROCESSED_FOLDER, output_filename)

        # Enrichir le fichier avec la version améliorée (correction intégrée)
        print(f"📋 [DEBUG] Enrichissement avec:")
        print(f"   Input: {input_filepath}")
        print(f"   Output: {output_filepath}")
        print(f"   Format: {format_type}")

        tool = TestEnrichmentToolEnhanced()
        tool.enrich_csv_enhanced(input_filepath, output_filepath, format_type)

        # Vérifier le résultat
        if os.path.exists(output_filepath):
            with open(output_filepath, 'r', encoding='utf-8') as f:
                header = f.readline().strip()
            labels_count = header.count('Labels')
            print(f"📋 [DEBUG] Fichier enrichi créé avec {labels_count} colonnes Labels")
            if labels_count == 1:
                print(f"⚠️ [DEBUG] PROBLÈME: Une seule colonne Labels au lieu de plusieurs!")
                print(f"⚠️ [DEBUG] En-tête: {header[:200]}...")
        else:
            print(f"❌ [DEBUG] Fichier enrichi non créé!")

        # Créer aussi un fichier Excel
        excel_filename = f"enriched_{file_id}.xlsx"
        excel_filepath = os.path.join(PROCESSED_FOLDER, excel_filename)

        # Import du script de création Excel
        from create_excel_output import create_excel_output
        create_excel_output(output_filepath, excel_filepath)

        return jsonify({
            'success': True,
            'message': f'Enrichissement terminé avec succès (format {format_type})',
            'csv_file': output_filename,
            'excel_file': excel_filename,
            'format': format_type
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erreur lors de l\'enrichissement: {str(e)}'
        })

@app.route('/download/<filename>')
def download_file(filename):
    """Téléchargement des fichiers traités"""
    try:
        # Chercher d'abord dans UPLOAD_FOLDER (fichiers avec étapes JSON)
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        if os.path.exists(filepath):
            return send_file(filepath, as_attachment=True)

        # Puis dans PROCESSED_FOLDER (fichiers enrichis)
        filepath = os.path.join(PROCESSED_FOLDER, filename)
        if os.path.exists(filepath):
            return send_file(filepath, as_attachment=True)

        flash('Fichier non trouvé', 'error')
        return redirect(url_for('index'))
    except Exception as e:
        flash(f'Erreur lors du téléchargement: {str(e)}', 'error')
        return redirect(url_for('index'))

@app.route('/preview/<file_id>')
def preview_file(file_id):
    """Aperçu du fichier enrichi"""
    try:
        output_filename = f"enriched_{file_id}.csv"
        output_filepath = os.path.join(PROCESSED_FOLDER, output_filename)
        
        if not os.path.exists(output_filepath):
            return jsonify({'success': False, 'message': 'Fichier enrichi non trouvé'})
        
        # Lire quelques lignes pour l'aperçu
        df = pd.read_csv(output_filepath)
        preview_data = df.head(10).to_dict('records')
        
        # Statistiques
        stats = {
            'total_tests': len(df),
            'components': df['Component/s'].value_counts().to_dict(),
            'functional_count': df['Labels'].str.contains('Functional', na=False).sum(),
            'non_functional_count': df['Labels'].str.contains('NonFunctional', na=False).sum()
        }
        
        return jsonify({
            'success': True,
            'preview': preview_data,
            'stats': stats,
            'columns': list(df.columns)
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'Erreur: {str(e)}'})

@app.route('/rules')
def show_rules():
    """Page des règles de labellisation"""
    return render_template('rules.html')

@app.route('/help')
def show_help():
    """Page d'aide"""
    return render_template('help.html')

@app.route('/dashboard')
def show_dashboard():
    """Page de dashboard avec statistiques"""
    return render_template('dashboard.html')

@app.route('/export_options/<file_id>')
def show_export_options(file_id):
    """Page d'options d'export avancées"""
    return render_template('export_options.html', file_id=file_id)

@app.route('/history')
def show_history():
    """Page d'historique des enrichissements"""
    try:
        # Lister les fichiers traités
        processed_files = []
        if os.path.exists(PROCESSED_FOLDER):
            for filename in os.listdir(PROCESSED_FOLDER):
                if filename.endswith('.csv'):
                    filepath = os.path.join(PROCESSED_FOLDER, filename)
                    file_stats = os.stat(filepath)

                    # Lire quelques informations du fichier
                    try:
                        df = pd.read_csv(filepath)
                        processed_files.append({
                            'filename': filename,
                            'size': file_stats.st_size,
                            'modified': file_stats.st_mtime,
                            'test_count': len(df),
                            'components': len(df['Component/s'].unique()) if 'Component/s' in df.columns else 0
                        })
                    except:
                        continue

        # Trier par date de modification (plus récent en premier)
        processed_files.sort(key=lambda x: x['modified'], reverse=True)

        return render_template('history.html', files=processed_files)
    except Exception as e:
        flash(f'Erreur lors du chargement de l\'historique: {str(e)}', 'error')
        return redirect(url_for('index'))

@app.route('/compare/<file_id>')
def compare_results(file_id):
    """Page de comparaison avant/après enrichissement"""
    try:
        # Trouver le fichier original
        uploaded_files = [f for f in os.listdir(UPLOAD_FOLDER) if f.startswith(file_id)]
        if not uploaded_files:
            flash('Fichier original non trouvé', 'error')
            return redirect(url_for('index'))

        original_filepath = os.path.join(UPLOAD_FOLDER, uploaded_files[0])
        enriched_filepath = os.path.join(PROCESSED_FOLDER, f"enriched_{file_id}.csv")

        if not os.path.exists(enriched_filepath):
            flash('Fichier enrichi non trouvé', 'error')
            return redirect(url_for('index'))

        # Lire les deux fichiers
        df_original = pd.read_csv(original_filepath)
        df_enriched = pd.read_csv(enriched_filepath)

        # Préparer les données de comparaison
        comparison_data = {
            'original': {
                'rows': len(df_original),
                'columns': len(df_original.columns),
                'columns_list': list(df_original.columns),
                'sample': df_original.head(5).to_dict('records')
            },
            'enriched': {
                'rows': len(df_enriched),
                'columns': len(df_enriched.columns),
                'columns_list': list(df_enriched.columns),
                'sample': df_enriched.head(5).to_dict('records')
            },
            'added_columns': [col for col in df_enriched.columns if col not in df_original.columns],
            'file_id': file_id
        }

        return render_template('compare.html', data=comparison_data)

    except Exception as e:
        flash(f'Erreur lors de la comparaison: {str(e)}', 'error')
        return redirect(url_for('index'))

@app.route('/delete/<filename>', methods=['POST'])
def delete_file(filename):
    """Supprimer un fichier traité"""
    try:
        # Vérifier que le fichier existe
        filepath = os.path.join(PROCESSED_FOLDER, filename)
        if not os.path.exists(filepath):
            return jsonify({'success': False, 'message': 'Fichier non trouvé'})

        # Supprimer le fichier CSV
        os.remove(filepath)

        # Supprimer le fichier Excel correspondant s'il existe
        excel_filename = filename.replace('.csv', '.xlsx')
        excel_filepath = os.path.join(PROCESSED_FOLDER, excel_filename)
        if os.path.exists(excel_filepath):
            os.remove(excel_filepath)

        return jsonify({'success': True, 'message': 'Fichier supprimé avec succès'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'Erreur lors de la suppression: {str(e)}'})

@app.route('/cleanup', methods=['POST'])
def cleanup_old_files():
    """Nettoyer les anciens fichiers (plus de 7 jours)"""
    try:
        import time
        current_time = time.time()
        seven_days = 7 * 24 * 60 * 60  # 7 jours en secondes

        deleted_count = 0

        # Nettoyer les uploads
        if os.path.exists(UPLOAD_FOLDER):
            for filename in os.listdir(UPLOAD_FOLDER):
                filepath = os.path.join(UPLOAD_FOLDER, filename)
                if os.path.isfile(filepath):
                    file_age = current_time - os.path.getmtime(filepath)
                    if file_age > seven_days:
                        os.remove(filepath)
                        deleted_count += 1

        # Nettoyer les fichiers traités anciens
        if os.path.exists(PROCESSED_FOLDER):
            for filename in os.listdir(PROCESSED_FOLDER):
                filepath = os.path.join(PROCESSED_FOLDER, filename)
                if os.path.isfile(filepath):
                    file_age = current_time - os.path.getmtime(filepath)
                    if file_age > seven_days:
                        os.remove(filepath)
                        deleted_count += 1

        return jsonify({
            'success': True,
            'message': f'{deleted_count} fichier(s) ancien(s) supprimé(s)'
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'Erreur lors du nettoyage: {str(e)}'})

@app.route('/export/<file_id>/<format_type>')
def export_file(file_id, format_type):
    """Exporte un fichier enrichi dans le format spécifié"""
    try:
        # Vérifier que le format est supporté
        if format_type not in export_manager.supported_formats:
            return jsonify({'success': False, 'message': f'Format non supporté: {format_type}'})

        # Trouver le fichier enrichi
        enriched_filename = f"enriched_{file_id}.csv"
        enriched_filepath = os.path.join(PROCESSED_FOLDER, enriched_filename)

        if not os.path.exists(enriched_filepath):
            return jsonify({'success': False, 'message': 'Fichier enrichi non trouvé'})

        # Lire le fichier
        df = pd.read_csv(enriched_filepath)

        # Créer le fichier d'export
        export_filename = f"enriched_{file_id}.{format_type}"
        export_filepath = os.path.join(PROCESSED_FOLDER, export_filename)

        # Options d'export selon le format
        options = {}
        if format_type == 'json':
            options = {'format': 'grouped_by_component', 'indent': 2}
        elif format_type == 'xml':
            options = {'include_metadata': True}

        # Exporter
        export_manager.export_to_format(df, export_filepath, format_type, options)

        # Télécharger le fichier
        return send_file(export_filepath, as_attachment=True)

    except Exception as e:
        return jsonify({'success': False, 'message': f'Erreur lors de l\'export: {str(e)}'})

@app.route('/export_jira/<file_id>')
def export_jira_format(file_id):
    """Exporte au format optimisé pour Jira Xray"""
    try:
        # Trouver le fichier enrichi
        enriched_filename = f"enriched_{file_id}.csv"
        enriched_filepath = os.path.join(PROCESSED_FOLDER, enriched_filename)

        if not os.path.exists(enriched_filepath):
            return jsonify({'success': False, 'message': 'Fichier enrichi non trouvé'})

        # Lire le fichier
        df = pd.read_csv(enriched_filepath)

        # Créer le fichier Jira
        jira_filename = f"jira_import_{file_id}.csv"
        jira_filepath = os.path.join(PROCESSED_FOLDER, jira_filename)

        # Créer le format Jira
        export_manager.create_jira_import_format(df, jira_filepath)

        # Télécharger le fichier
        return send_file(jira_filepath, as_attachment=True)

    except Exception as e:
        return jsonify({'success': False, 'message': f'Erreur lors de l\'export Jira: {str(e)}'})

@app.route('/comparison_report/<file_id>')
def generate_comparison_report(file_id):
    """Génère un rapport de comparaison avant/après"""
    try:
        # Trouver les fichiers
        uploaded_files = [f for f in os.listdir(UPLOAD_FOLDER) if f.startswith(file_id)]
        if not uploaded_files:
            return jsonify({'success': False, 'message': 'Fichier original non trouvé'})

        original_filepath = os.path.join(UPLOAD_FOLDER, uploaded_files[0])
        enriched_filepath = os.path.join(PROCESSED_FOLDER, f"enriched_{file_id}.csv")

        if not os.path.exists(enriched_filepath):
            return jsonify({'success': False, 'message': 'Fichier enrichi non trouvé'})

        # Lire les fichiers
        original_df = pd.read_csv(original_filepath)
        enriched_df = pd.read_csv(enriched_filepath)

        # Créer le rapport
        report_filename = f"comparison_report_{file_id}.json"
        report_filepath = os.path.join(PROCESSED_FOLDER, report_filename)

        export_manager.create_comparison_report(original_df, enriched_df, report_filepath)

        # Télécharger le rapport
        return send_file(report_filepath, as_attachment=True)

    except Exception as e:
        return jsonify({'success': False, 'message': f'Erreur lors de la génération du rapport: {str(e)}'})

# Route pour générer le fichier CSV des Test Sets
@app.route('/generate_test_sets_csv')
def generate_test_sets_csv():
    """Génère un fichier CSV pour import des Test Sets"""
    try:
        generator = TestSetCSVGenerator()

        # Générer un nom de fichier unique
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_sets_import_{timestamp}.csv"
        filepath = os.path.join(PROCESSED_FOLDER, filename)

        # Générer le fichier
        generator.generate_test_sets_csv(filepath)

        # Obtenir les statistiques
        stats = generator.get_test_sets_statistics()

        return jsonify({
            'success': True,
            'filename': filename,
            'filepath': filepath,
            'stats': stats,
            'message': f'Fichier CSV généré avec {stats["total_test_sets"]} Test Sets'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erreur lors de la génération: {str(e)}'
        })

# Routes d'analyse supprimées - correction intégrée directement dans l'enrichissement

@app.route('/api/stats')
def api_stats():
    """API pour obtenir les statistiques globales"""
    try:
        stats = {
            'total_files_processed': 0,
            'total_tests_enriched': 0,
            'most_common_components': {},
            'processing_history': []
        }

        if os.path.exists(PROCESSED_FOLDER):
            for filename in os.listdir(PROCESSED_FOLDER):
                if filename.endswith('.csv'):
                    try:
                        filepath = os.path.join(PROCESSED_FOLDER, filename)
                        df = pd.read_csv(filepath)

                        stats['total_files_processed'] += 1
                        stats['total_tests_enriched'] += len(df)

                        # Compter les composants
                        if 'Component/s' in df.columns:
                            components = df['Component/s'].value_counts().to_dict()
                            for comp, count in components.items():
                                stats['most_common_components'][comp] = stats['most_common_components'].get(comp, 0) + count

                        # Historique
                        file_stats = os.stat(filepath)
                        stats['processing_history'].append({
                            'filename': filename,
                            'test_count': len(df),
                            'timestamp': file_stats.st_mtime
                        })
                    except:
                        continue

        # Trier l'historique
        stats['processing_history'].sort(key=lambda x: x['timestamp'], reverse=True)
        stats['processing_history'] = stats['processing_history'][:10]  # Garder les 10 derniers

        return jsonify(stats)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
